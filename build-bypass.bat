@echo off
echo === Zencoder 认证绕过插件构建脚本 ===

REM 设置临时 JAVA_HOME
set TEMP_JAVA_HOME=C:\Program Files\Common Files\Oracle\Java\javapath\..

REM 尝试多个可能的 Java 路径
if exist "C:\Program Files\Java\jdk-21" (
    set JAVA_HOME=C:\Program Files\Java\jdk-21
    echo 找到 Java 21 JDK
) else if exist "C:\Program Files\Java\jdk-17" (
    set JAVA_HOME=C:\Program Files\Java\jdk-17
    echo 找到 Java 17 JDK
) else if exist "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot" (
    set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
    echo 找到 Eclipse Adoptium JDK 21
) else if exist "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot" (
    set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
    echo 找到 Eclipse Adoptium JDK 17
) else (
    echo 警告: 未找到合适的 JDK，使用默认路径
    set JAVA_HOME=C:\Program Files\Common Files\Oracle\Java\javapath\..
)

echo 使用 JAVA_HOME: %JAVA_HOME%

REM 检查 Java 版本
echo 检查 Java 版本...
java -version

REM 尝试构建
echo 开始构建插件...
gradlew.bat clean build --no-daemon --stacktrace

if %ERRORLEVEL% EQU 0 (
    echo ✅ 构建成功！
    echo 生成的插件文件位于: build\distributions\
    dir build\distributions\*.zip
) else (
    echo ❌ 构建失败，错误代码: %ERRORLEVEL%
    echo 尝试手动编译核心类...
    
    REM 手动编译核心类
    echo 手动编译核心类...
    mkdir build\manual 2>nul
    
    javac -cp "src\main\java" -d "build\manual" src\main\java\ai\zencoder\plugin\bypass\*.java
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ 手动编译成功！
        echo 可以手动创建插件包
    ) else (
        echo ❌ 手动编译也失败
    )
)

pause
