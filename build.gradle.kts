plugins {
    id("java")
    id("org.jetbrains.kotlin.jvm") version "2.1.0"
    id("org.jetbrains.intellij.platform") version "2.5.0"
}

group = "ai.zencoder.plugin.bypass"
version = "1.0.0"

repositories {
    mavenCentral()
    intellijPlatform {
        defaultRepositories()
    }
}

dependencies {
    intellijPlatform {
        create("IC", "2024.2.4")
        testFramework(org.jetbrains.intellij.platform.gradle.TestFrameworkType.Platform)
    }

    implementation("org.jetbrains.kotlin:kotlin-stdlib")
    implementation("com.squareup.okhttp3:okhttp:4.11.0")
    implementation("com.google.code.gson:gson:2.10.1")

    testImplementation("org.junit.jupiter:junit-jupiter-api:5.9.2")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine:5.9.2")
    testImplementation("org.mockito:mockito-core:5.1.1")
    testImplementation("org.mockito:mockito-kotlin:4.1.0")
}

// Configure IntelliJ Platform Plugin
intellijPlatform {
    pluginConfiguration {
        ideaVersion {
            sinceBuild = "233"
            untilBuild = "243.*"
        }
    }
}

tasks {
    // Set the JVM compatibility versions
    withType<JavaCompile> {
        sourceCompatibility = "17"
        targetCompatibility = "17"
        options.encoding = "UTF-8"
    }
    
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        kotlinOptions.jvmTarget = "17"
    }

    patchPluginXml {
        sinceBuild.set("233")
        untilBuild.set("243.*")
        
        // 从plugin.xml读取描述
        pluginDescription.set(
            """
            <h1>Zencoder Authentication Bypass</h1>
            <p>Advanced plugin that bypasses Zencoder authentication using sophisticated reflection and service hijacking techniques.</p>
            
            <h2>Features</h2>
            <ul>
                <li>Automatic authentication bypass with reflection technology</li>
                <li>Service hijacking and credential injection</li>
                <li>Real-time state synchronization</li>
                <li>Target discovery for different Zencoder versions</li>
                <li>Comprehensive configuration management</li>
                <li>Status monitoring and recovery mechanisms</li>
                <li>User-friendly management interface</li>
            </ul>
            
            <h2>Security Notice</h2>
            <p>This plugin is intended for development and testing purposes only.</p>
            """.trimIndent()
        )
        
        changeNotes.set(
            """
            <h3>Version 1.0.0</h3>
            <ul>
                <li>Initial release with complete bypass architecture</li>
                <li>Advanced reflection utilities with TTL caching</li>
                <li>Dynamic service hijacking with proxy technology</li>
                <li>Automatic target discovery and adaptation</li>
                <li>Real-time state synchronization and recovery</li>
                <li>Comprehensive configuration management</li>
                <li>User-friendly management interface</li>
                <li>Status bar integration for quick access</li>
            </ul>
            """.trimIndent()
        )
    }

    signPlugin {
        certificateChain.set(System.getenv("CERTIFICATE_CHAIN"))
        privateKey.set(System.getenv("PRIVATE_KEY"))
        password.set(System.getenv("PRIVATE_KEY_PASSWORD"))
    }

    publishPlugin {
        token.set(System.getenv("PUBLISH_TOKEN"))
    }
    
    test {
        useJUnitPlatform()
    }
    
    // 自定义任务：验证插件结构
    register("validatePlugin") {
        group = "verification"
        description = "Validate plugin structure and dependencies"
        
        doLast {
            println("Validating Zencoder Bypass Plugin...")
            
            // 检查核心类是否存在
            val coreClasses = listOf(
                "ai.zencoder.plugin.bypass.ZencoderReflectionUtils",
                "ai.zencoder.plugin.bypass.ZencoderCredentialInjector",
                "ai.zencoder.plugin.bypass.ZencoderServiceHijacker",
                "ai.zencoder.plugin.bypass.ZencoderTargetDiscovery",
                "ai.zencoder.plugin.bypass.ZencoderStateSynchronizer",
                "ai.zencoder.plugin.bypass.ZencoderBypassInitializer",
                "ai.zencoder.plugin.config.ZencoderConfig"
            )
            
            val srcDir = file("src/main/java")
            var allClassesFound = true
            
            coreClasses.forEach { className ->
                val classFile = file("${srcDir}/${className.replace('.', '/')}.java")
                if (classFile.exists()) {
                    println("✓ Found: $className")
                } else {
                    println("✗ Missing: $className")
                    allClassesFound = false
                }
            }
            
            if (allClassesFound) {
                println("✓ All core classes found")
            } else {
                throw GradleException("Some core classes are missing")
            }
            
            // 检查plugin.xml
            val pluginXml = file("src/main/resources/META-INF/plugin.xml")
            if (pluginXml.exists()) {
                println("✓ plugin.xml found")
            } else {
                throw GradleException("plugin.xml not found")
            }
            
            println("Plugin validation completed successfully!")
        }
    }
    
    // 自定义任务：生成文档
    register("generateDocs") {
        group = "documentation"
        description = "Generate plugin documentation"
        
        doLast {
            val docsDir = file("docs")
            docsDir.mkdirs()
            
            val readmeContent = """
# Zencoder Authentication Bypass Plugin

## 概述

这是一个高级的IntelliJ IDEA插件，通过复杂的反射技术和服务劫持机制来绕过Zencoder官方插件的认证系统。

## 核心架构

### 1. 反射绕过层 (ZencoderReflectionUtils)
- 高级反射操作，支持TTL缓存
- 绕过final字段限制
- 使用Unsafe类进行底层内存操作
- 动态类发现和方法调用

### 2. 凭证注入器 (ZencoderCredentialInjector)
- 创建伪造的AuthInfo和UserData对象
- 注入虚假认证信息到官方插件
- 支持自定义用户数据和令牌

### 3. 服务劫持器 (ZencoderServiceHijacker)
- 使用动态代理技术拦截服务调用
- 替换官方认证逻辑
- 支持多种服务的同时劫持

### 4. 目标发现器 (ZencoderTargetDiscovery)
- 自动发现Zencoder插件的关键类
- 适应不同版本的API变化
- 缓存发现结果以提高性能

### 5. 状态同步器 (ZencoderStateSynchronizer)
- 实时监控绕过状态
- 自动恢复机制
- 定期同步和快速检查

### 6. 初始化器 (ZencoderBypassInitializer)
- 项目启动时自动初始化
- 支持重试机制
- 完整的错误处理

### 7. 配置管理 (ZencoderConfig)
- 持久化配置存储
- 丰富的配置选项
- 运行时配置更新

## 使用方法

1. 安装插件到IntelliJ IDEA
2. 确保Zencoder官方插件已安装
3. 通过Tools菜单或状态栏访问绕过管理器
4. 配置绕过选项并激活

## 安全注意事项

此插件仅用于开发和测试目的。请勿在生产环境中使用。

## 技术特点

- 使用最新的Java反射技术
- 动态代理和服务注入
- 异步处理和错误恢复
- 用户友好的管理界面
- 完整的日志和监控

## 构建和部署

```bash
# 构建插件
./gradlew buildPlugin

# 验证插件
./gradlew validatePlugin

# 运行测试
./gradlew test

# 生成文档
./gradlew generateDocs
```

## 版本历史

### 1.0.0
- 初始版本发布
- 完整的绕过架构实现
- 所有核心组件完成
- 用户界面和配置管理
""".trimIndent()
            
            file("${docsDir}/README.md").writeText(readmeContent)
            println("Documentation generated in docs/README.md")
        }
    }
    
    // 构建前验证
    build {
        dependsOn("validatePlugin")
    }
}

// Java工具链配置
java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}
