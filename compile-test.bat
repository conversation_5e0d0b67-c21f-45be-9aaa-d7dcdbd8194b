@echo off
echo === Zencoder 认证绕过编译测试 ===

REM 创建输出目录
mkdir build\classes 2>nul

REM 设置类路径
set CLASSPATH=src\main\java

echo 正在编译核心类...

REM 编译基础类
javac -cp %CLASSPATH% -d build\classes src\main\java\ai\zencoder\plugin\auth\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 基础类编译失败
    goto :error
)
echo ✅ 基础类编译成功

REM 编译观察者类
javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\ai\zencoder\plugin\observers\auth\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 观察者类编译失败
    goto :error
)
echo ✅ 观察者类编译成功

REM 编译配置类
javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\ai\zencoder\plugin\config\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 配置类编译失败
    goto :error
)
echo ✅ 配置类编译成功

REM 编译 IntelliJ 模拟类
javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\com\intellij\openapi\application\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ IntelliJ 应用类编译失败
    goto :error
)

javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\com\intellij\openapi\diagnostic\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ IntelliJ 诊断类编译失败
    goto :error
)

javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\com\intellij\openapi\project\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ IntelliJ 项目类编译失败
    goto :error
)

REM Skip IntelliJ components compilation - files removed
echo ✅ IntelliJ 模拟类编译成功

REM 编译绕过核心类
javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\ai\zencoder\plugin\bypass\FakeDataGenerator.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ FakeDataGenerator 编译失败
    goto :error
)
echo ✅ FakeDataGenerator 编译成功

javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\ai\zencoder\plugin\bypass\AdvancedReflectionUtils.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ AdvancedReflectionUtils 编译失败
    goto :error
)
echo ✅ AdvancedReflectionUtils 编译成功

javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\ai\zencoder\plugin\bypass\ReflectionBypassUtils.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ ReflectionBypassUtils 编译失败
    goto :error
)
echo ✅ ReflectionBypassUtils 编译成功

javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\ai\zencoder\plugin\bypass\BypassAuthServiceProxy.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ BypassAuthServiceProxy 编译失败
    goto :error
)
echo ✅ BypassAuthServiceProxy 编译成功

javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\ai\zencoder\plugin\bypass\SmartAuthStateManager.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ SmartAuthStateManager 编译失败
    goto :error
)
echo ✅ SmartAuthStateManager 编译成功

javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\ai\zencoder\plugin\bypass\BypassStatusMonitor.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ BypassStatusMonitor 编译失败
    goto :error
)
echo ✅ BypassStatusMonitor 编译成功

javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\ai\zencoder\plugin\bypass\ZencoderAuthBypass.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ ZencoderAuthBypass 编译失败
    goto :error
)
echo ✅ ZencoderAuthBypass 编译成功

javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\ai\zencoder\plugin\bypass\ZencoderBypassPlugin.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ ZencoderBypassPlugin 编译失败
    goto :error
)
echo ✅ ZencoderBypassPlugin 编译成功

javac -cp %CLASSPATH%;build\classes -d build\classes src\main\java\ai\zencoder\plugin\bypass\BypassTester.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ BypassTester 编译失败
    goto :error
)
echo ✅ BypassTester 编译成功

echo.
echo 🎉 所有核心类编译成功！
echo.
echo 运行简单测试...
java -cp build\classes ai.zencoder.plugin.bypass.SimpleBypassTest

echo.
echo === 编译测试完成 ===
goto :end

:error
echo.
echo ❌ 编译过程中出现错误
echo 请检查上面的错误信息

:end
pause
