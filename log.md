日志1：
行 45478: 2025-09-01 11:22:00,279 [ 225517]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 45479: 2025-09-01 11:22:01,337 [ 226575]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 45482: 2025-09-01 11:23:32,208 [ 317446]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 45487: 2025-09-01 11:24:19,263 [ 364501]   INFO - #c.i.i.p.DynamicPlugins - Plugin ai.zencoder.plugin.bypass is not unload-safe because it declares components
行 45508: 2025-09-01 11:24:36,815 [ 382053]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server closed
行 45509: 2025-09-01 11:24:36,818 [ 382056]   INFO - #ai.zencoder.plugin.webview.WebviewWrapper - Disposing webview
行 45510: 2025-09-01 11:24:36,818 [ 382056]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - Disposing DeltaBasedHistoryService for project: augment-plugin
行 45511: 2025-09-01 11:24:36,819 [ 382057]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.ThreadSafeDocumentChangeTracker - Disposing DocumentChangeTracker
行 46074: 2025-09-01 11:24:43,433 [   3229]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - DeltaBasedHistoryService initialized for project: augment-plugin
行 46105: 2025-09-01 11:24:55,415 [  15211]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46106: 2025-09-01 11:24:55,449 [  15245]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46107: 2025-09-01 11:24:55,467 [  15263]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46108: 2025-09-01 11:24:55,468 [  15264]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46109: 2025-09-01 11:24:55,485 [  15281]   INFO - #ai.zencoder.plugin.chat.agentmodels.AgentModelsManager - Fetching agent models config
行 46117: 2025-09-01 11:24:56,055 [  15851]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46118: 2025-09-01 11:24:56,056 [  15852]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46119: 2025-09-01 11:24:56,056 [  15852]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46120: 2025-09-01 11:24:56,056 [  15852]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46121: 2025-09-01 11:24:56,056 [  15852]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46122: 2025-09-01 11:24:56,060 [  15856]   INFO - #ai.zencoder.plugin.flags.FeatureFlagsService - Refreshing feature flags
行 46123: 2025-09-01 11:24:56,060 [  15856]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46147: 2025-09-01 11:24:57,016 [  16812]   INFO - #ai.zencoder.plugin.flags.FeatureFlagsService - New flags: {enable-web-dev-agent=FeatureFlagVariant(value=on, payload=null), use-zencoder-cli-agent-runtime=FeatureFlagVariant(value=null, payload=null), enable-new-chat-manager=FeatureFlagVariant(value=on, payload=null), enable-private-deployment-mvp=FeatureFlagVariant(value=null, payload=null), plugin-banners=FeatureFlagVariant(value=on, payload=[{"button":{"label":"Learn more","type":"external_link","url":"https://docs.zencoder.ai/features/web-dev-agent"},"endDate":"2025-08-26T00:00:00Z","icon":{"attributes":{"fill":"none","height":"13","viewBox":"0 0 14 13","width":"14","xmlns":"http://www.w3.org/2000/svg"},"children":[{"attributes":{"d":"M12.407 0.590089C12.3615 0.39829 12.2423 0.232113 12.0753 0.127506C11.9082 0.0228982 11.7066 -0.0117285 11.5142 0.0311165C11.3218 0.0739615 11.154 0.190833 11.0471 0.356449C10.9402 0.522065 10.9028 0.723109 10.943 0.916089C11.308 2.55209 11.5 4.25309 11.5 6.00009C11.5 7.74709 11.308 9.44809 10.943 11.0841C10.9215 11.1802 10.9192 11.2796 10.9362 11.3767C10.9531 11.4737 10.989 11.5664 11.0418 11.6496C11.0946 11.7327 11.1632 11.8047 11.2438 11.8613C11.3244 11.9179 11.4154 11.9581 11.5115 11.9796C11.6076 12.0011 11.707 12.0034 11.8041 11.9864C11.9011 11.9695 11.9938 11.9336 12.077 11.8808C12.1601 11.828 12.2321 11.7594 12.2887 11.6788C12.3453 11.5982 12.3855 11.5072 12.407 11.4111C12.671 10.2261 12.851 9.00909 12.938 7.76709C13.259 7.5969 13.5276 7.34249 13.7149 7.03118C13.9022 6.71986 14.0011 6.36341 14.0011 6.00009C14.0011 5.63677 13.9022 5.28032 13.7149 4.969C13.5276 4.65769 13.259 4.40328 12.938 4.23309C12.8517 3.00725 12.6742 1.78955 12.407 0.590089ZM3.348 9.00009H3C2.20435 9.00009 1.44129 8.68402 0.87868 8.12141C0.31607 7.5588 0 6.79574 0 6.00009C0 5.20444 0.31607 4.44138 0.87868 3.87877C1.44129 3.31616 2.20435 3.00009 3 3.00009H5C6.647 3.00009 8.217 2.66809 9.646 2.06709C9.878 3.34109 10 4.65509 10 6.00009C10 7.34509 9.878 8.65909 9.646 9.93309C8.30412 9.36966 6.8 ...
行 46184: 2025-09-01 11:24:57,354 [  17150]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46185: 2025-09-01 11:24:57,377 [  17173]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - Starting MCP MessageBus server...
行 46186: 2025-09-01 11:24:57,379 [  17175]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46187: 2025-09-01 11:24:57,392 [  17188]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46190: 2025-09-01 11:24:57,393 [  17189]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46191: 2025-09-01 11:24:57,403 [  17199]   INFO - #ai.zencoder.plugin.agents.CustomAgentsManager - Custom agents manager has been loaded
行 46192: 2025-09-01 11:24:57,403 [  17199]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 46213: 2025-09-01 11:24:57,657 [  17453]   INFO - STDOUT - 11:24:57,586 |-INFO in ch.qos.logback.core.model.processor.ConversionRuleModelHandler - registering conversion word tid with class [ai.zencoder.plugin.logging.ThreadIdConverter]
行 46248: 2025-09-01 11:24:57,718 [  17514]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registering annotated MCP handlers...
行 46249: 2025-09-01 11:24:57,718 [  17514]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Running on Windows - using enhanced classpath scanning configuration
行 46250: 2025-09-01 11:24:57,718 [  17514]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @Service
行 46267: 2025-09-01 11:24:58,135 [  17931]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 classes annotated with @Service
行 46268: 2025-09-01 11:24:58,135 [  17931]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @McpTool
行 46269: 2025-09-01 11:24:58,322 [  18118]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 0 classes annotated with @McpTool
行 46270: 2025-09-01 11:24:58,322 [  18118]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Adding explicitly registered service classes
行 46271: 2025-09-01 11:24:58,322 [  18118]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Added 0 explicitly registered service classes
行 46272: 2025-09-01 11:24:58,322 [  18118]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 total service classes:
行 46275: 2025-09-01 11:24:58,596 [  18392]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: list_resources
行 46277: 2025-09-01 11:24:58,596 [  18392]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: read_resource
行 46281: 2025-09-01 11:24:58,789 [  18585]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: file_search
行 46284: 2025-09-01 11:24:58,798 [  18594]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: fulltext_search
行 46285: 2025-09-01 11:24:58,857 [  18653]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Annotated MCP handlers registered successfully
行 46286: 2025-09-01 11:24:58,858 [  18654]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server started successfully
行 46314: 2025-09-01 11:25:00,489 [  20285]   WARN - #ai.zencoder.plugin.chat.agentmodels.AgentModelsManager - Using default models configuration due to fetch failure




2025-09-01 11:25:00,489 [  20285]   WARN - #ai.zencoder.plugin.chat.agentmodels.AgentModelsManager - Using default models configuration due to fetch failure
2025-09-01 11:25:00,554 [  20350]   INFO - #c.i.p.m.ProjectDependenciesModuleAggregator - Supported models after update: [IntellijProjectDependenciesModel, GradleProjectDependenciesModel]
2025-09-01 11:25:00,641 [  20437]   INFO - #c.i.p.m.i.ProjectDependenciesModelImpl - Count of declared packages: 4, imported packages: 16
2025-09-01 11:25:02,874 [  22670]   INFO - #c.i.DynamicBundle - Cleanup bundle cache for messages.JavaCompilerBundle
2025-09-01 11:25:02,878 [  22674]   INFO - #o.j.j.b.i.CompilerReferenceIndex - backward reference index version doesn't exist
2025-09-01 11:25:02,879 [  22675]   INFO - #c.i.DynamicBundle - Cleanup bundle cache for messages.IdeCoreBundle
2025-09-01 11:25:02,882 [  22678]   INFO - #c.i.c.b.IsUpToDateCheckStartupActivity - suitable consumer is not found
2025-09-01 11:25:02,890 [  22686]   INFO - #c.j.p.p.PyPIPackageUtil - Fetching index of all packages available on https://pypi.org/simple/
2025-09-01 11:25:02,890 [  22686]   INFO - #c.i.d.WindowsDefenderCheckerActivity - real-time protection: false
2025-09-01 11:25:02,924 [  22720]   INFO - #kotlinx.coroutines.CoroutineScope - Skipping Tip-Of-The-Day because there are suggestion notifications shown
2025-09-01 11:25:02,970 [  22766]   INFO - #c.i.w.i.i.GlobalWorkspaceModel - Sync global entities with mutable entity storage
2025-09-01 11:25:02,985 [  22781]   INFO - #c.i.w.i.i.j.s.JpsProjectModelSynchronizer - Attempt 1: Apply JPS storage (iml files)
2025-09-01 11:25:02,998 [  22794]   INFO - #c.i.w.i.i.EntitiesOrphanageImpl - Update orphanage. 0 modules added
2025-09-01 11:25:03,002 [  22798]   INFO - #c.i.w.i.i.j.s.JpsProjectModelSynchronizer - Attempt 1: Changes were successfully applied
2025-09-01 11:25:03,006 [  22802]   INFO - #c.i.w.i.i.j.s.DelayedProjectSynchronizer$Util - Workspace model loaded from cache. Syncing real project state into workspace model in 136 ms. Thread[#194,DefaultDispatcher-worker-84,5,main]
2025-09-01 11:25:03,089 [  22885]   WARN - #o.j.i.d.d.DocumentationContentProvider - Could not download documentation content from https://jb.gg/sdk-docs/plugin-descriptor.yaml
javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
at java.base/sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1714)
at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1514)
at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:455)
at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:426)
at java.base/sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:586)
at java.base/sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:187)
at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1690)
at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1614)
at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:531)
at java.base/sun.net.www.protocol.https.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:307)
at com.intellij.util.io.HttpRequests.openConnection(HttpRequests.java:626)
at com.intellij.util.io.HttpRequests$RequestImpl.getConnection(HttpRequests.java:366)
at com.intellij.util.io.HttpRequests$RequestImpl.doReadBytes(HttpRequests.java:442)
at com.intellij.util.io.HttpRequests$RequestImpl.readString(HttpRequests.java:431)
at com.intellij.util.io.RequestBuilder.lambda$readString$5(RequestBuilder.java:85)
at com.intellij.util.io.HttpRequests.doProcess(HttpRequests.java:529)
at com.intellij.util.io.HttpRequests.process(HttpRequests.java:511)
at com.intellij.util.io.HttpRequests$RequestBuilderImpl.connect(HttpRequests.java:340)
at com.intellij.util.io.RequestBuilder.readString(RequestBuilder.java:85)
at com.intellij.util.io.RequestBuilder.readString(RequestBuilder.java:89)
at org.jetbrains.idea.devkit.documentation.DocumentationContentProvider$downloadContentAsync$1.invokeSuspend(DocumentationContentProvider.kt:87)
at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
at kotlinx.coroutines.internal.SoftLimitedDispatcher$Worker.run(SoftLimitedDispatcher.kt:125)
at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:608)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:873)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:763)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:750)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:494)
at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:483)
at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1506)
... 28 more
2025-09-01 11:25:03,112 [  22908]   INFO - #c.j.p.p.p.PypiPackageCache - Package list loaded from file with 672750 entries
2025-09-01 11:25:32,908 [  52704]   INFO - #c.j.r.p.c.CodeWithMeCleanup - running activity to cleanup old thin clients... Root path is 'C:\Users\<USER>\AppData\Local\JetBrains'. JvmStartTimeMs=1756697079757
2025-09-01 11:25:32,908 [  52704]   INFO - #c.j.r.p.c.CodeWithMeCleanup - found 0 Code With Me client system folders to check
2025-09-01 11:25:32,915 [  52711]   INFO - #c.j.r.p.c.CodeWithMeCleanup - found 0 Code With Me client config folders to check
2025-09-01 11:25:32,915 [  52711]   INFO - #c.j.r.p.c.CodeWithMeCleanup - keep only [] major versions
2025-09-01 11:25:32,915 [  52711]   INFO - #c.j.r.p.c.CodeWithMeCleanup - Starting the logs folder cleanup. Path: C:\Users\<USER>\AppData\Local\JetBrains\IdeaIC2025.1\log
2025-09-01 11:25:32,916 [  52712]   INFO - #c.i.r.OsRegistryConfigProvider - Looking for 'versionManagementEnabled' value in [HKLM_64\SOFTWARE\JetBrains\JetBrainsClient\versionManagementEnabled, HKLM_32\SOFTWARE\JetBrains\JetBrainsClient\versionManagementEnabled, HKCU_64\SOFTWARE\JetBrains\JetBrainsClient\versionManagementEnabled, HKCU_32\SOFTWARE\JetBrains\JetBrainsClient\versionManagementEnabled]
2025-09-01 11:25:32,916 [  52712]   INFO - #c.i.r.OsRegistryConfigProvider - OS provided value for 'versionManagementEnabled' is not found
2025-09-01 11:25:40,642 [  60438]   INFO - #c.i.DynamicBundle - Cleanup bundle cache for messages.FindBundle
2025-09-01 11:25:41,848 [  61644]   INFO - #c.i.u.g.s.GistStorageImpl - Cleaning old huge-gists dirs from [C:\Users\<USER>\AppData\Local\JetBrains\IdeaIC2025.1\caches\huge-gists] ...
2025-09-01 11:25:41,851 [  61647]   INFO - #c.i.u.g.s.GistStorageImpl - Cleaning old huge-gists dirs finished
2025-09-01 11:25:57,271 [  77067]   INFO - #git4idea.commands.GitHandler - [.] git version
2025-09-01 11:25:57,329 [  77125]   INFO - #git4idea.commands.GitHandler - git version 2.45.2.windows.1
2025-09-01 11:25:57,337 [  77133]   INFO - #git4idea.config.GitExecutableFileTester - Git version for D:\git\Git\cmd\git.exe: 2.45.2.0 (MSYS)
2025-09-01 11:28:15,224 [ 215020] SEVERE - #c.i.o.a.i.ActionManagerImpl - class with name 'ai.zencoder.plugin.bypass.actions.ShowBypassStatusAction' must be an instance of 'com.intellij.openapi.actionSystem.AnAction'; got ai.zencoder.plugin.bypass.actions.ShowBypassStatusAction@499dacb7 [Plugin: ai.zencoder.plugin.bypass]
com.intellij.diagnostic.PluginException: class with name 'ai.zencoder.plugin.bypass.actions.ShowBypassStatusAction' must be an instance of 'com.intellij.openapi.actionSystem.AnAction'; got ai.zencoder.plugin.bypass.actions.ShowBypassStatusAction@499dacb7 [Plugin: ai.zencoder.plugin.bypass]
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.instantiate(ActionManagerImpl.kt:1468)
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.convertStub(ActionManagerImpl.kt:1694)
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.getAction(ActionManagerImpl.kt:2191)
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.access$getAction(ActionManagerImpl.kt:1)
at com.intellij.openapi.actionSystem.impl.ActionManagerImpl.getAction(ActionManagerImpl.kt:402)
at com.intellij.openapi.actionSystem.DefaultActionGroup.getChildren(DefaultActionGroup.java:402)
at com.intellij.openapi.actionSystem.DefaultActionGroup.getChildren(DefaultActionGroup.java:368)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1$1.invoke(ActionUpdater.kt:337)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1$1.invoke(ActionUpdater.kt:336)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$2$1.invoke(ActionUpdater.kt:146)
at com.intellij.openapi.application.rw.InternalReadAction.insideReadAction(InternalReadAction.kt:120)
at com.intellij.openapi.application.rw.InternalReadAction.tryReadCancellable$lambda$6(InternalReadAction.kt:110)
at com.intellij.openapi.application.rw.CancellableReadActionKt.cancellableReadActionInternal$lambda$3$lambda$2$lambda$1(cancellableReadAction.kt:32)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.tryRunReadAction$lambda$11(AnyThreadWriteThreadingSupport.kt:522)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWithTemporaryThreadLocal(AnyThreadWriteThreadingSupport.kt:204)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.tryRunReadAction(AnyThreadWriteThreadingSupport.kt:522)
at com.intellij.openapi.application.impl.ApplicationImpl.tryRunReadAction(ApplicationImpl.java:1064)
at com.intellij.openapi.application.rw.CancellableReadActionKt.cancellableReadActionInternal$lambda$3$lambda$2(cancellableReadAction.kt:30)
at com.intellij.openapi.progress.util.ProgressIndicatorUtilService.runActionAndCancelBeforeWrite(ProgressIndicatorUtilService.java:73)
at com.intellij.openapi.progress.util.ProgressIndicatorUtils.runActionAndCancelBeforeWrite(ProgressIndicatorUtils.java:152)
at com.intellij.openapi.application.rw.CancellableReadActionKt.cancellableReadActionInternal(cancellableReadAction.kt:28)
at com.intellij.openapi.application.rw.InternalReadAction.tryReadCancellable(InternalReadAction.kt:109)
at com.intellij.openapi.application.rw.InternalReadAction.tryReadAction(InternalReadAction.kt:93)
at com.intellij.openapi.application.rw.InternalReadAction.readLoop(InternalReadAction.kt:80)
at com.intellij.openapi.application.rw.InternalReadAction.access$readLoop(InternalReadAction.kt:17)
at com.intellij.openapi.application.rw.InternalReadAction$runReadAction$4.invokeSuspend(InternalReadAction.kt:40)
at com.intellij.openapi.application.rw.InternalReadAction$runReadAction$4.invoke(InternalReadAction.kt)
at com.intellij.openapi.application.rw.InternalReadAction$runReadAction$4.invoke(InternalReadAction.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
at com.intellij.openapi.application.rw.InternalReadAction.runReadAction(InternalReadAction.kt:39)
at com.intellij.openapi.application.rw.PlatformReadWriteActionSupport.executeReadAction(PlatformReadWriteActionSupport.kt:38)
at com.intellij.openapi.application.ReadWriteActionSupport.executeReadAction$default(ReadWriteActionSupport.kt:15)
at com.intellij.openapi.application.CoroutinesKt.constrainedReadActionUndispatched(coroutines.kt:86)
at com.intellij.openapi.application.CoroutinesKt.readActionUndispatched(coroutines.kt:73)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$$inlined$useWithScope$1.invokeSuspend(trace.kt:59)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$$inlined$useWithScope$1.invoke(trace.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$$inlined$useWithScope$1.invoke(trace.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.callAction(ActionUpdater.kt:897)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.access$callAction(ActionUpdater.kt:78)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1.invokeSuspend(ActionUpdater.kt:336)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$$inlined$retryOnAwaitSharedData$1.invokeSuspend(ActionUpdater.kt:846)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$$inlined$retryOnAwaitSharedData$1.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$$inlined$retryOnAwaitSharedData$1.invoke(ActionUpdater.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.getGroupChildren(ActionUpdater.kt:928)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.access$getGroupChildren(ActionUpdater.kt:78)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2.invokeSuspend(ActionUpdater.kt:269)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2.invoke(ActionUpdater.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.doExpandActionGroup(ActionUpdater.kt:262)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.expandGroupChild(ActionUpdater.kt:410)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.access$expandGroupChild(ActionUpdater.kt:78)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2$expandResult$1$1.invokeSuspend(ActionUpdater.kt:275)
at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:608)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:873)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:763)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:750)
2025-09-01 11:28:15,226 [ 215022] SEVERE - #c.i.o.a.i.ActionManagerImpl - IntelliJ IDEA 2025.1.3  Build #IC-251.26927.53
2025-09-01 11:28:15,226 [ 215022] SEVERE - #c.i.o.a.i.ActionManagerImpl - JDK: 21.0.7; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-01 11:28:15,226 [ 215022] SEVERE - #c.i.o.a.i.ActionManagerImpl - OS: Windows 11
2025-09-01 11:28:15,226 [ 215022] SEVERE - #c.i.o.a.i.ActionManagerImpl - Plugin to blame: Zencoder Authentication Bypass version: 1.0.0
2025-09-01 11:28:15,228 [ 215024] SEVERE - #c.i.o.a.i.ActionManagerImpl - class with name 'ai.zencoder.plugin.bypass.actions.ReactivateBypassAction' must be an instance of 'com.intellij.openapi.actionSystem.AnAction'; got ai.zencoder.plugin.bypass.actions.ReactivateBypassAction@144e63df [Plugin: ai.zencoder.plugin.bypass]
com.intellij.diagnostic.PluginException: class with name 'ai.zencoder.plugin.bypass.actions.ReactivateBypassAction' must be an instance of 'com.intellij.openapi.actionSystem.AnAction'; got ai.zencoder.plugin.bypass.actions.ReactivateBypassAction@144e63df [Plugin: ai.zencoder.plugin.bypass]
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.instantiate(ActionManagerImpl.kt:1468)
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.convertStub(ActionManagerImpl.kt:1694)
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.getAction(ActionManagerImpl.kt:2191)
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.access$getAction(ActionManagerImpl.kt:1)
at com.intellij.openapi.actionSystem.impl.ActionManagerImpl.getAction(ActionManagerImpl.kt:402)
at com.intellij.openapi.actionSystem.DefaultActionGroup.getChildren(DefaultActionGroup.java:402)
at com.intellij.openapi.actionSystem.DefaultActionGroup.getChildren(DefaultActionGroup.java:368)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1$1.invoke(ActionUpdater.kt:337)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1$1.invoke(ActionUpdater.kt:336)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$2$1.invoke(ActionUpdater.kt:146)
at com.intellij.openapi.application.rw.InternalReadAction.insideReadAction(InternalReadAction.kt:120)
at com.intellij.openapi.application.rw.InternalReadAction.tryReadCancellable$lambda$6(InternalReadAction.kt:110)
at com.intellij.openapi.application.rw.CancellableReadActionKt.cancellableReadActionInternal$lambda$3$lambda$2$lambda$1(cancellableReadAction.kt:32)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.tryRunReadAction$lambda$11(AnyThreadWriteThreadingSupport.kt:522)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWithTemporaryThreadLocal(AnyThreadWriteThreadingSupport.kt:204)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.tryRunReadAction(AnyThreadWriteThreadingSupport.kt:522)
at com.intellij.openapi.application.impl.ApplicationImpl.tryRunReadAction(ApplicationImpl.java:1064)
at com.intellij.openapi.application.rw.CancellableReadActionKt.cancellableReadActionInternal$lambda$3$lambda$2(cancellableReadAction.kt:30)
at com.intellij.openapi.progress.util.ProgressIndicatorUtilService.runActionAndCancelBeforeWrite(ProgressIndicatorUtilService.java:73)
at com.intellij.openapi.progress.util.ProgressIndicatorUtils.runActionAndCancelBeforeWrite(ProgressIndicatorUtils.java:152)
at com.intellij.openapi.application.rw.CancellableReadActionKt.cancellableReadActionInternal(cancellableReadAction.kt:28)
at com.intellij.openapi.application.rw.InternalReadAction.tryReadCancellable(InternalReadAction.kt:109)
at com.intellij.openapi.application.rw.InternalReadAction.tryReadAction(InternalReadAction.kt:93)
at com.intellij.openapi.application.rw.InternalReadAction.readLoop(InternalReadAction.kt:80)
at com.intellij.openapi.application.rw.InternalReadAction.access$readLoop(InternalReadAction.kt:17)
at com.intellij.openapi.application.rw.InternalReadAction$runReadAction$4.invokeSuspend(InternalReadAction.kt:40)
at com.intellij.openapi.application.rw.InternalReadAction$runReadAction$4.invoke(InternalReadAction.kt)
at com.intellij.openapi.application.rw.InternalReadAction$runReadAction$4.invoke(InternalReadAction.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
at com.intellij.openapi.application.rw.InternalReadAction.runReadAction(InternalReadAction.kt:39)
at com.intellij.openapi.application.rw.PlatformReadWriteActionSupport.executeReadAction(PlatformReadWriteActionSupport.kt:38)
at com.intellij.openapi.application.ReadWriteActionSupport.executeReadAction$default(ReadWriteActionSupport.kt:15)
at com.intellij.openapi.application.CoroutinesKt.constrainedReadActionUndispatched(coroutines.kt:86)
at com.intellij.openapi.application.CoroutinesKt.readActionUndispatched(coroutines.kt:73)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$$inlined$useWithScope$1.invokeSuspend(trace.kt:59)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$$inlined$useWithScope$1.invoke(trace.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$$inlined$useWithScope$1.invoke(trace.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.callAction(ActionUpdater.kt:897)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.access$callAction(ActionUpdater.kt:78)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1.invokeSuspend(ActionUpdater.kt:336)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$$inlined$retryOnAwaitSharedData$1.invokeSuspend(ActionUpdater.kt:846)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$$inlined$retryOnAwaitSharedData$1.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$$inlined$retryOnAwaitSharedData$1.invoke(ActionUpdater.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.getGroupChildren(ActionUpdater.kt:928)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.access$getGroupChildren(ActionUpdater.kt:78)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2.invokeSuspend(ActionUpdater.kt:269)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2.invoke(ActionUpdater.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.doExpandActionGroup(ActionUpdater.kt:262)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.expandGroupChild(ActionUpdater.kt:410)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.access$expandGroupChild(ActionUpdater.kt:78)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2$expandResult$1$1.invokeSuspend(ActionUpdater.kt:275)
at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:608)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:873)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:763)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:750)
2025-09-01 11:28:15,228 [ 215024] SEVERE - #c.i.o.a.i.ActionManagerImpl - IntelliJ IDEA 2025.1.3  Build #IC-251.26927.53
2025-09-01 11:28:15,228 [ 215024] SEVERE - #c.i.o.a.i.ActionManagerImpl - JDK: 21.0.7; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-01 11:28:15,229 [ 215025] SEVERE - #c.i.o.a.i.ActionManagerImpl - OS: Windows 11
2025-09-01 11:28:15,229 [ 215025] SEVERE - #c.i.o.a.i.ActionManagerImpl - Plugin to blame: Zencoder Authentication Bypass version: 1.0.0
2025-09-01 11:28:15,229 [ 215025] SEVERE - #c.i.o.a.i.ActionManagerImpl - class with name 'ai.zencoder.plugin.bypass.actions.ShowDiagnosticsAction' must be an instance of 'com.intellij.openapi.actionSystem.AnAction'; got ai.zencoder.plugin.bypass.actions.ShowDiagnosticsAction@72b94c77 [Plugin: ai.zencoder.plugin.bypass]
com.intellij.diagnostic.PluginException: class with name 'ai.zencoder.plugin.bypass.actions.ShowDiagnosticsAction' must be an instance of 'com.intellij.openapi.actionSystem.AnAction'; got ai.zencoder.plugin.bypass.actions.ShowDiagnosticsAction@72b94c77 [Plugin: ai.zencoder.plugin.bypass]
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.instantiate(ActionManagerImpl.kt:1468)
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.convertStub(ActionManagerImpl.kt:1694)
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.getAction(ActionManagerImpl.kt:2191)
at com.intellij.openapi.actionSystem.impl.ActionManagerImplKt.access$getAction(ActionManagerImpl.kt:1)
at com.intellij.openapi.actionSystem.impl.ActionManagerImpl.getAction(ActionManagerImpl.kt:402)
at com.intellij.openapi.actionSystem.DefaultActionGroup.getChildren(DefaultActionGroup.java:402)
at com.intellij.openapi.actionSystem.DefaultActionGroup.getChildren(DefaultActionGroup.java:368)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1$1.invoke(ActionUpdater.kt:337)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1$1.invoke(ActionUpdater.kt:336)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$2$1.invoke(ActionUpdater.kt:146)
at com.intellij.openapi.application.rw.InternalReadAction.insideReadAction(InternalReadAction.kt:120)
at com.intellij.openapi.application.rw.InternalReadAction.tryReadCancellable$lambda$6(InternalReadAction.kt:110)
at com.intellij.openapi.application.rw.CancellableReadActionKt.cancellableReadActionInternal$lambda$3$lambda$2$lambda$1(cancellableReadAction.kt:32)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.tryRunReadAction$lambda$11(AnyThreadWriteThreadingSupport.kt:522)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWithTemporaryThreadLocal(AnyThreadWriteThreadingSupport.kt:204)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.tryRunReadAction(AnyThreadWriteThreadingSupport.kt:522)
at com.intellij.openapi.application.impl.ApplicationImpl.tryRunReadAction(ApplicationImpl.java:1064)
at com.intellij.openapi.application.rw.CancellableReadActionKt.cancellableReadActionInternal$lambda$3$lambda$2(cancellableReadAction.kt:30)
at com.intellij.openapi.progress.util.ProgressIndicatorUtilService.runActionAndCancelBeforeWrite(ProgressIndicatorUtilService.java:73)
at com.intellij.openapi.progress.util.ProgressIndicatorUtils.runActionAndCancelBeforeWrite(ProgressIndicatorUtils.java:152)
at com.intellij.openapi.application.rw.CancellableReadActionKt.cancellableReadActionInternal(cancellableReadAction.kt:28)
at com.intellij.openapi.application.rw.InternalReadAction.tryReadCancellable(InternalReadAction.kt:109)
at com.intellij.openapi.application.rw.InternalReadAction.tryReadAction(InternalReadAction.kt:93)
at com.intellij.openapi.application.rw.InternalReadAction.readLoop(InternalReadAction.kt:80)
at com.intellij.openapi.application.rw.InternalReadAction.access$readLoop(InternalReadAction.kt:17)
at com.intellij.openapi.application.rw.InternalReadAction$runReadAction$4.invokeSuspend(InternalReadAction.kt:40)
at com.intellij.openapi.application.rw.InternalReadAction$runReadAction$4.invoke(InternalReadAction.kt)
at com.intellij.openapi.application.rw.InternalReadAction$runReadAction$4.invoke(InternalReadAction.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
at com.intellij.openapi.application.rw.InternalReadAction.runReadAction(InternalReadAction.kt:39)
at com.intellij.openapi.application.rw.PlatformReadWriteActionSupport.executeReadAction(PlatformReadWriteActionSupport.kt:38)
at com.intellij.openapi.application.ReadWriteActionSupport.executeReadAction$default(ReadWriteActionSupport.kt:15)
at com.intellij.openapi.application.CoroutinesKt.constrainedReadActionUndispatched(coroutines.kt:86)
at com.intellij.openapi.application.CoroutinesKt.readActionUndispatched(coroutines.kt:73)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$$inlined$useWithScope$1.invokeSuspend(trace.kt:59)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$$inlined$useWithScope$1.invoke(trace.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$callAction$$inlined$useWithScope$1.invoke(trace.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.callAction(ActionUpdater.kt:897)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.access$callAction(ActionUpdater.kt:78)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1.invokeSuspend(ActionUpdater.kt:336)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$children$1$1.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$$inlined$retryOnAwaitSharedData$1.invokeSuspend(ActionUpdater.kt:846)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$$inlined$retryOnAwaitSharedData$1.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$getGroupChildren$$inlined$retryOnAwaitSharedData$1.invoke(ActionUpdater.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.getGroupChildren(ActionUpdater.kt:928)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.access$getGroupChildren(ActionUpdater.kt:78)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2.invokeSuspend(ActionUpdater.kt:269)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2.invoke(ActionUpdater.kt)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2.invoke(ActionUpdater.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.doExpandActionGroup(ActionUpdater.kt:262)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.expandGroupChild(ActionUpdater.kt:410)
at com.intellij.openapi.actionSystem.impl.ActionUpdater.access$expandGroupChild(ActionUpdater.kt:78)
at com.intellij.openapi.actionSystem.impl.ActionUpdater$doExpandActionGroup$2$expandResult$1$1.invokeSuspend(ActionUpdater.kt:275)
at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:608)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:873)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:763)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:750)
2025-09-01 11:28:15,229 [ 215025] SEVERE - #c.i.o.a.i.ActionManagerImpl - IntelliJ IDEA 2025.1.3  Build #IC-251.26927.53
2025-09-01 11:28:15,229 [ 215025] SEVERE - #c.i.o.a.i.ActionManagerImpl - JDK: 21.0.7; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-01 11:28:15,229 [ 215025] SEVERE - #c.i.o.a.i.ActionManagerImpl - OS: Windows 11
2025-09-01 11:28:15,229 [ 215025] SEVERE - #c.i.o.a.i.ActionManagerImpl - Plugin to blame: Zencoder Authentication Bypass version: 1.0.0
2025-09-01 11:28:15,237 [ 215033] SEVERE - #c.i.i.p.PluginManager - There's no registered action with id=ZencoderBypass.Status
java.lang.IllegalArgumentException: There's no registered action with id=ZencoderBypass.Status
at com.intellij.openapi.actionSystem.impl.actionholder.IdActionRef.getAction(ActionRef.kt:21)
at com.intellij.openapi.actionSystem.impl.ActionMenuItem.updateAccelerator(ActionMenuItem.kt:107)
at com.intellij.openapi.actionSystem.impl.ActionMenuItem.<init>(ActionMenuItem.kt:95)
at com.intellij.openapi.actionSystem.impl.Utils.fillMenuInner(Utils.kt:556)
at com.intellij.openapi.actionSystem.impl.Utils.fillMenu$intellij_platform_ide_impl(Utils.kt:476)
at com.intellij.openapi.actionSystem.impl.ActionMenu.fillMenu(ActionMenu.kt:424)
at com.intellij.openapi.actionSystem.impl.ActionMenu.setPopupMenuVisible(ActionMenu.kt:365)
at java.desktop/javax.swing.JPopupMenu.menuSelectionChanged(JPopupMenu.java:1531)
at java.desktop/javax.swing.MenuSelectionManager.setSelectedPath(MenuSelectionManager.java:134)
at java.desktop/javax.swing.plaf.basic.BasicMenuUI$Handler.mouseEntered(BasicMenuUI.java:595)
at java.desktop/java.awt.Component.processMouseEvent(Component.java:6682)
at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3394)
at com.intellij.openapi.actionSystem.impl.ActionMenu.processMouseEvent(ActionMenu.kt:267)
at java.desktop/java.awt.Component.processEvent(Component.java:6438)
at java.desktop/java.awt.Container.processEvent(Container.java:2266)
at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5043)
at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
at java.desktop/java.awt.Component.dispatchEvent(Component.java:4871)
at java.desktop/java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4963)
at java.desktop/java.awt.LightweightDispatcher.retargetMouseEnterExit(Container.java:4728)
at java.desktop/java.awt.LightweightDispatcher.trackMouseEnterExit(Container.java:4706)
at java.desktop/java.awt.LightweightDispatcher.processMouseEvent(Container.java:4557)
at java.desktop/java.awt.LightweightDispatcher.dispatchEvent(Container.java:4518)
at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2310)
at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2810)
at java.desktop/java.awt.Component.dispatchEvent(Component.java:4871)
at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:783)
at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98)
at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:755)
at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:753)
at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:752)
at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:585)
at com.intellij.ide.IdeEventQueue.dispatchMouseEvent(IdeEventQueue.kt:530)
at com.intellij.ide.IdeEventQueue._dispatchEvent$lambda$16(IdeEventQueue.kt:473)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWriteIntentReadAction$lambda$6(AnyThreadWriteThreadingSupport.kt:274)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWithTemporaryThreadLocal(AnyThreadWriteThreadingSupport.kt:204)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWriteIntentReadAction(AnyThreadWriteThreadingSupport.kt:274)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runPreventiveWriteIntentReadAction(AnyThreadWriteThreadingSupport.kt:218)
at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:473)
at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:307)
at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:864)
at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:306)
at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:958)
at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:117)
at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:958)
at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:301)
at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:341)
at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
2025-09-01 11:28:15,238 [ 215034] SEVERE - #c.i.i.p.PluginManager - IntelliJ IDEA 2025.1.3  Build #IC-251.26927.53
2025-09-01 11:28:15,238 [ 215034] SEVERE - #c.i.i.p.PluginManager - JDK: 21.0.7; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-09-01 11:28:15,238 [ 215034] SEVERE - #c.i.i.p.PluginManager - OS: Windows 11
2025-09-01 11:28:24,538 [ 224334]   WARN - #c.i.o.o.e.ConfigurableExtensionPointUtil - ignore deprecated groupId: language for id: preferences.language.Kotlin.scripting
2025-09-01 11:28:24,542 [ 224338]   WARN - #c.i.o.o.e.ConfigurableExtensionPointUtil - ignore deprecated groupId: tools for id: kotlinNotebook.newNotebook
2025-09-01 11:28:30,990 [ 230786]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
2025-09-01 11:28:49,924 [ 249720]   WARN - #c.i.o.o.e.ConfigurableCardPanel - auto-dispose 'Plugins' id=preferences.pluginManager
2025-09-01 11:28:57,197 [ 256993]   INFO - #c.i.w.i.i.j.s.JpsGlobalModelSynchronizerImpl - Saving global entities com.intellij.platform.workspace.jps.entities.SdkEntity to files
2025-09-01 11:28:57,209 [ 257005]   INFO - #c.i.w.i.i.j.s.JpsGlobalModelSynchronizerImpl - Saving global entities com.intellij.platform.workspace.jps.entities.LibraryEntity to files
2025-09-01 11:28:57,281 [ 257077]   INFO - #c.i.c.ComponentStoreImpl - Saving appGradleDslModelExperimentalSettings took 27 ms
2025-09-01 11:29:27,235 [ 287031]   INFO - #c.i.w.i.i.j.s.JpsGlobalModelSynchronizerImpl - Saving global entities com.intellij.platform.workspace.jps.entities.SdkEntity to files
2025-09-01 11:29:27,238 [ 287034]   INFO - #c.i.w.i.i.j.s.JpsGlobalModelSynchronizerImpl - Saving global entities com.intellij.platform.workspace.jps.entities.LibraryEntity to files
2025-09-01 11:29:57,408 [ 317204]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
2025-09-01 11:29:59,363 [ 319159]   INFO - #o.j.k.i.script - [KOTLIN_SCRIPTING] Loading script definitions: classes = [], classpath = []
2025-09-01 11:29:59,364 [ 319160]   INFO - #o.j.k.i.script - [KOTLIN_SCRIPTING] Loaded definitions: classes = [], definitions = []