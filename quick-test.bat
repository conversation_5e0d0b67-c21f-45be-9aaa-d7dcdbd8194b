@echo off
chcp 65001 >nul
echo === Zencoder 认证绕过快速测试 ===

REM 清理并创建输出目录
rmdir /s /q build\classes 2>nul
mkdir build\classes 2>nul

echo 1. 编译基础类...
javac -cp "src\main\java" -d build\classes src\main\java\ai\zencoder\plugin\auth\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 基础类编译失败
    goto :end
)
echo ✅ 基础类编译成功

echo 2. 编译配置类...
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\ai\zencoder\plugin\config\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 配置类编译失败
    goto :end
)
echo ✅ 配置类编译成功

echo 3. 编译观察者类...
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\ai\zencoder\plugin\observers\auth\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 观察者类编译失败
    goto :end
)
echo ✅ 观察者类编译成功

echo 4. 编译 IntelliJ 模拟类...
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\com\intellij\openapi\application\*.java
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\com\intellij\openapi\diagnostic\*.java
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\com\intellij\openapi\project\*.java
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\com\intellij\openapi\components\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ IntelliJ 模拟类编译失败
    goto :end
)
echo ✅ IntelliJ 模拟类编译成功

echo 5. 编译核心绕过类...
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\ai\zencoder\plugin\bypass\FakeDataGenerator.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ FakeDataGenerator 编译失败
    goto :end
)
echo ✅ FakeDataGenerator 编译成功

javac -cp "src\main\java;build\classes" -d build\classes src\main\java\ai\zencoder\plugin\bypass\AdvancedReflectionUtils.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ AdvancedReflectionUtils 编译失败
    goto :end
)
echo ✅ AdvancedReflectionUtils 编译成功

javac -cp "src\main\java;build\classes" -d build\classes src\main\java\ai\zencoder\plugin\bypass\ReflectionBypassUtils.java
if %ERRORLEVEL% NEQ 0 (
    echo ❌ ReflectionBypassUtils 编译失败
    goto :end
)
echo ✅ ReflectionBypassUtils 编译成功

echo 6. 运行核心逻辑测试...
java -cp build\classes ai.zencoder.plugin.bypass.SimpleBypassTest
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 核心逻辑测试失败
    goto :end
)

echo.
echo 🎉 快速测试完成！核心功能验证成功！
echo.
echo === 测试结果摘要 ===
echo ✅ JWT 令牌生成
echo ✅ 用户数据生成
echo ✅ 认证信息生成
echo ✅ 动态代理创建
echo ✅ 反射工具功能
echo.
echo 🚀 Zencoder 认证绕过核心功能已准备就绪！
echo 用户可以无需注册登录即可使用 Zencoder 的全部功能。

:end
pause
