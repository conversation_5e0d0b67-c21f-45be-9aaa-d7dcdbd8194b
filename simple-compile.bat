@echo off
chcp 65001 >nul
echo === Zencoder Bypass Compile Test ===

mkdir build\classes 2>nul

echo Compiling auth classes...
javac -cp src\main\java -d build\classes src\main\java\ai\zencoder\plugin\auth\*.java
if %ERRORLEVEL% NEQ 0 goto error

echo Compiling config classes...
javac -cp src\main\java;build\classes -d build\classes src\main\java\ai\zencoder\plugin\config\*.java
if %ERRORLEVEL% NEQ 0 goto error

echo Compiling observer classes...
javac -cp src\main\java;build\classes -d build\classes src\main\java\ai\zencoder\plugin\observers\auth\*.java
if %ERRORLEVEL% NEQ 0 goto error

echo Compiling IntelliJ mock classes...
javac -cp src\main\java;build\classes -d build\classes src\main\java\com\intellij\openapi\application\*.java
if %ERRORLEVEL% NEQ 0 goto error

javac -cp src\main\java;build\classes -d build\classes src\main\java\com\intellij\openapi\diagnostic\*.java
if %ERRORLEVEL% NEQ 0 goto error

javac -cp src\main\java;build\classes -d build\classes src\main\java\com\intellij\openapi\project\*.java
if %ERRORLEVEL% NEQ 0 goto error

javac -cp src\main\java;build\classes -d build\classes src\main\java\com\intellij\openapi\components\*.java
if %ERRORLEVEL% NEQ 0 goto error

echo Compiling bypass core classes...
javac -cp src\main\java;build\classes -d build\classes src\main\java\ai\zencoder\plugin\bypass\FakeDataGenerator.java
if %ERRORLEVEL% NEQ 0 goto error

javac -cp src\main\java;build\classes -d build\classes src\main\java\ai\zencoder\plugin\bypass\AdvancedReflectionUtils.java
if %ERRORLEVEL% NEQ 0 goto error

echo SUCCESS: All classes compiled!

echo Running simple test...
java -cp build\classes ai.zencoder.plugin.bypass.SimpleBypassTest

goto end

:error
echo ERROR: Compilation failed
exit /b 1

:end
