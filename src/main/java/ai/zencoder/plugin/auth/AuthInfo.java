package ai.zencoder.plugin.auth;

import java.util.Base64;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Zencoder AuthInfo 的简化版本
 */
public class AuthInfo {
    
    private final String accessToken;
    private final String refreshToken;
    private final UserData userData;
    
    public AuthInfo(String accessToken, String refreshToken) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.userData = parseUserDataFromToken(accessToken);
    }
    
    /**
     * 从 JWT 令牌中解析用户数据
     */
    private UserData parseUserDataFromToken(String token) {
        try {
            if (token == null || token.isEmpty()) {
                return createDefaultUserData();
            }
            
            // 分割 JWT 令牌
            String[] parts = token.split("\\.");
            if (parts.length < 2) {
                return createDefaultUserData();
            }
            
            // 解码 payload 部分
            byte[] decodedBytes = Base64.getUrlDecoder().decode(parts[1]);
            String payload = new String(decodedBytes);
            
            // 使用简单的 JSON 解析
            Map<String, Object> payloadMap = parseSimpleJson(payload);
            
            // 提取用户信息
            String id = (String) payloadMap.getOrDefault("id", "bypass_user");
            String name = (String) payloadMap.getOrDefault("name", "Bypass User");
            String email = (String) payloadMap.getOrDefault("email", "bypass@local");
            
            @SuppressWarnings("unchecked")
            List<String> permissions = (List<String>) payloadMap.getOrDefault("permissions", 
                Arrays.asList("read", "write", "admin"));
            
            @SuppressWarnings("unchecked")
            List<String> zencoderRoles = (List<String>) payloadMap.getOrDefault("zencoderRoles", 
                Arrays.asList("premium_user"));
            
            // 创建自定义声明
            UserData.CustomClaims customClaims = new UserData.CustomClaims(
                "enterprise", 
                false, 
                permissions
            );
            
            return new UserData(id, name, email, permissions, zencoderRoles, customClaims);
            
        } catch (Exception e) {
            // 如果解析失败，返回默认用户数据
            return createDefaultUserData();
        }
    }
    
    /**
     * 创建默认用户数据
     */
    private UserData createDefaultUserData() {
        return new UserData(
            "bypass_user_default",
            "Bypass User",
            "bypass@local",
            Arrays.asList("read", "write", "admin"),
            Arrays.asList("premium_user"),
            new UserData.CustomClaims("enterprise", false, Arrays.asList("read", "write", "admin"))
        );
    }

    /**
     * 简单的 JSON 解析方法
     */
    private Map<String, Object> parseSimpleJson(String json) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 简化的 JSON 解析，只处理基本的字符串和数组
            json = json.trim();
            if (json.startsWith("{") && json.endsWith("}")) {
                json = json.substring(1, json.length() - 1);
                String[] pairs = json.split(",");
                for (String pair : pairs) {
                    String[] keyValue = pair.split(":", 2);
                    if (keyValue.length == 2) {
                        String key = keyValue[0].trim().replaceAll("\"", "");
                        String value = keyValue[1].trim().replaceAll("\"", "");

                        // 处理数组
                        if (value.startsWith("[") && value.endsWith("]")) {
                            String arrayContent = value.substring(1, value.length() - 1);
                            String[] items = arrayContent.split(",");
                            List<String> list = Arrays.asList(items);
                            for (int i = 0; i < list.size(); i++) {
                                list.set(i, list.get(i).trim().replaceAll("\"", ""));
                            }
                            result.put(key, list);
                        } else {
                            result.put(key, value);
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 解析失败时返回空 Map
        }
        return result;
    }

    // 混淆的方法名（基于实际 Zencoder 代码）
    public String getAccessTokenMixed() {
        return accessToken;
    }

    public String getRefreshTokenMixed() {
        return refreshToken;
    }

    public UserData getUserDataMixed() {
        return userData;
    }
    
    // 便利方法（非混淆版本）
    public String getAccessToken() {
        return accessToken;
    }
    
    public String getRefreshToken() {
        return refreshToken;
    }
    
    public UserData getUserData() {
        return userData;
    }
    
    @Override
    public String toString() {
        return "AuthInfo{" +
                "accessToken='" + (accessToken != null ? accessToken.substring(0, Math.min(20, accessToken.length())) + "..." : "null") + '\'' +
                ", refreshToken='" + (refreshToken != null ? refreshToken.substring(0, Math.min(10, refreshToken.length())) + "..." : "null") + '\'' +
                ", userData=" + userData +
                '}';
    }
}
