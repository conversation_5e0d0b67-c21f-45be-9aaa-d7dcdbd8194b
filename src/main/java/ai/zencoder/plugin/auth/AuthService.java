package ai.zencoder.plugin.auth;

/**
 * Zencoder AuthService 接口的简化版本
 * 用于编译我们的绕过代码
 */
public interface AuthService {

    // 混淆的方法名 - 基于实际的 Zencoder 代码
    String h() throws NoAuthInfoException; // getAccessToken

    AuthInfo b() throws NoAuthInfoException; // getAuthInfo

    AuthInfo a(); // getAuthInfoOrNull

    UserData getUserDataOrNull(); // getUserDataOrNull - 避免方法重复

    void signInMethod(); // signIn - 避免方法重复

    void signUpMethod(); // signUp - 避免方法重复

    boolean isAuthenticatedMethod(); // isAuthenticated - 避免方法重复

    void c(); // resetAuthentication

    AuthInfo refreshAuthenticationMethod(String expiredAccessToken); // refreshAuthentication
    
    // 便利方法（非混淆版本，用于向后兼容）
    default String getAccessToken() throws NoAuthInfoException {
        return h();
    }

    default AuthInfo getAuthInfo() throws NoAuthInfoException {
        return b();
    }

    default AuthInfo getAuthInfoOrNull() {
        return a();
    }

    default void signIn() {
        signInMethod();
    }

    default void signUp() {
        signUpMethod();
    }

    default boolean isAuthenticated() {
        return isAuthenticatedMethod();
    }

    default void resetAuthentication() {
        c();
    }

    default AuthInfo refreshAuthentication(String expiredAccessToken) {
        return refreshAuthenticationMethod(expiredAccessToken);
    }
}
