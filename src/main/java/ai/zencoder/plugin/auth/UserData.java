package ai.zencoder.plugin.auth;

import java.util.List;

/**
 * Zencoder UserData 的简化版本
 */
public class UserData {
    
    private final String id;
    private final String name;
    private final String email;
    private final List<String> permissions;
    private final List<String> zencoderRoles;
    private final CustomClaims customClaims;
    
    public UserData(String id, String name, String email, List<String> permissions, 
                   List<String> zencoderRoles, CustomClaims customClaims) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.permissions = permissions;
        this.zencoderRoles = zencoderRoles;
        this.customClaims = customClaims;
    }
    
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public List<String> getPermissions() {
        return permissions;
    }
    
    public List<String> getZencoderRoles() {
        return zencoderRoles;
    }
    
    public CustomClaims getCustomClaims() {
        return customClaims;
    }
    
    @Override
    public String toString() {
        return "UserData{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", permissions=" + permissions +
                ", zencoderRoles=" + zencoderRoles +
                ", customClaims=" + customClaims +
                '}';
    }
    
    /**
     * 自定义声明类
     */
    public static class CustomClaims {
        private final String plan;
        private final Boolean isOnTrial;
        private final List<String> permissions;
        
        public CustomClaims(String plan, Boolean isOnTrial, List<String> permissions) {
            this.plan = plan;
            this.isOnTrial = isOnTrial;
            this.permissions = permissions;
        }
        
        public String getPlan() {
            return plan;
        }
        
        public Boolean getIsOnTrial() {
            return isOnTrial;
        }
        
        public List<String> getPermissions() {
            return permissions;
        }
        
        @Override
        public String toString() {
            return "CustomClaims{" +
                    "plan='" + plan + '\'' +
                    ", isOnTrial=" + isOnTrial +
                    ", permissions=" + permissions +
                    '}';
        }
    }
}
