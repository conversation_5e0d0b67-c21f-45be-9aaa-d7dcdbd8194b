package ai.zencoder.plugin.bypass;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ApplicationManager.Application;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 高级反射工具类
 * 提供深度服务替换和反射操作功能
 */
public class AdvancedReflectionUtils {
    private static final Logger LOG = Logger.getInstance(AdvancedReflectionUtils.class);
    private static final Map<String, Object> reflectionCache = new ConcurrentHashMap<>();
    
    /**
     * 深度服务替换 - 处理多层服务容器
     */
    public static boolean deepReplaceService(Class<?> serviceClass, Object newImplementation) {
        try {
            LOG.info("开始深度替换服务: " + serviceClass.getName());
            
            Application app = ApplicationManager.getApplication();
            
            // 策略1: 直接替换应用级服务
            if (replaceApplicationService(app, serviceClass, newImplementation)) {
                LOG.info("应用级服务替换成功: " + serviceClass.getName());
                return true;
            }
            
            // 策略2: 替换项目级服务
            Project[] projects = ProjectManager.getInstance().getOpenProjects();
            boolean projectSuccess = false;
            for (Project project : projects) {
                if (replaceProjectService(project, serviceClass, newImplementation)) {
                    LOG.info("项目级服务替换成功: " + serviceClass.getName() + " (项目: " + project.getName() + ")");
                    projectSuccess = true;
                }
            }
            
            // 策略3: 通过 ServiceManager 替换
            boolean serviceManagerSuccess = replaceViaServiceManager(serviceClass, newImplementation);
            if (serviceManagerSuccess) {
                LOG.info("ServiceManager 服务替换成功: " + serviceClass.getName());
            }
            
            // 策略4: 通过组件管理器替换
            boolean componentSuccess = replaceViaComponentManager(serviceClass, newImplementation);
            if (componentSuccess) {
                LOG.info("ComponentManager 服务替换成功: " + serviceClass.getName());
            }
            
            return projectSuccess || serviceManagerSuccess || componentSuccess;
            
        } catch (Exception e) {
            LOG.error("深度服务替换失败: " + serviceClass.getName(), e);
            return false;
        }
    }
    
    /**
     * 替换应用级服务
     */
    private static boolean replaceApplicationService(Application app, Class<?> serviceClass, Object newImpl) {
        try {
            // 尝试多种可能的字段名
            String[] possibleFields = {
                "myServiceContainer", "serviceContainer", "myContainer", 
                "myServices", "services", "myComponentManager"
            };
            
            for (String fieldName : possibleFields) {
                Field containerField = findFieldInHierarchy(app.getClass(), fieldName);
                if (containerField != null) {
                    containerField.setAccessible(true);
                    Object container = containerField.get(app);
                    if (container != null && replaceInContainer(container, serviceClass, newImpl)) {
                        LOG.debug("通过字段 " + fieldName + " 替换应用服务成功");
                        return true;
                    }
                }
            }
            
            return false;
        } catch (Exception e) {
            LOG.warn("替换应用服务失败", e);
            return false;
        }
    }
    
    /**
     * 替换项目级服务
     */
    private static boolean replaceProjectService(Project project, Class<?> serviceClass, Object newImpl) {
        try {
            // 尝试多种可能的字段名
            String[] possibleFields = {
                "myServiceContainer", "serviceContainer", "myContainer",
                "myServices", "services", "myComponentManager"
            };
            
            for (String fieldName : possibleFields) {
                Field containerField = findFieldInHierarchy(project.getClass(), fieldName);
                if (containerField != null) {
                    containerField.setAccessible(true);
                    Object container = containerField.get(project);
                    if (container != null && replaceInContainer(container, serviceClass, newImpl)) {
                        LOG.debug("通过字段 " + fieldName + " 替换项目服务成功");
                        return true;
                    }
                }
            }
            
            return false;
        } catch (Exception e) {
            LOG.debug("替换项目服务失败: " + project.getName() + " - " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 通过 ServiceManager 替换服务
     */
    private static boolean replaceViaServiceManager(Class<?> serviceClass, Object newImpl) {
        try {
            // 尝试获取 ServiceManager 类
            Class<?> serviceManagerClass = Class.forName("com.intellij.openapi.components.ServiceManager");
            
            // 查找服务缓存字段
            String[] cacheFields = {"ourCache", "cache", "myCache", "serviceCache"};
            
            for (String fieldName : cacheFields) {
                Field cacheField = findFieldInHierarchy(serviceManagerClass, fieldName);
                if (cacheField != null) {
                    cacheField.setAccessible(true);
                    Object cache = cacheField.get(null);
                    
                    if (cache instanceof Map) {
                        Map<Object, Object> serviceCache = (Map<Object, Object>) cache;
                        serviceCache.put(serviceClass, newImpl);
                        LOG.debug("通过 ServiceManager 缓存替换服务成功");
                        return true;
                    }
                }
            }
            
            return false;
        } catch (Exception e) {
            LOG.debug("通过 ServiceManager 替换服务失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 通过组件管理器替换服务
     */
    private static boolean replaceViaComponentManager(Class<?> serviceClass, Object newImpl) {
        try {
            Application app = ApplicationManager.getApplication();
            
            // 尝试调用 registerService 方法
            Method registerMethod = findMethodInHierarchy(app.getClass(), "registerService");
            if (registerMethod != null) {
                registerMethod.setAccessible(true);
                registerMethod.invoke(app, serviceClass, newImpl);
                LOG.debug("通过 registerService 方法替换服务成功");
                return true;
            }
            
            // 尝试调用 replaceService 方法
            Method replaceMethod = findMethodInHierarchy(app.getClass(), "replaceService");
            if (replaceMethod != null) {
                replaceMethod.setAccessible(true);
                replaceMethod.invoke(app, serviceClass, newImpl);
                LOG.debug("通过 replaceService 方法替换服务成功");
                return true;
            }
            
            return false;
        } catch (Exception e) {
            LOG.debug("通过组件管理器替换服务失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 在容器中替换服务
     */
    private static boolean replaceInContainer(Object container, Class<?> serviceClass, Object newImpl) {
        try {
            // 获取容器的所有字段
            Field[] fields = container.getClass().getDeclaredFields();
            
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(container);
                
                if (value instanceof Map) {
                    Map<Object, Object> map = (Map<Object, Object>) value;
                    
                    // 尝试不同的键格式
                    Object[] possibleKeys = {
                        serviceClass,
                        serviceClass.getName(),
                        serviceClass.getSimpleName(),
                        serviceClass.getCanonicalName()
                    };
                    
                    boolean replaced = false;
                    for (Object key : possibleKeys) {
                        if (map.containsKey(key)) {
                            Object oldValue = map.put(key, newImpl);
                            LOG.debug("替换服务映射: " + key + " -> " + newImpl.getClass().getSimpleName());
                            replaced = true;
                        }
                    }
                    
                    // 如果没有找到现有键，直接添加
                    if (!replaced) {
                        map.put(serviceClass, newImpl);
                        LOG.debug("添加新服务映射: " + serviceClass.getName());
                        replaced = true;
                    }
                    
                    if (replaced) {
                        return true;
                    }
                }
            }
            
            return false;
        } catch (Exception e) {
            LOG.error("在容器中替换服务失败", e);
            return false;
        }
    }
    
    /**
     * 在类层次结构中查找字段
     */
    private static Field findFieldInHierarchy(Class<?> clazz, String... fieldNames) {
        String cacheKey = clazz.getName() + ":" + String.join(",", fieldNames);
        
        // 检查缓存
        Field cachedField = (Field) reflectionCache.get(cacheKey);
        if (cachedField != null) {
            return cachedField;
        }
        
        Class<?> current = clazz;
        while (current != null && current != Object.class) {
            for (String fieldName : fieldNames) {
                try {
                    Field field = current.getDeclaredField(fieldName);
                    if (field != null) {
                        // 缓存结果
                        reflectionCache.put(cacheKey, field);
                        return field;
                    }
                } catch (NoSuchFieldException e) {
                    // 继续查找
                }
            }
            current = current.getSuperclass();
        }
        
        return null;
    }
    
    /**
     * 在类层次结构中查找方法
     */
    private static Method findMethodInHierarchy(Class<?> clazz, String methodName) {
        String cacheKey = clazz.getName() + ":method:" + methodName;
        
        // 检查缓存
        Method cachedMethod = (Method) reflectionCache.get(cacheKey);
        if (cachedMethod != null) {
            return cachedMethod;
        }
        
        Class<?> current = clazz;
        while (current != null && current != Object.class) {
            try {
                Method[] methods = current.getDeclaredMethods();
                for (Method method : methods) {
                    if (method.getName().equals(methodName)) {
                        // 缓存结果
                        reflectionCache.put(cacheKey, method);
                        return method;
                    }
                }
            } catch (Exception e) {
                // 继续查找
            }
            current = current.getSuperclass();
        }
        
        return null;
    }
    
    /**
     * 获取对象的字段值
     */
    public static Object getFieldValue(Object obj, String fieldName) {
        try {
            Field field = findFieldInHierarchy(obj.getClass(), fieldName);
            if (field != null) {
                field.setAccessible(true);
                return field.get(obj);
            }
            return null;
        } catch (Exception e) {
            LOG.error("获取字段值失败: " + fieldName, e);
            return null;
        }
    }
    
    /**
     * 设置对象的字段值
     */
    public static boolean setFieldValue(Object obj, String fieldName, Object value) {
        try {
            Field field = findFieldInHierarchy(obj.getClass(), fieldName);
            if (field != null) {
                field.setAccessible(true);
                field.set(obj, value);
                return true;
            }
            return false;
        } catch (Exception e) {
            LOG.error("设置字段值失败: " + fieldName, e);
            return false;
        }
    }
    
    /**
     * 调用对象的方法
     */
    public static Object invokeMethod(Object obj, String methodName, Object... args) {
        try {
            Method method = findMethodInHierarchy(obj.getClass(), methodName);
            if (method != null) {
                method.setAccessible(true);
                return method.invoke(obj, args);
            }
            return null;
        } catch (Exception e) {
            LOG.error("调用方法失败: " + methodName, e);
            return null;
        }
    }
    
    /**
     * 检查类是否存在
     */
    public static boolean isClassAvailable(String className) {
        try {
            Class.forName(className);
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
    
    /**
     * 创建类的实例
     */
    public static Object createInstance(String className) {
        try {
            Class<?> clazz = Class.forName(className);
            return clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            LOG.error("创建实例失败: " + className, e);
            return null;
        }
    }
    
    /**
     * 清除反射缓存
     */
    public static void clearCache() {
        reflectionCache.clear();
        LOG.debug("反射缓存已清除");
    }
    
    /**
     * 获取反射统计信息
     */
    public static String getReflectionStats() {
        return String.format(
            "反射工具统计:\n" +
            "- 缓存条目数: %d\n" +
            "- 可用方法: 字段查找、方法查找、服务替换\n" +
            "- 支持的替换策略: 4种",
            reflectionCache.size()
        );
    }
}
