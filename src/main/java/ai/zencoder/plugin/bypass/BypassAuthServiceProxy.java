package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.AuthService;
import ai.zencoder.plugin.auth.NoAuthInfoException;
import ai.zencoder.plugin.auth.UserData;
import ai.zencoder.plugin.observers.auth.AuthObserver;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

/**
 * AuthService 代理实现
 * 使用动态代理拦截所有认证相关方法调用，返回伪造的认证数据
 */
public class BypassAuthServiceProxy implements InvocationHandler {
    private static final Logger LOG = Logger.getInstance(BypassAuthServiceProxy.class);

    private final AuthService originalService;
    private AuthInfo currentAuthInfo;
    private UserData currentUserData;

    public BypassAuthServiceProxy(AuthService originalService) {
        this.originalService = originalService;
        this.currentUserData = FakeDataGenerator.createFakeUserData();
        this.currentAuthInfo = FakeDataGenerator.createFakeAuthInfo();

        LOG.info("BypassAuthServiceProxy 已创建，用户: " + currentUserData.getName());
    }

    /**
     * 创建代理实例
     */
    public static AuthService createProxy(AuthService originalService) {
        BypassAuthServiceProxy handler = new BypassAuthServiceProxy(originalService);
        return (AuthService) Proxy.newProxyInstance(
            AuthService.class.getClassLoader(),
            new Class<?>[]{AuthService.class},
            handler
        );
    }
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        String methodName = method.getName();

        try {
            // 根据方法名进行拦截和处理
            switch (methodName) {
                case "h": // getAccessToken
                    String token = currentAuthInfo.getAccessToken();
                    LOG.debug("getAccessToken() 调用 - 返回伪造令牌: " + maskToken(token));
                    return token;

                case "b": // getAuthInfo 或 isAuthenticated
                    if (method.getReturnType() == boolean.class) {
                        // isAuthenticated
                        LOG.debug("isAuthenticated() 调用 - 返回 true");
                        return true;
                    } else {
                        // getAuthInfo
                        LOG.debug("getAuthInfo() 调用 - 返回伪造认证信息");
                        return currentAuthInfo;
                    }

                case "a": // getAuthInfoOrNull, getUserData, signIn, refreshAuthentication
                    if (method.getReturnType() == AuthInfo.class) {
                        if (method.getParameterCount() == 0) {
                            // getAuthInfoOrNull
                            LOG.debug("getAuthInfoOrNull() 调用 - 返回伪造认证信息");
                            return currentAuthInfo;
                        } else {
                            // refreshAuthentication
                            LOG.info("refreshAuthentication() 调用 - 生成新的伪造令牌");
                            refreshAuthData();
                            return currentAuthInfo;
                        }
                    } else if (method.getReturnType() == UserData.class) {
                        // getUserData
                        LOG.debug("getUserData() 调用 - 返回伪造用户数据: " + currentUserData.getName());
                        return currentUserData;
                    } else if (method.getReturnType() == void.class) {
                        // signIn
                        LOG.info("signIn() 调用 - 模拟登录成功");
                        refreshAuthData();
                        triggerSignInEvent();
                        return null;
                    }
                    break;

                case "c": // resetAuthentication
                    LOG.info("resetAuthentication() 调用 - 重置认证状态");
                    refreshAuthData();
                    try {
                        ZencoderAuthBypass.getInstance().forceReactivate();
                    } catch (Exception e) {
                        LOG.error("重新激活绕过失败", e);
                    }
                    return null;

                default:
                    // 对于其他方法，尝试智能处理
                    return handleOtherMethods(method, args);
            }

            // 默认返回值
            if (method.getReturnType() == boolean.class) {
                return true;
            } else if (method.getReturnType() == AuthInfo.class) {
                return currentAuthInfo;
            } else if (method.getReturnType() == UserData.class) {
                return currentUserData;
            } else if (method.getReturnType() == String.class) {
                return currentAuthInfo.getAccessToken();
            }

            return null;

        } catch (Exception e) {
            LOG.error("代理方法调用失败: " + methodName, e);

            // 尝试调用原始方法
            if (originalService != null) {
                try {
                    return method.invoke(originalService, args);
                } catch (Exception originalException) {
                    LOG.debug("原始方法调用也失败: " + originalException.getMessage());
                }
            }

            throw e;
        }
    }

    /**
     * 处理其他方法
     */
    private Object handleOtherMethods(Method method, Object[] args) {
        String methodName = method.getName();

        // 根据方法名模式进行处理
        if (methodName.toLowerCase().contains("auth") || methodName.toLowerCase().contains("sign")) {
            if (method.getReturnType() == boolean.class) {
                return true;
            } else if (method.getReturnType() == void.class) {
                refreshAuthData();
                triggerSignInEvent();
                return null;
            }
        }

        // 默认处理
        if (method.getReturnType() == boolean.class) {
            return true;
        } else if (method.getReturnType() == AuthInfo.class) {
            return currentAuthInfo;
        } else if (method.getReturnType() == UserData.class) {
            return currentUserData;
        } else if (method.getReturnType() == String.class) {
            return currentAuthInfo.getAccessToken();
        }

        return null;
    }
    
    /**
     * 刷新认证数据
     */
    private void refreshAuthData() {
        try {
            this.currentUserData = FakeDataGenerator.createFakeUserData();
            this.currentAuthInfo = FakeDataGenerator.createFakeAuthInfo();
            
            LOG.debug("认证数据已刷新");
        } catch (Exception e) {
            LOG.error("刷新认证数据失败", e);
        }
    }
    
    /**
     * 触发登录事件
     */
    private void triggerSignInEvent() {
        try {
            AuthObserver observer = ApplicationManager.getApplication().getService(AuthObserver.class);
            if (observer != null) {
                observer.signIn(currentAuthInfo); // 使用便利方法
                LOG.debug("已触发登录事件");
            } else {
                LOG.warn("AuthObserver 服务不可用");
            }
        } catch (Exception e) {
            LOG.error("触发登录事件失败", e);
        }
    }
    
    /**
     * 更新认证信息
     */
    public void updateAuthInfo(AuthInfo newAuthInfo) {
        if (newAuthInfo != null) {
            this.currentAuthInfo = newAuthInfo;
            this.currentUserData = newAuthInfo.getUserData();
            LOG.debug("认证信息已更新");
        }
    }
    
    /**
     * 获取原始服务（用于调试）
     */
    public AuthService getOriginalService() {
        return originalService;
    }
    
    /**
     * 掩码令牌用于日志输出
     */
    private String maskToken(String token) {
        if (token == null || token.length() < 10) {
            return "***";
        }
        return token.substring(0, 6) + "..." + token.substring(token.length() - 4);
    }
    
    /**
     * 获取代理状态信息
     */
    public String getProxyStatus() {
        try {
            StringBuilder status = new StringBuilder();
            status.append("代理状态: 激活\n");
            status.append("当前用户: ").append(currentUserData.getName()).append("\n");
            status.append("用户ID: ").append(currentUserData.getId()).append("\n");
            status.append("用户邮箱: ").append(currentUserData.getEmail()).append("\n");
            status.append("访问令牌: ").append(maskToken(currentAuthInfo.getAccessToken())).append("\n");
            status.append("刷新令牌: ").append(maskToken(currentAuthInfo.getRefreshToken())).append("\n");
            status.append("原始服务: ").append(originalService != null ? originalService.getClass().getSimpleName() : "null");
            
            return status.toString();
        } catch (Exception e) {
            return "获取代理状态失败: " + e.getMessage();
        }
    }
    
    /**
     * 检查代理是否正常工作
     */
    public boolean isProxyWorking() {
        try {
            return currentAuthInfo != null && 
                   currentUserData != null && 
                   currentAuthInfo.getAccessToken() != null &&
                   !currentAuthInfo.getAccessToken().isEmpty();
        } catch (Exception e) {
            LOG.error("检查代理状态失败", e);
            return false;
        }
    }
    
    /**
     * 强制刷新所有数据
     */
    public void forceRefresh() {
        LOG.info("强制刷新代理数据...");
        refreshAuthData();
        triggerSignInEvent();
    }
    
    @Override
    public String toString() {
        return "BypassAuthServiceProxy{" +
                "user=" + (currentUserData != null ? currentUserData.getName() : "null") +
                ", authenticated=" + true +
                ", working=" + isProxyWorking() +
                '}';
    }
}
