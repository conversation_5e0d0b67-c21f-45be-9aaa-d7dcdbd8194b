package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.auth.AuthService;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;

import java.lang.reflect.Proxy;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 绕过状态监控器
 * 持续监控认证绕过的状态，确保绕过始终有效
 */
@Service
public final class BypassStatusMonitor {
    private static final Logger LOG = Logger.getInstance(BypassStatusMonitor.class);
    
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    private final AtomicInteger checkCount = new AtomicInteger(0);
    private final AtomicInteger reactivationCount = new AtomicInteger(0);
    private final AtomicLong lastCheckTime = new AtomicLong(0);
    private final AtomicLong lastReactivationTime = new AtomicLong(0);
    
    private volatile boolean monitoring = false;
    
    /**
     * 启动状态监控
     */
    public void startMonitoring() {
        if (monitoring) {
            LOG.warn("状态监控已在运行中");
            return;
        }
        
        monitoring = true;
        LOG.info("启动 Zencoder 绕过状态监控...");
        
        // 主要监控任务 - 每30秒检查一次
        scheduler.scheduleAtFixedRate(this::performStatusCheck, 10, 30, TimeUnit.SECONDS);
        
        // 快速检查任务 - 每5秒检查一次（前5分钟）
        scheduler.scheduleAtFixedRate(this::performQuickCheck, 5, 5, TimeUnit.SECONDS);
        
        // 深度检查任务 - 每5分钟进行一次深度检查
        scheduler.scheduleAtFixedRate(this::performDeepCheck, 5, 5, TimeUnit.MINUTES);
        
        // 健康报告任务 - 每小时输出一次健康报告
        scheduler.scheduleAtFixedRate(this::generateHealthReport, 1, 1, TimeUnit.HOURS);
        
        LOG.info("绕过状态监控已启动");
    }
    
    /**
     * 执行状态检查
     */
    private void performStatusCheck() {
        try {
            checkCount.incrementAndGet();
            lastCheckTime.set(System.currentTimeMillis());
            
            LOG.debug("执行绕过状态检查 #" + checkCount.get());
            
            if (!isAuthBypassActive()) {
                LOG.warn("🚨 检测到认证绕过失效，尝试重新激活...");
                reactivateBypass();
            } else {
                LOG.debug("✅ 认证绕过状态正常");
            }
            
        } catch (Exception e) {
            LOG.error("状态检查失败", e);
        }
    }
    
    /**
     * 执行快速检查
     */
    private void performQuickCheck() {
        try {
            // 快速检查只在前5分钟执行
            if (System.currentTimeMillis() - getMonitorStartTime() > 5 * 60 * 1000) {
                return;
            }
            
            if (!isAuthBypassActive()) {
                LOG.warn("快速检查发现绕过失效，立即重新激活");
                reactivateBypass();
            }
            
        } catch (Exception e) {
            LOG.debug("快速检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行深度检查
     */
    private void performDeepCheck() {
        try {
            LOG.info("执行深度绕过状态检查...");
            
            // 检查 AuthService 状态
            boolean authServiceOk = checkAuthServiceStatus();
            
            // 检查存储状态
            boolean storageOk = checkStorageStatus();
            
            // 检查代理状态
            boolean proxyOk = checkProxyStatus();
            
            if (!authServiceOk || !storageOk || !proxyOk) {
                LOG.warn("深度检查发现问题，执行完整重新激活");
                forceFullReactivation();
            } else {
                LOG.info("✅ 深度检查通过，所有组件状态正常");
            }
            
        } catch (Exception e) {
            LOG.error("深度检查失败", e);
        }
    }
    
    /**
     * 检查认证绕过是否激活
     */
    private boolean isAuthBypassActive() {
        try {
            AuthService service = ApplicationManager.getApplication().getService(AuthService.class);

            // 检查服务是否为代理实例（动态代理）
            boolean isProxy = service != null && Proxy.isProxyClass(service.getClass());

            // 检查认证状态 - 使用便利方法
            boolean isAuthenticated = service != null && service.isAuthenticated();

            return isProxy && isAuthenticated;

        } catch (Exception e) {
            LOG.debug("检查绕过状态失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查 AuthService 状态
     */
    private boolean checkAuthServiceStatus() {
        try {
            AuthService service = ApplicationManager.getApplication().getService(AuthService.class);
            
            if (service == null) {
                LOG.warn("AuthService 不可用");
                return false;
            }
            
            if (!(service instanceof BypassAuthServiceProxy)) {
                LOG.warn("AuthService 不是代理实例: " + service.getClass().getSimpleName());
                return false;
            }
            
            if (!service.isAuthenticated()) { // 使用便利方法 isAuthenticated
                LOG.warn("AuthService 显示未认证状态");
                return false;
            }

            // 尝试获取访问令牌
            try {
                String token = service.getAccessToken(); // 使用便利方法 getAccessToken
                if (token == null || token.isEmpty()) {
                    LOG.warn("访问令牌为空");
                    return false;
                }
            } catch (Exception e) {
                LOG.warn("获取访问令牌失败: " + e.getMessage());
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            LOG.error("检查 AuthService 状态失败", e);
            return false;
        }
    }
    
    /**
     * 检查存储状态
     */
    private boolean checkStorageStatus() {
        try {
            return ReflectionBypassUtils.verifyAuthDataInjection();
        } catch (Exception e) {
            LOG.error("检查存储状态失败", e);
            return false;
        }
    }
    
    /**
     * 检查代理状态
     */
    private boolean checkProxyStatus() {
        try {
            AuthService service = ApplicationManager.getApplication().getService(AuthService.class);

            // 检查是否为动态代理
            if (service != null && Proxy.isProxyClass(service.getClass())) {
                // 尝试调用基本方法来验证代理是否工作
                try {
                    service.isAuthenticated(); // isAuthenticated
                    service.getAccessToken(); // getAccessToken
                    return true;
                } catch (Exception e) {
                    LOG.debug("代理方法调用失败: " + e.getMessage());
                    return false;
                }
            }

            return false;
        } catch (Exception e) {
            LOG.error("检查代理状态失败", e);
            return false;
        }
    }
    
    /**
     * 重新激活绕过
     */
    private void reactivateBypass() {
        try {
            reactivationCount.incrementAndGet();
            lastReactivationTime.set(System.currentTimeMillis());
            
            LOG.info("重新激活认证绕过... (第 " + reactivationCount.get() + " 次)");
            
            ZencoderAuthBypass bypass = ZencoderAuthBypass.getInstance();
            if (bypass.forceReactivate()) {
                LOG.info("✅ 认证绕过重新激活成功");
            } else {
                LOG.error("❌ 认证绕过重新激活失败");
            }
            
        } catch (Exception e) {
            LOG.error("重新激活绕过失败", e);
        }
    }
    
    /**
     * 强制完整重新激活
     */
    private void forceFullReactivation() {
        try {
            LOG.info("执行强制完整重新激活...");
            
            ZencoderAuthBypass bypass = ZencoderAuthBypass.getInstance();
            
            // 先停用
            bypass.deactivateBypass();
            
            // 等待2秒
            Thread.sleep(2000);
            
            // 重新激活
            if (bypass.activateBypass()) {
                LOG.info("✅ 强制完整重新激活成功");
                
                // 重新启动状态管理
                SmartAuthStateManager stateManager = new SmartAuthStateManager();
                stateManager.startContinuousMonitoring();
                
            } else {
                LOG.error("❌ 强制完整重新激活失败");
            }
            
        } catch (Exception e) {
            LOG.error("强制完整重新激活失败", e);
        }
    }
    
    /**
     * 生成健康报告
     */
    private void generateHealthReport() {
        try {
            StringBuilder report = new StringBuilder();
            report.append("\n=== Zencoder 绕过健康报告 ===\n");
            report.append("监控运行时间: ").append(getMonitorUptime()).append("\n");
            report.append("状态检查次数: ").append(checkCount.get()).append("\n");
            report.append("重新激活次数: ").append(reactivationCount.get()).append("\n");
            report.append("最后检查时间: ").append(formatTime(lastCheckTime.get())).append("\n");
            
            if (lastReactivationTime.get() > 0) {
                report.append("最后重新激活时间: ").append(formatTime(lastReactivationTime.get())).append("\n");
            }
            
            report.append("当前绕过状态: ").append(isAuthBypassActive() ? "✅ 正常" : "❌ 异常").append("\n");
            
            // 详细状态
            report.append("\n--- 详细状态 ---\n");
            report.append("AuthService: ").append(checkAuthServiceStatus() ? "✅" : "❌").append("\n");
            report.append("存储状态: ").append(checkStorageStatus() ? "✅" : "❌").append("\n");
            report.append("代理状态: ").append(checkProxyStatus() ? "✅" : "❌").append("\n");
            
            report.append("========================\n");
            
            LOG.info(report.toString());
            
        } catch (Exception e) {
            LOG.error("生成健康报告失败", e);
        }
    }
    
    /**
     * 获取监控启动时间
     */
    private long getMonitorStartTime() {
        // 简化实现，使用第一次检查时间
        return lastCheckTime.get() - (checkCount.get() * 30 * 1000L);
    }
    
    /**
     * 获取监控运行时间
     */
    private String getMonitorUptime() {
        long uptime = System.currentTimeMillis() - getMonitorStartTime();
        long hours = uptime / (1000 * 60 * 60);
        long minutes = (uptime % (1000 * 60 * 60)) / (1000 * 60);
        return hours + "小时" + minutes + "分钟";
    }
    
    /**
     * 格式化时间
     */
    private String formatTime(long timestamp) {
        if (timestamp == 0) return "从未";
        return new java.util.Date(timestamp).toString();
    }
    
    /**
     * 停止监控
     */
    public void shutdown() {
        monitoring = false;
        scheduler.shutdown();
        
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
        }
        
        LOG.info("绕过状态监控已停止");
    }
    
    /**
     * 获取监控统计信息
     */
    public String getMonitorStats() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("状态监控统计:\n");
            stats.append("- 监控状态: ").append(monitoring ? "运行中" : "已停止").append("\n");
            stats.append("- 检查次数: ").append(checkCount.get()).append("\n");
            stats.append("- 重新激活次数: ").append(reactivationCount.get()).append("\n");
            stats.append("- 运行时间: ").append(getMonitorUptime()).append("\n");
            stats.append("- 当前绕过状态: ").append(isAuthBypassActive() ? "正常" : "异常");
            
            return stats.toString();
        } catch (Exception e) {
            return "获取监控统计失败: " + e.getMessage();
        }
    }
    
    /**
     * 手动触发状态检查
     */
    public void triggerManualCheck() {
        LOG.info("手动触发状态检查");
        performStatusCheck();
    }
    
    /**
     * 手动触发深度检查
     */
    public void triggerDeepCheck() {
        LOG.info("手动触发深度检查");
        performDeepCheck();
    }

    /**
     * 检查监控是否正在运行
     */
    public boolean isMonitoring() {
        return monitoring && !scheduler.isShutdown();
    }
}
