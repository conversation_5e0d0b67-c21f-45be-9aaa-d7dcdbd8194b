package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.AuthService;
import ai.zencoder.plugin.auth.UserData;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;

/**
 * 绕过功能测试器
 * 用于测试和验证认证绕过功能
 */
public class BypassTester {
    private static final Logger LOG = Logger.getInstance(BypassTester.class);
    
    /**
     * 执行完整的绕过测试
     */
    public static boolean runFullTest() {
        LOG.info("=== 开始执行 Zencoder 绕过完整测试 ===");
        
        try {
            // 1. 测试数据生成
            if (!testDataGeneration()) {
                LOG.error("数据生成测试失败");
                return false;
            }
            
            // 2. 测试反射工具
            if (!testReflectionUtils()) {
                LOG.error("反射工具测试失败");
                return false;
            }
            
            // 3. 测试代理创建
            if (!testProxyCreation()) {
                LOG.error("代理创建测试失败");
                return false;
            }
            
            // 4. 测试绕过激活
            if (!testBypassActivation()) {
                LOG.error("绕过激活测试失败");
                return false;
            }
            
            // 5. 测试认证状态
            if (!testAuthenticationState()) {
                LOG.error("认证状态测试失败");
                return false;
            }
            
            LOG.info("✅ 所有测试通过！Zencoder 绕过功能正常工作");
            return true;
            
        } catch (Exception e) {
            LOG.error("测试过程中发生异常", e);
            return false;
        }
    }
    
    /**
     * 测试数据生成
     */
    private static boolean testDataGeneration() {
        LOG.info("测试数据生成...");
        
        try {
            // 测试 JWT 生成
            String jwt = FakeDataGenerator.generateFakeJWT();
            if (jwt == null || jwt.isEmpty() || !jwt.contains(".")) {
                LOG.error("JWT 生成失败");
                return false;
            }
            LOG.info("✅ JWT 生成成功: " + jwt.substring(0, Math.min(50, jwt.length())) + "...");
            
            // 测试用户数据生成
            UserData userData = FakeDataGenerator.createFakeUserData();
            if (userData == null || userData.getName() == null || userData.getId() == null) {
                LOG.error("用户数据生成失败");
                return false;
            }
            LOG.info("✅ 用户数据生成成功: " + userData.getName() + " (" + userData.getId() + ")");
            
            // 测试认证信息生成
            AuthInfo authInfo = FakeDataGenerator.createFakeAuthInfo();
            if (authInfo == null || authInfo.getAccessToken() == null || authInfo.getRefreshToken() == null) {
                LOG.error("认证信息生成失败");
                return false;
            }
            LOG.info("✅ 认证信息生成成功");
            
            // 验证数据有效性
            if (!FakeDataGenerator.validateFakeData(authInfo)) {
                LOG.error("生成的数据验证失败");
                return false;
            }
            LOG.info("✅ 数据验证通过");
            
            return true;
            
        } catch (Exception e) {
            LOG.error("数据生成测试失败", e);
            return false;
        }
    }
    
    /**
     * 测试反射工具
     */
    private static boolean testReflectionUtils() {
        LOG.info("测试反射工具...");
        
        try {
            // 测试认证数据注入
            AuthInfo testAuthInfo = FakeDataGenerator.createFakeAuthInfo();
            boolean injectionResult = ReflectionBypassUtils.injectAuthDataToStorage(testAuthInfo);
            LOG.info("认证数据注入结果: " + (injectionResult ? "成功" : "失败"));
            
            // 测试数据验证
            boolean verificationResult = ReflectionBypassUtils.verifyAuthDataInjection();
            LOG.info("数据注入验证结果: " + (verificationResult ? "成功" : "失败"));
            
            // 测试存储的数据获取
            AuthInfo storedAuth = ReflectionBypassUtils.getStoredAuthInfo();
            if (storedAuth != null) {
                LOG.info("✅ 成功获取存储的认证信息");
            } else {
                LOG.warn("⚠️ 未能获取存储的认证信息");
            }
            
            return true;
            
        } catch (Exception e) {
            LOG.error("反射工具测试失败", e);
            return false;
        }
    }
    
    /**
     * 测试代理创建
     */
    private static boolean testProxyCreation() {
        LOG.info("测试代理创建...");
        
        try {
            // 获取原始 AuthService
            AuthService originalService = ApplicationManager.getApplication().getService(AuthService.class);
            if (originalService == null) {
                LOG.warn("⚠️ 原始 AuthService 不可用，跳过代理测试");
                return true;
            }
            
            // 创建代理
            AuthService proxyService = BypassAuthServiceProxy.createProxy(originalService);
            if (proxyService == null) {
                LOG.error("代理创建失败");
                return false;
            }
            LOG.info("✅ 代理创建成功");
            
            // 测试代理方法调用
            try {
                boolean isAuth = proxyService.isAuthenticated(); // isAuthenticated
                LOG.info("代理认证状态: " + isAuth);

                String token = proxyService.getAccessToken(); // getAccessToken
                LOG.info("代理访问令牌: " + (token != null ? "已获取" : "null"));

                LOG.info("✅ 代理方法调用成功");

            } catch (Exception e) {
                LOG.error("代理方法调用失败", e);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            LOG.error("代理创建测试失败", e);
            return false;
        }
    }
    
    /**
     * 测试绕过激活
     */
    private static boolean testBypassActivation() {
        LOG.info("测试绕过激活...");
        
        try {
            ZencoderAuthBypass bypass = ZencoderAuthBypass.getInstance();
            if (bypass == null) {
                LOG.error("无法获取绕过实例");
                return false;
            }
            
            // 测试激活
            boolean activationResult = bypass.activateBypass();
            LOG.info("绕过激活结果: " + (activationResult ? "成功" : "失败"));
            
            // 检查激活状态
            boolean isActive = bypass.isBypassActive();
            LOG.info("绕过激活状态: " + (isActive ? "已激活" : "未激活"));
            
            // 获取统计信息
            String stats = bypass.getBypassStatistics();
            LOG.info("绕过统计信息:\n" + stats);
            
            return activationResult || isActive;
            
        } catch (Exception e) {
            LOG.error("绕过激活测试失败", e);
            return false;
        }
    }
    
    /**
     * 测试认证状态
     */
    private static boolean testAuthenticationState() {
        LOG.info("测试认证状态...");
        
        try {
            AuthService service = ApplicationManager.getApplication().getService(AuthService.class);
            if (service == null) {
                LOG.error("AuthService 不可用");
                return false;
            }
            
            // 测试认证状态
            boolean isAuthenticated = service.isAuthenticated(); // isAuthenticated
            LOG.info("当前认证状态: " + (isAuthenticated ? "已认证" : "未认证"));

            if (!isAuthenticated) {
                LOG.error("认证状态不正确");
                return false;
            }

            // 测试访问令牌获取
            try {
                String token = service.getAccessToken(); // getAccessToken
                if (token == null || token.isEmpty()) {
                    LOG.error("访问令牌为空");
                    return false;
                }
                LOG.info("✅ 访问令牌获取成功，长度: " + token.length());
            } catch (Exception e) {
                LOG.error("获取访问令牌失败", e);
                return false;
            }

            // 测试用户数据获取
            try {
                UserData userData = service.getUserDataOrNull(); // getUserData
                if (userData == null) {
                    LOG.error("用户数据为空");
                    return false;
                }
                LOG.info("✅ 用户数据获取成功: " + userData.getName());
            } catch (Exception e) {
                LOG.error("获取用户数据失败", e);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            LOG.error("认证状态测试失败", e);
            return false;
        }
    }
    
    /**
     * 快速状态检查
     */
    public static String quickStatusCheck() {
        try {
            StringBuilder status = new StringBuilder();
            status.append("=== Zencoder 绕过快速状态检查 ===\n");
            
            // 检查绕过实例
            ZencoderAuthBypass bypass = ZencoderAuthBypass.getInstance();
            status.append("绕过实例: ").append(bypass != null ? "✅ 可用" : "❌ 不可用").append("\n");
            
            if (bypass != null) {
                status.append("绕过状态: ").append(bypass.isBypassActive() ? "✅ 激活" : "❌ 未激活").append("\n");
            }
            
            // 检查 AuthService
            AuthService service = ApplicationManager.getApplication().getService(AuthService.class);
            status.append("AuthService: ").append(service != null ? "✅ 可用" : "❌ 不可用").append("\n");
            
            if (service != null) {
                try {
                    boolean isAuth = service.isAuthenticated();
                    status.append("认证状态: ").append(isAuth ? "✅ 已认证" : "❌ 未认证").append("\n");
                } catch (Exception e) {
                    status.append("认证状态: ❌ 检查失败 - ").append(e.getMessage()).append("\n");
                }
            }
            
            // 检查数据生成
            try {
                AuthInfo testAuth = FakeDataGenerator.createFakeAuthInfo();
                status.append("数据生成: ").append(testAuth != null ? "✅ 正常" : "❌ 失败").append("\n");
            } catch (Exception e) {
                status.append("数据生成: ❌ 失败 - ").append(e.getMessage()).append("\n");
            }
            
            status.append("=====================================");
            return status.toString();
            
        } catch (Exception e) {
            return "快速状态检查失败: " + e.getMessage();
        }
    }
}
