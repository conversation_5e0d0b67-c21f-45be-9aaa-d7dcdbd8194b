package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.UserData;
import com.intellij.openapi.diagnostic.Logger;

import java.util.*;

/**
 * 伪造数据生成器
 * 生成符合 Zencoder 预期格式的认证数据
 */
public class FakeDataGenerator {
    private static final Logger LOG = Logger.getInstance(FakeDataGenerator.class);
    
    // 用户信息模板
    private static final String[] USER_NAMES = {
        "Zencoder Bypass User",
        "Premium Developer",
        "Code Assistant User",
        "AI Developer"
    };
    
    private static final String[] EMAIL_DOMAINS = {
        "bypass.local",
        "zencoder.dev",
        "premium.local",
        "developer.ai"
    };
    
    /**
     * 生成伪造的 JWT 访问令牌
     */
    public static String generateFakeJWT() {
        try {
            // JWT Header
            Map<String, Object> header = new HashMap<>();
            header.put("alg", "HS256");
            header.put("typ", "JWT");
            
            String headerJson = mapToJson(header);
            String encodedHeader = Base64.getUrlEncoder().withoutPadding()
                .encodeToString(headerJson.getBytes());
            
            // JWT Payload - 包含用户信息
            Map<String, Object> payload = createJWTPayload();
            String payloadJson = mapToJson(payload);
            String encodedPayload = Base64.getUrlEncoder().withoutPadding()
                .encodeToString(payloadJson.getBytes());
            
            // JWT Signature (伪造但格式正确)
            String signature = generateFakeSignature();
            
            String jwt = encodedHeader + "." + encodedPayload + "." + signature;
            LOG.debug("生成伪造JWT: " + jwt.substring(0, Math.min(50, jwt.length())) + "...");
            
            return jwt;
            
        } catch (Exception e) {
            LOG.error("生成伪造JWT失败", e);
            // 返回一个基础的伪造JWT
            return createBasicFakeJWT();
        }
    }
    
    /**
     * 创建JWT载荷
     */
    private static Map<String, Object> createJWTPayload() {
        Map<String, Object> payload = new HashMap<>();
        
        String userId = "bypass_user_" + System.currentTimeMillis();
        String userName = USER_NAMES[new Random().nextInt(USER_NAMES.length)];
        String userEmail = generateEmail();
        
        // 标准JWT字段
        payload.put("sub", userId);
        payload.put("iss", "zencoder-bypass");
        payload.put("aud", "zencoder-client");
        payload.put("iat", System.currentTimeMillis() / 1000);
        payload.put("exp", (System.currentTimeMillis() / 1000) + 86400); // 24小时后过期
        payload.put("nbf", System.currentTimeMillis() / 1000);
        payload.put("jti", UUID.randomUUID().toString());
        
        // Zencoder特定字段
        payload.put("id", userId);
        payload.put("name", userName);
        payload.put("email", userEmail);
        payload.put("permissions", Arrays.asList("read", "write", "admin", "premium"));
        payload.put("zencoderRoles", Arrays.asList("premium_user", "developer", "enterprise"));
        
        // 自定义声明
        Map<String, Object> customClaims = new HashMap<>();
        customClaims.put("plan", "enterprise");
        customClaims.put("features", Arrays.asList("chat", "code_completion", "refactoring", "analysis"));
        customClaims.put("limits", createLimitsMap());
        payload.put("customClaims", customClaims);
        
        return payload;
    }
    
    /**
     * 创建限制映射
     */
    private static Map<String, Object> createLimitsMap() {
        Map<String, Object> limits = new HashMap<>();
        limits.put("dailyRequests", 10000);
        limits.put("monthlyRequests", 300000);
        limits.put("concurrentSessions", 10);
        limits.put("maxFileSize", 10485760); // 10MB
        return limits;
    }
    
    /**
     * 生成伪造签名
     */
    private static String generateFakeSignature() {
        // 生成一个看起来像真实签名的随机字符串
        byte[] randomBytes = new byte[32];
        new Random().nextBytes(randomBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
    }
    
    /**
     * 创建基础伪造JWT（备用方案）
     */
    private static String createBasicFakeJWT() {
        String header = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9";
        String payload = Base64.getUrlEncoder().withoutPadding()
            .encodeToString(("{\"sub\":\"bypass_user\",\"name\":\"Bypass User\",\"email\":\"bypass@local\"}").getBytes());
        String signature = "fake_signature_" + System.currentTimeMillis();
        
        return header + "." + payload + "." + signature;
    }
    
    /**
     * 生成邮箱地址
     */
    private static String generateEmail() {
        String domain = EMAIL_DOMAINS[new Random().nextInt(EMAIL_DOMAINS.length)];
        String username = "user" + System.currentTimeMillis();
        return username + "@" + domain;
    }
    
    /**
     * 创建伪造的用户数据
     */
    public static UserData createFakeUserData() {
        try {
            String userId = "bypass_user_" + System.currentTimeMillis();
            String userName = USER_NAMES[new Random().nextInt(USER_NAMES.length)];
            String userEmail = generateEmail();
            
            List<String> permissions = Arrays.asList(
                "read", "write", "admin", "premium", 
                "chat", "code_completion", "refactoring"
            );
            
            List<String> zencoderRoles = Arrays.asList(
                "premium_user", "developer", "enterprise", "beta_tester"
            );
            
            // 创建自定义声明
            UserData.CustomClaims customClaims = createCustomClaims();
            
            UserData userData = new UserData(userId, userName, userEmail, permissions, zencoderRoles, customClaims);
            
            LOG.debug("创建伪造用户数据: " + userName + " (" + userEmail + ")");
            return userData;
            
        } catch (Exception e) {
            LOG.error("创建伪造用户数据失败", e);
            // 返回基础用户数据
            return createBasicUserData();
        }
    }
    
    /**
     * 创建自定义声明
     */
    private static UserData.CustomClaims createCustomClaims() {
        try {
            // 尝试通过反射创建 CustomClaims
            Class<?> customClaimsClass = UserData.CustomClaims.class;
            
            // 查找构造函数
            try {
                return (UserData.CustomClaims) customClaimsClass.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                // 如果默认构造函数不可用，返回null
                return null;
            }
        } catch (Exception e) {
            LOG.debug("创建自定义声明失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 创建基础用户数据（备用方案）
     */
    private static UserData createBasicUserData() {
        return new UserData(
            "bypass_user_basic",
            "Bypass User",
            "bypass@local",
            Arrays.asList("read", "write"),
            Arrays.asList("user"),
            null
        );
    }
    
    /**
     * 创建伪造的认证信息
     */
    public static AuthInfo createFakeAuthInfo() {
        try {
            String accessToken = generateFakeJWT();
            String refreshToken = generateRefreshToken();
            
            AuthInfo authInfo = new AuthInfo(accessToken, refreshToken);
            
            LOG.debug("创建伪造认证信息，访问令牌长度: " + accessToken.length());
            return authInfo;
            
        } catch (Exception e) {
            LOG.error("创建伪造认证信息失败", e);
            // 返回基础认证信息
            return createBasicAuthInfo();
        }
    }
    
    /**
     * 生成刷新令牌
     */
    private static String generateRefreshToken() {
        return "refresh_" + UUID.randomUUID().toString().replace("-", "") + "_" + System.currentTimeMillis();
    }
    
    /**
     * 创建基础认证信息（备用方案）
     */
    private static AuthInfo createBasicAuthInfo() {
        String basicJWT = createBasicFakeJWT();
        String basicRefresh = "refresh_basic_" + System.currentTimeMillis();
        return new AuthInfo(basicJWT, basicRefresh);
    }
    
    /**
     * 生成随机用户ID
     */
    public static String generateUserId() {
        return "bypass_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
    
    /**
     * 生成随机用户名
     */
    public static String generateUserName() {
        return USER_NAMES[new Random().nextInt(USER_NAMES.length)] + "_" + 
               (System.currentTimeMillis() % 10000);
    }
    
    /**
     * 验证生成的数据是否有效
     */
    public static boolean validateFakeData(AuthInfo authInfo) {
        try {
            if (authInfo == null) return false;
            if (authInfo.getAccessToken() == null || authInfo.getAccessToken().isEmpty()) return false;
            if (authInfo.getRefreshToken() == null || authInfo.getRefreshToken().isEmpty()) return false;
            if (authInfo.getUserData() == null) return false;
            
            UserData userData = authInfo.getUserData();
            if (userData.getId() == null || userData.getId().isEmpty()) return false;
            if (userData.getName() == null || userData.getName().isEmpty()) return false;
            if (userData.getEmail() == null || userData.getEmail().isEmpty()) return false;
            
            return true;
        } catch (Exception e) {
            LOG.error("验证伪造数据失败", e);
            return false;
        }
    }
    
    /**
     * 简单的 Map 到 JSON 转换
     */
    private static String mapToJson(Map<String, Object> map) {
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!first) {
                json.append(",");
            }
            json.append("\"").append(entry.getKey()).append("\":");

            Object value = entry.getValue();
            if (value instanceof String) {
                json.append("\"").append(value).append("\"");
            } else if (value instanceof List) {
                json.append("[");
                List<?> list = (List<?>) value;
                for (int i = 0; i < list.size(); i++) {
                    if (i > 0) json.append(",");
                    json.append("\"").append(list.get(i)).append("\"");
                }
                json.append("]");
            } else if (value instanceof Map) {
                json.append(mapToJson((Map<String, Object>) value));
            } else {
                json.append(value);
            }
            first = false;
        }
        json.append("}");
        return json.toString();
    }

    /**
     * 获取数据生成统计信息
     */
    public static String getGenerationStats() {
        return String.format(
            "数据生成器状态:\n" +
            "- 支持的用户名模板: %d\n" +
            "- 支持的邮箱域名: %d\n" +
            "- JWT生成: 可用\n" +
            "- 用户数据生成: 可用\n" +
            "- 认证信息生成: 可用",
            USER_NAMES.length,
            EMAIL_DOMAINS.length
        );
    }
}
