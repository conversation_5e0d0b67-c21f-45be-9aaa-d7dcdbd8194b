package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.config.FileTokenStorage;
import ai.zencoder.plugin.config.TokenStorage;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 反射绕过工具类
 * 提供认证数据注入和服务操作功能
 */
public class ReflectionBypassUtils {
    private static final Logger LOG = Logger.getInstance(ReflectionBypassUtils.class);
    
    /**
     * 注入认证数据到 TokenStorage
     */
    public static boolean injectAuthDataToStorage(AuthInfo authInfo) {
        try {
            LOG.info("开始注入认证数据到存储...");
            
            // 策略1: 注入到 FileTokenStorage
            boolean fileStorageSuccess = injectToFileTokenStorage(authInfo);
            
            // 策略2: 注入到 SecureTokensStorage
            boolean secureStorageSuccess = injectToSecureTokenStorage(authInfo);
            
            // 策略3: 注入到其他可能的存储实现
            boolean otherStorageSuccess = injectToOtherStorages(authInfo);
            
            boolean success = fileStorageSuccess || secureStorageSuccess || otherStorageSuccess;
            
            if (success) {
                LOG.info("认证数据注入成功");
            } else {
                LOG.warn("认证数据注入失败，但继续执行");
            }
            
            return success;
            
        } catch (Exception e) {
            LOG.error("注入认证数据失败", e);
            return false;
        }
    }
    
    /**
     * 注入到 FileTokenStorage
     */
    private static boolean injectToFileTokenStorage(AuthInfo authInfo) {
        try {
            // 获取 FileTokenStorage 实例
            TokenStorage fileStorage = getFileTokenStorageInstance();
            if (fileStorage != null) {
                fileStorage.storeAuthInfo(authInfo);
                LOG.debug("FileTokenStorage 注入成功");
                return true;
            }
            return false;
        } catch (Exception e) {
            LOG.debug("FileTokenStorage 注入失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 注入到 SecureTokensStorage
     */
    private static boolean injectToSecureTokenStorage(AuthInfo authInfo) {
        try {
            // 查找 SecureTokensStorage 的实现类
            String[] possibleClasses = {
                "ai.zencoder.plugin.auth.ZencoderTokensStorage",
                "ai.zencoder.plugin.auth.DefaultSecureTokensStorage",
                "ai.zencoder.plugin.config.SecureTokensStorageImpl"
            };
            
            for (String className : possibleClasses) {
                if (injectToStorageClass(className, authInfo)) {
                    LOG.debug("SecureTokensStorage 注入成功: " + className);
                    return true;
                }
            }
            
            return false;
        } catch (Exception e) {
            LOG.debug("SecureTokensStorage 注入失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 注入到其他存储实现
     */
    private static boolean injectToOtherStorages(AuthInfo authInfo) {
        try {
            // 尝试通过应用服务获取 TokenStorage
            TokenStorage appStorage = ApplicationManager.getApplication().getService(TokenStorage.class);
            if (appStorage != null) {
                appStorage.storeAuthInfo(authInfo);
                LOG.debug("应用级 TokenStorage 注入成功");
                return true;
            }
            
            return false;
        } catch (Exception e) {
            LOG.debug("其他存储注入失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取 FileTokenStorage 实例
     */
    public static TokenStorage getFileTokenStorageInstance() {
        try {
            // 尝试通过 Companion 获取实例
            Class<?> fileStorageClass = FileTokenStorage.class;

            // 查找 Companion 字段
            Field companionField = fileStorageClass.getDeclaredField("Companion");
            if (companionField != null) {
                companionField.setAccessible(true);
                Object companion = companionField.get(null);

                // 调用 getInstance 方法
                Method getInstanceMethod = companion.getClass().getDeclaredMethod("a"); // 混淆的方法名
                if (getInstanceMethod != null) {
                    getInstanceMethod.setAccessible(true);
                    return (TokenStorage) getInstanceMethod.invoke(companion);
                }
            }

            // 备用方案：直接创建实例
            return (TokenStorage) fileStorageClass.getDeclaredConstructor().newInstance();

        } catch (Exception e) {
            LOG.debug("获取 FileTokenStorage 实例失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 注入到指定存储类
     */
    private static boolean injectToStorageClass(String className, AuthInfo authInfo) {
        try {
            Class<?> storageClass = Class.forName(className);
            Object instance = storageClass.getDeclaredConstructor().newInstance();
            
            if (instance instanceof TokenStorage) {
                ((TokenStorage) instance).storeAuthInfo(authInfo);
                return true;
            }
            
            // 尝试通过反射调用 storeAuthInfo 方法
            Method storeMethod = storageClass.getMethod("storeAuthInfo", AuthInfo.class);
            if (storeMethod != null) {
                storeMethod.invoke(instance, authInfo);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            LOG.debug("注入到存储类失败: " + className + " - " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 创建伪造的 AuthInfo 对象
     */
    public static AuthInfo createFakeAuthInfo(String accessToken, String refreshToken) {
        try {
            return new AuthInfo(accessToken, refreshToken);
        } catch (Exception e) {
            LOG.error("创建伪造 AuthInfo 失败", e);
            return null;
        }
    }
    
    /**
     * 创建伪造的 UserData 对象
     */
    public static Object createFakeUserData(String userId, String userName, String userEmail) {
        try {
            return FakeDataGenerator.createFakeUserData();
        } catch (Exception e) {
            LOG.error("创建伪造 UserData 失败", e);
            return null;
        }
    }
    
    /**
     * 获取当前 UserData
     */
    public static Object getCurrentUserData() {
        try {
            // 尝试从当前 AuthService 获取
            ai.zencoder.plugin.auth.AuthService authService =
                ApplicationManager.getApplication().getService(ai.zencoder.plugin.auth.AuthService.class);

            if (authService != null && authService.isAuthenticated()) { // 使用便利方法
                return authService.getUserDataOrNull(); // 使用便利方法
            }

            return null;
        } catch (Exception e) {
            LOG.debug("获取当前 UserData 失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 注入 AuthInfo 到 AuthService
     */
    public static boolean injectAuthInfo(Object authInfo) {
        try {
            // 这个方法主要用于兼容现有的绕过代码
            if (authInfo instanceof AuthInfo) {
                return injectAuthDataToStorage((AuthInfo) authInfo);
            }
            return false;
        } catch (Exception e) {
            LOG.error("注入 AuthInfo 失败", e);
            return false;
        }
    }
    
    /**
     * 清除所有存储的认证信息
     */
    public static boolean clearAllAuthData() {
        try {
            boolean success = false;
            
            // 清除 FileTokenStorage
            TokenStorage fileStorage = getFileTokenStorageInstance();
            if (fileStorage != null) {
                fileStorage.cleanAuthInfo();
                success = true;
            }
            
            // 清除其他存储
            try {
                TokenStorage appStorage = ApplicationManager.getApplication().getService(TokenStorage.class);
                if (appStorage != null) {
                    appStorage.cleanAuthInfo();
                    success = true;
                }
            } catch (Exception e) {
                LOG.debug("清除应用存储失败: " + e.getMessage());
            }
            
            LOG.info("认证数据清除" + (success ? "成功" : "失败"));
            return success;
            
        } catch (Exception e) {
            LOG.error("清除认证数据失败", e);
            return false;
        }
    }
    
    /**
     * 验证认证数据是否已注入
     */
    public static boolean verifyAuthDataInjection() {
        try {
            // 检查 FileTokenStorage
            TokenStorage fileStorage = getFileTokenStorageInstance();
            if (fileStorage != null) {
                AuthInfo authInfo = fileStorage.loadAuthInfo();
                if (authInfo != null && authInfo.getAccessToken() != null) {
                    LOG.debug("FileTokenStorage 中存在认证数据");
                    return true;
                }
            }
            
            // 检查应用级存储
            try {
                TokenStorage appStorage = ApplicationManager.getApplication().getService(TokenStorage.class);
                if (appStorage != null) {
                    AuthInfo authInfo = appStorage.loadAuthInfo();
                    if (authInfo != null && authInfo.getAccessToken() != null) {
                        LOG.debug("应用级存储中存在认证数据");
                        return true;
                    }
                }
            } catch (Exception e) {
                LOG.debug("检查应用存储失败: " + e.getMessage());
            }
            
            return false;
        } catch (Exception e) {
            LOG.error("验证认证数据注入失败", e);
            return false;
        }
    }
    
    /**
     * 获取存储的认证信息
     */
    public static AuthInfo getStoredAuthInfo() {
        try {
            // 从 FileTokenStorage 获取
            TokenStorage fileStorage = getFileTokenStorageInstance();
            if (fileStorage != null) {
                AuthInfo authInfo = fileStorage.loadAuthInfo();
                if (authInfo != null) {
                    return authInfo;
                }
            }
            
            // 从应用级存储获取
            try {
                TokenStorage appStorage = ApplicationManager.getApplication().getService(TokenStorage.class);
                if (appStorage != null) {
                    return appStorage.loadAuthInfo();
                }
            } catch (Exception e) {
                LOG.debug("从应用存储获取失败: " + e.getMessage());
            }
            
            return null;
        } catch (Exception e) {
            LOG.error("获取存储的认证信息失败", e);
            return null;
        }
    }
    
    /**
     * 强制刷新存储的认证数据
     */
    public static boolean forceRefreshStoredAuth() {
        try {
            // 清除现有数据
            clearAllAuthData();
            
            // 等待一秒
            Thread.sleep(1000);
            
            // 注入新数据
            AuthInfo newAuthInfo = FakeDataGenerator.createFakeAuthInfo();
            return injectAuthDataToStorage(newAuthInfo);
            
        } catch (Exception e) {
            LOG.error("强制刷新存储认证数据失败", e);
            return false;
        }
    }
    
    /**
     * 获取反射绕过统计信息
     */
    public static String getBypassStats() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("反射绕过统计:\n");
            stats.append("- 认证数据注入: ").append(verifyAuthDataInjection() ? "成功" : "失败").append("\n");
            stats.append("- FileTokenStorage: ").append(getFileTokenStorageInstance() != null ? "可用" : "不可用").append("\n");
            
            AuthInfo storedAuth = getStoredAuthInfo();
            stats.append("- 存储的认证信息: ").append(storedAuth != null ? "存在" : "不存在").append("\n");
            
            if (storedAuth != null) {
                stats.append("- 访问令牌长度: ").append(storedAuth.getAccessToken().length()).append("\n");
                stats.append("- 用户数据: ").append(storedAuth.getUserData() != null ? "存在" : "不存在").append("\n");
            }
            
            return stats.toString();
        } catch (Exception e) {
            return "获取统计信息失败: " + e.getMessage();
        }
    }
}
