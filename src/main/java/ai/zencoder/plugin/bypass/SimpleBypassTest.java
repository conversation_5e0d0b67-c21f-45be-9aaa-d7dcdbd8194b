package ai.zencoder.plugin.bypass;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 简化的绕过测试类
 * 用于验证核心逻辑而不依赖 IntelliJ Platform
 */
public class SimpleBypassTest {
    
    public static void main(String[] args) {
        System.out.println("=== Zencoder 认证绕过核心逻辑测试 ===");
        
        boolean allTestsPassed = true;
        
        // 测试 1: JWT 生成
        System.out.println("\n1. 测试 JWT 生成...");
        if (testJWTGeneration()) {
            System.out.println("✅ JWT 生成测试通过");
        } else {
            System.out.println("❌ JWT 生成测试失败");
            allTestsPassed = false;
        }
        
        // 测试 2: 用户数据生成
        System.out.println("\n2. 测试用户数据生成...");
        if (testUserDataGeneration()) {
            System.out.println("✅ 用户数据生成测试通过");
        } else {
            System.out.println("❌ 用户数据生成测试失败");
            allTestsPassed = false;
        }
        
        // 测试 3: 认证信息生成
        System.out.println("\n3. 测试认证信息生成...");
        if (testAuthInfoGeneration()) {
            System.out.println("✅ 认证信息生成测试通过");
        } else {
            System.out.println("❌ 认证信息生成测试失败");
            allTestsPassed = false;
        }
        
        // 测试 4: 动态代理创建
        System.out.println("\n4. 测试动态代理创建...");
        if (testProxyCreation()) {
            System.out.println("✅ 动态代理创建测试通过");
        } else {
            System.out.println("❌ 动态代理创建测试失败");
            allTestsPassed = false;
        }
        
        // 测试 5: 反射工具
        System.out.println("\n5. 测试反射工具...");
        if (testReflectionUtils()) {
            System.out.println("✅ 反射工具测试通过");
        } else {
            System.out.println("❌ 反射工具测试失败");
            allTestsPassed = false;
        }
        
        // 总结
        System.out.println("\n" + "=".repeat(50));
        if (allTestsPassed) {
            System.out.println("🎉 所有核心逻辑测试通过！");
            System.out.println("Zencoder 认证绕过的核心功能已准备就绪。");
        } else {
            System.out.println("⚠️ 部分测试失败，需要检查和修复。");
        }
        System.out.println("=".repeat(50));
    }
    
    /**
     * 测试 JWT 生成
     */
    private static boolean testJWTGeneration() {
        try {
            String jwt = generateSimpleJWT();
            
            if (jwt == null || jwt.isEmpty()) {
                System.out.println("   - JWT 为空");
                return false;
            }
            
            if (!jwt.contains(".")) {
                System.out.println("   - JWT 格式不正确，缺少分隔符");
                return false;
            }
            
            String[] parts = jwt.split("\\.");
            if (parts.length != 3) {
                System.out.println("   - JWT 应该有3个部分，实际有: " + parts.length);
                return false;
            }
            
            System.out.println("   - JWT 生成成功: " + jwt.substring(0, Math.min(50, jwt.length())) + "...");
            System.out.println("   - JWT 长度: " + jwt.length());
            
            return true;
            
        } catch (Exception e) {
            System.out.println("   - JWT 生成异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试用户数据生成
     */
    private static boolean testUserDataGeneration() {
        try {
            Map<String, Object> userData = generateSimpleUserData();
            
            if (userData == null || userData.isEmpty()) {
                System.out.println("   - 用户数据为空");
                return false;
            }
            
            String[] requiredFields = {"id", "name", "email", "permissions", "zencoderRoles"};
            for (String field : requiredFields) {
                if (!userData.containsKey(field)) {
                    System.out.println("   - 缺少必需字段: " + field);
                    return false;
                }
            }
            
            System.out.println("   - 用户ID: " + userData.get("id"));
            System.out.println("   - 用户名: " + userData.get("name"));
            System.out.println("   - 邮箱: " + userData.get("email"));
            System.out.println("   - 权限数量: " + ((List<?>) userData.get("permissions")).size());
            
            return true;
            
        } catch (Exception e) {
            System.out.println("   - 用户数据生成异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试认证信息生成
     */
    private static boolean testAuthInfoGeneration() {
        try {
            Map<String, Object> authInfo = generateSimpleAuthInfo();
            
            if (authInfo == null || authInfo.isEmpty()) {
                System.out.println("   - 认证信息为空");
                return false;
            }
            
            if (!authInfo.containsKey("accessToken") || !authInfo.containsKey("refreshToken")) {
                System.out.println("   - 缺少必需的令牌字段");
                return false;
            }
            
            String accessToken = (String) authInfo.get("accessToken");
            String refreshToken = (String) authInfo.get("refreshToken");
            
            if (accessToken == null || accessToken.isEmpty()) {
                System.out.println("   - 访问令牌为空");
                return false;
            }
            
            if (refreshToken == null || refreshToken.isEmpty()) {
                System.out.println("   - 刷新令牌为空");
                return false;
            }
            
            System.out.println("   - 访问令牌长度: " + accessToken.length());
            System.out.println("   - 刷新令牌长度: " + refreshToken.length());
            
            return true;
            
        } catch (Exception e) {
            System.out.println("   - 认证信息生成异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试动态代理创建
     */
    private static boolean testProxyCreation() {
        try {
            // 创建一个简单的接口和实现
            TestAuthService originalService = new TestAuthServiceImpl();
            
            // 创建动态代理
            TestAuthService proxyService = createSimpleProxy(originalService);
            
            if (proxyService == null) {
                System.out.println("   - 代理创建失败");
                return false;
            }
            
            // 测试代理方法调用
            boolean isAuth = proxyService.isAuthenticated();
            String token = proxyService.getAccessToken();
            
            if (!isAuth) {
                System.out.println("   - 代理认证状态不正确");
                return false;
            }
            
            if (token == null || token.isEmpty()) {
                System.out.println("   - 代理访问令牌为空");
                return false;
            }
            
            System.out.println("   - 代理认证状态: " + isAuth);
            System.out.println("   - 代理访问令牌: " + token.substring(0, Math.min(20, token.length())) + "...");
            
            return true;
            
        } catch (Exception e) {
            System.out.println("   - 动态代理创建异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试反射工具
     */
    private static boolean testReflectionUtils() {
        try {
            // 测试字段查找
            TestClass testObj = new TestClass();
            
            boolean fieldFound = findFieldInClass(testObj.getClass(), "testField");
            if (!fieldFound) {
                System.out.println("   - 字段查找失败");
                return false;
            }
            
            // 测试方法查找
            boolean methodFound = findMethodInClass(testObj.getClass(), "testMethod");
            if (!methodFound) {
                System.out.println("   - 方法查找失败");
                return false;
            }
            
            System.out.println("   - 字段查找: 成功");
            System.out.println("   - 方法查找: 成功");
            
            return true;
            
        } catch (Exception e) {
            System.out.println("   - 反射工具测试异常: " + e.getMessage());
            return false;
        }
    }
    
    // 辅助方法
    
    private static String generateSimpleJWT() {
        String header = Base64.getEncoder().encodeToString("{\"alg\":\"HS256\",\"typ\":\"JWT\"}".getBytes());
        String payload = Base64.getEncoder().encodeToString("{\"sub\":\"test_user\",\"name\":\"Test User\"}".getBytes());
        String signature = Base64.getEncoder().encodeToString("fake_signature".getBytes());
        
        return header + "." + payload + "." + signature;
    }
    
    private static Map<String, Object> generateSimpleUserData() {
        Map<String, Object> userData = new HashMap<>();
        userData.put("id", "test_user_" + System.currentTimeMillis());
        userData.put("name", "Test Bypass User");
        userData.put("email", "<EMAIL>");
        userData.put("permissions", Arrays.asList("read", "write", "admin"));
        userData.put("zencoderRoles", Arrays.asList("premium_user"));
        
        return userData;
    }
    
    private static Map<String, Object> generateSimpleAuthInfo() {
        Map<String, Object> authInfo = new HashMap<>();
        authInfo.put("accessToken", generateSimpleJWT());
        authInfo.put("refreshToken", "refresh_" + UUID.randomUUID().toString());
        
        return authInfo;
    }
    
    private static TestAuthService createSimpleProxy(TestAuthService original) {
        return (TestAuthService) java.lang.reflect.Proxy.newProxyInstance(
            TestAuthService.class.getClassLoader(),
            new Class<?>[]{TestAuthService.class},
            (proxy, method, args) -> {
                switch (method.getName()) {
                    case "isAuthenticated":
                        return true;
                    case "getAccessToken":
                        return generateSimpleJWT();
                    default:
                        return method.invoke(original, args);
                }
            }
        );
    }
    
    private static boolean findFieldInClass(Class<?> clazz, String fieldName) {
        try {
            clazz.getDeclaredField(fieldName);
            return true;
        } catch (NoSuchFieldException e) {
            return false;
        }
    }
    
    private static boolean findMethodInClass(Class<?> clazz, String methodName) {
        try {
            clazz.getDeclaredMethod(methodName);
            return true;
        } catch (NoSuchMethodException e) {
            return false;
        }
    }
    
    // 测试接口和类
    
    interface TestAuthService {
        boolean isAuthenticated();
        String getAccessToken();
    }
    
    static class TestAuthServiceImpl implements TestAuthService {
        @Override
        public boolean isAuthenticated() {
            return false;
        }
        
        @Override
        public String getAccessToken() {
            return "original_token";
        }
    }
    
    static class TestClass {
        private String testField = "test";
        
        public void testMethod() {
            // Test method
        }
    }
}
