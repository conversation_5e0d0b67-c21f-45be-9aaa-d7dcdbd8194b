package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.config.TokenStorage;
import ai.zencoder.plugin.observers.auth.AuthObserver;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.Map;

/**
 * 智能认证状态管理器
 * 确保所有组件都认为用户已认证
 */
@Service
public final class SmartAuthStateManager {
    private static final Logger LOG = Logger.getInstance(SmartAuthStateManager.class);
    private final Map<String, Object> stateCache = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    /**
     * 智能状态同步 - 确保所有组件都认为用户已认证
     */
    public void synchronizeAuthState(AuthInfo authInfo) {
        try {
            LOG.info("开始智能认证状态同步...");
            
            // 1. 更新 AuthObserver
            updateAuthObserver(authInfo);
            
            // 2. 更新 TokenStorage
            updateTokenStorage(authInfo);
            
            // 3. 更新 WebView 状态
            updateWebViewState(authInfo);
            
            // 4. 更新聊天服务状态
            updateChatServiceState(authInfo);
            
            // 5. 更新连接管理器
            updateConnectionManager(authInfo);
            
            // 6. 更新其他相关组件
            updateOtherComponents(authInfo);
            
            LOG.info("智能认证状态同步完成");
            
        } catch (Exception e) {
            LOG.error("智能状态同步失败", e);
        }
    }
    
    /**
     * 更新 AuthObserver
     */
    private void updateAuthObserver(AuthInfo authInfo) {
        try {
            AuthObserver observer = ApplicationManager.getApplication().getService(AuthObserver.class);
            if (observer != null) {
                observer.signIn(authInfo); // 使用便利方法
                LOG.debug("AuthObserver 状态已更新");
            } else {
                LOG.warn("AuthObserver 服务不可用");
            }
        } catch (Exception e) {
            LOG.warn("更新 AuthObserver 失败", e);
        }
    }
    
    /**
     * 更新 TokenStorage
     */
    private void updateTokenStorage(AuthInfo authInfo) {
        try {
            // 更新 FileTokenStorage
            updateFileTokenStorage(authInfo);
            
            // 更新 SecureTokensStorage (如果存在)
            updateSecureTokenStorage(authInfo);
            
            // 更新应用级 TokenStorage
            updateApplicationTokenStorage(authInfo);
            
        } catch (Exception e) {
            LOG.warn("更新 TokenStorage 失败", e);
        }
    }
    
    /**
     * 更新 FileTokenStorage
     */
    private void updateFileTokenStorage(AuthInfo authInfo) {
        try {
            TokenStorage fileStorage = ReflectionBypassUtils.getFileTokenStorageInstance();
            if (fileStorage != null) {
                fileStorage.storeAuthInfo(authInfo);
                LOG.debug("FileTokenStorage 已更新");
            }
        } catch (Exception e) {
            LOG.debug("更新 FileTokenStorage 失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新 SecureTokensStorage
     */
    private void updateSecureTokenStorage(AuthInfo authInfo) {
        try {
            // 查找 SecureTokensStorage 的实现类
            String[] possibleClasses = {
                "ai.zencoder.plugin.auth.ZencoderTokensStorage",
                "ai.zencoder.plugin.auth.DefaultSecureTokensStorage"
            };
            
            for (String className : possibleClasses) {
                if (updateStorageByClassName(className, authInfo)) {
                    LOG.debug("SecureTokensStorage 已更新: " + className);
                    break;
                }
            }
            
        } catch (Exception e) {
            LOG.debug("SecureTokensStorage 不可用或更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新应用级 TokenStorage
     */
    private void updateApplicationTokenStorage(AuthInfo authInfo) {
        try {
            TokenStorage appStorage = ApplicationManager.getApplication().getService(TokenStorage.class);
            if (appStorage != null) {
                appStorage.storeAuthInfo(authInfo);
                LOG.debug("应用级 TokenStorage 已更新");
            }
        } catch (Exception e) {
            LOG.debug("更新应用级 TokenStorage 失败: " + e.getMessage());
        }
    }
    
    /**
     * 通过类名更新存储
     */
    private boolean updateStorageByClassName(String className, AuthInfo authInfo) {
        try {
            Class<?> storageClass = Class.forName(className);
            Object instance = storageClass.getDeclaredConstructor().newInstance();
            
            if (instance instanceof TokenStorage) {
                ((TokenStorage) instance).storeAuthInfo(authInfo);
                return true;
            }
            
            // 尝试通过反射调用
            Method storeMethod = storageClass.getMethod("storeAuthInfo", AuthInfo.class);
            storeMethod.invoke(instance, authInfo);
            return true;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 更新 WebView 状态
     */
    private void updateWebViewState(AuthInfo authInfo) {
        try {
            Project[] projects = ProjectManager.getInstance().getOpenProjects();
            for (Project project : projects) {
                updateProjectWebViewState(project, authInfo);
            }
        } catch (Exception e) {
            LOG.warn("更新 WebView 状态失败", e);
        }
    }
    
    /**
     * 更新项目 WebView 状态
     */
    private void updateProjectWebViewState(Project project, AuthInfo authInfo) {
        try {
            // 查找 ChatWebviewManager
            String[] webviewClasses = {
                "ai.zencoder.plugin.webview.chat.ChatWebviewManager",
                "ai.zencoder.plugin.webview.WebviewManager"
            };
            
            for (String className : webviewClasses) {
                if (updateComponentByClassName(project, className, "onAuthStateChanged", authInfo)) {
                    LOG.debug("项目 WebView 状态已更新: " + project.getName());
                    break;
                }
            }
            
        } catch (Exception e) {
            LOG.debug("更新项目 WebView 状态失败: " + project.getName() + " - " + e.getMessage());
        }
    }
    
    /**
     * 更新聊天服务状态
     */
    private void updateChatServiceState(AuthInfo authInfo) {
        try {
            Project[] projects = ProjectManager.getInstance().getOpenProjects();
            for (Project project : projects) {
                updateProjectChatState(project, authInfo);
            }
        } catch (Exception e) {
            LOG.warn("更新聊天服务状态失败", e);
        }
    }
    
    /**
     * 更新项目聊天状态
     */
    private void updateProjectChatState(Project project, AuthInfo authInfo) {
        try {
            String[] chatClasses = {
                "ai.zencoder.plugin.webview.chat.ChatManager",
                "ai.zencoder.plugin.chat.ChatService"
            };
            
            for (String className : chatClasses) {
                if (updateComponentByClassName(project, className, "onAuthUpdate", authInfo)) {
                    LOG.debug("项目聊天状态已更新: " + project.getName());
                    break;
                }
            }
            
        } catch (Exception e) {
            LOG.debug("更新项目聊天状态失败: " + project.getName() + " - " + e.getMessage());
        }
    }
    
    /**
     * 更新连接管理器
     */
    private void updateConnectionManager(AuthInfo authInfo) {
        try {
            Project[] projects = ProjectManager.getInstance().getOpenProjects();
            for (Project project : projects) {
                updateProjectConnectionManager(project, authInfo);
            }
        } catch (Exception e) {
            LOG.warn("更新连接管理器失败", e);
        }
    }
    
    /**
     * 更新项目连接管理器
     */
    private void updateProjectConnectionManager(Project project, AuthInfo authInfo) {
        try {
            String[] connectionClasses = {
                "ai.zencoder.plugin.socket.ably.AblyConnectionManager",
                "ai.zencoder.plugin.connection.ConnectionManager"
            };
            
            for (String className : connectionClasses) {
                Object component = getProjectComponent(project, className);
                if (component != null) {
                    // 尝试调用 updateUserId 方法
                    Method updateUserMethod = findMethod(component.getClass(), "updateUserId");
                    if (updateUserMethod != null) {
                        updateUserMethod.setAccessible(true);
                        updateUserMethod.invoke(component, authInfo.getUserData().getId());
                        LOG.debug("连接管理器已更新: " + project.getName());
                        break;
                    }
                }
            }
            
        } catch (Exception e) {
            LOG.debug("更新项目连接管理器失败: " + project.getName() + " - " + e.getMessage());
        }
    }
    
    /**
     * 更新其他相关组件
     */
    private void updateOtherComponents(AuthInfo authInfo) {
        try {
            // 更新分析服务
            updateAnalyticsService(authInfo);
            
            // 更新设置服务
            updateSettingsService(authInfo);
            
            // 更新其他可能的服务
            updateMiscServices(authInfo);
            
        } catch (Exception e) {
            LOG.warn("更新其他组件失败", e);
        }
    }
    
    /**
     * 更新分析服务
     */
    private void updateAnalyticsService(AuthInfo authInfo) {
        try {
            String[] analyticsClasses = {
                "ai.zencoder.plugin.observability.AnalyticsService",
                "ai.zencoder.plugin.analytics.AnalyticsManager"
            };
            
            for (String className : analyticsClasses) {
                if (updateApplicationComponentByClassName(className, "setUser", authInfo.getUserData())) {
                    LOG.debug("分析服务已更新");
                    break;
                }
            }
            
        } catch (Exception e) {
            LOG.debug("更新分析服务失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新设置服务
     */
    private void updateSettingsService(AuthInfo authInfo) {
        try {
            String[] settingsClasses = {
                "ai.zencoder.plugin.settings.ZencoderSettings",
                "ai.zencoder.plugin.config.SettingsManager"
            };
            
            for (String className : settingsClasses) {
                if (updateApplicationComponentByClassName(className, "onAuthChanged", authInfo)) {
                    LOG.debug("设置服务已更新");
                    break;
                }
            }
            
        } catch (Exception e) {
            LOG.debug("更新设置服务失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新其他服务
     */
    private void updateMiscServices(AuthInfo authInfo) {
        try {
            // 可以在这里添加其他需要更新的服务
            LOG.debug("其他服务更新完成");
        } catch (Exception e) {
            LOG.debug("更新其他服务失败: " + e.getMessage());
        }
    }
    
    /**
     * 通过类名更新组件
     */
    private boolean updateComponentByClassName(Project project, String className, String methodName, Object... args) {
        try {
            Object component = getProjectComponent(project, className);
            if (component != null) {
                return invokeMethodSafely(component, methodName, args);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 通过类名更新应用组件
     */
    private boolean updateApplicationComponentByClassName(String className, String methodName, Object... args) {
        try {
            Object component = getApplicationComponent(className);
            if (component != null) {
                return invokeMethodSafely(component, methodName, args);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取项目组件
     */
    private Object getProjectComponent(Project project, String className) {
        try {
            Class<?> componentClass = Class.forName(className);
            return project.getService(componentClass);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取应用组件
     */
    private Object getApplicationComponent(String className) {
        try {
            Class<?> componentClass = Class.forName(className);
            return ApplicationManager.getApplication().getService(componentClass);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 安全调用方法
     */
    private boolean invokeMethodSafely(Object obj, String methodName, Object... args) {
        try {
            Method method = findMethod(obj.getClass(), methodName);
            if (method != null) {
                method.setAccessible(true);
                method.invoke(obj, args);
                return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 查找方法
     */
    private Method findMethod(Class<?> clazz, String methodName) {
        Class<?> current = clazz;
        while (current != null && current != Object.class) {
            try {
                Method[] methods = current.getDeclaredMethods();
                for (Method method : methods) {
                    if (method.getName().equals(methodName) || 
                        method.getName().contains(methodName)) {
                        return method;
                    }
                }
            } catch (Exception e) {
                // 继续查找
            }
            current = current.getSuperclass();
        }
        return null;
    }
    
    /**
     * 持续监控和维护认证状态
     */
    public void startContinuousMonitoring() {
        // 每30秒检查一次认证状态
        scheduler.scheduleAtFixedRate(() -> {
            try {
                if (!isAuthStateConsistent()) {
                    LOG.warn("检测到认证状态不一致，重新同步...");
                    AuthInfo authInfo = FakeDataGenerator.createFakeAuthInfo();
                    synchronizeAuthState(authInfo);
                }
            } catch (Exception e) {
                LOG.error("监控认证状态失败", e);
            }
        }, 30, 30, TimeUnit.SECONDS);
        
        // 每5分钟刷新一次令牌
        scheduler.scheduleAtFixedRate(() -> {
            try {
                AuthInfo newAuthInfo = FakeDataGenerator.createFakeAuthInfo();
                synchronizeAuthState(newAuthInfo);
                LOG.debug("定期刷新认证令牌");
            } catch (Exception e) {
                LOG.error("定期刷新令牌失败", e);
            }
        }, 5, 5, TimeUnit.MINUTES);
        
        LOG.info("认证状态持续监控已启动");
    }
    
    /**
     * 检查认证状态是否一致
     */
    private boolean isAuthStateConsistent() {
        try {
            ai.zencoder.plugin.auth.AuthService service = 
                ApplicationManager.getApplication().getService(ai.zencoder.plugin.auth.AuthService.class);
            return service != null && service.isAuthenticated();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 停止监控
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
        }
        LOG.info("认证状态监控已停止");
    }
    
    /**
     * 获取状态管理统计信息
     */
    public String getStateManagerStats() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("智能状态管理器统计:\n");
            stats.append("- 缓存条目数: ").append(stateCache.size()).append("\n");
            stats.append("- 监控状态: ").append(scheduler.isShutdown() ? "已停止" : "运行中").append("\n");
            stats.append("- 认证状态一致性: ").append(isAuthStateConsistent() ? "一致" : "不一致").append("\n");

            return stats.toString();
        } catch (Exception e) {
            return "获取统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 检查状态管理器是否正在运行
     */
    public boolean isRunning() {
        return !scheduler.isShutdown();
    }
}
