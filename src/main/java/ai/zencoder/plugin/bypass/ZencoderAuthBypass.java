package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.auth.AuthInfo;
import ai.zencoder.plugin.auth.AuthService;
import ai.zencoder.plugin.auth.UserData;
import ai.zencoder.plugin.config.TokenStorage;
import ai.zencoder.plugin.observers.auth.AuthObserver;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Zencoder 认证绕过主控制器
 * 通过反射技术实现对 Zencoder 认证机制的完全绕过
 */
public class ZencoderAuthBypass {
    private static final Logger LOG = Logger.getInstance(ZencoderAuthBypass.class);
    private static ZencoderAuthBypass instance;
    
    private AuthService originalAuthService;
    private AuthService proxyAuthService;
    private boolean bypassActive = false;
    private final Map<String, Object> stateCache = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    private ZencoderAuthBypass() {
        // 私有构造函数，单例模式
    }
    
    public static synchronized ZencoderAuthBypass getInstance() {
        if (instance == null) {
            instance = new ZencoderAuthBypass();
        }
        return instance;
    }
    
    /**
     * 激活认证绕过
     */
    public boolean activateBypass() {
        try {
            LOG.info("开始激活 Zencoder 认证绕过...");
            
            // 1. 获取原始 AuthService
            originalAuthService = getOriginalAuthService();
            if (originalAuthService == null) {
                LOG.warn("未找到原始 AuthService，可能 Zencoder 插件未加载");
                return false;
            }
            
            // 2. 创建代理 AuthService
            proxyAuthService = BypassAuthServiceProxy.createProxy(originalAuthService);
            
            // 3. 替换服务注册
            if (!replaceAuthService()) {
                LOG.error("替换 AuthService 失败");
                return false;
            }
            
            // 4. 注入伪造认证数据
            if (!injectFakeAuthData()) {
                LOG.warn("注入认证数据失败，但继续执行");
            }
            
            // 5. 触发认证事件
            triggerAuthenticationEvents();
            
            // 6. 启动状态监控
            startStatusMonitoring();
            
            bypassActive = true;
            LOG.info("Zencoder 认证绕过激活成功");
            return true;
            
        } catch (Exception e) {
            LOG.error("认证绕过激活失败", e);
            return false;
        }
    }
    
    /**
     * 获取原始 AuthService
     */
    private AuthService getOriginalAuthService() {
        try {
            return ApplicationManager.getApplication().getService(AuthService.class);
        } catch (Exception e) {
            LOG.warn("获取原始 AuthService 失败", e);
            return null;
        }
    }
    
    /**
     * 替换 AuthService 服务注册
     */
    private boolean replaceAuthService() {
        try {
            // 使用高级反射工具替换服务
            return AdvancedReflectionUtils.deepReplaceService(AuthService.class, proxyAuthService);
        } catch (Exception e) {
            LOG.error("替换 AuthService 失败", e);
            return false;
        }
    }
    
    /**
     * 注入伪造认证数据
     */
    private boolean injectFakeAuthData() {
        try {
            AuthInfo fakeAuthInfo = FakeDataGenerator.createFakeAuthInfo();
            
            // 注入到 TokenStorage
            return ReflectionBypassUtils.injectAuthDataToStorage(fakeAuthInfo);
            
        } catch (Exception e) {
            LOG.error("注入认证数据失败", e);
            return false;
        }
    }
    
    /**
     * 触发认证事件
     */
    private void triggerAuthenticationEvents() {
        try {
            AuthInfo fakeAuthInfo = FakeDataGenerator.createFakeAuthInfo();

            // 触发 AuthObserver 事件
            AuthObserver observer = ApplicationManager.getApplication().getService(AuthObserver.class);
            if (observer != null) {
                observer.signIn(fakeAuthInfo); // 使用便利方法
                LOG.info("已触发认证成功事件");
            }

            // 使用智能状态管理器同步状态
            SmartAuthStateManager stateManager = new SmartAuthStateManager();
            stateManager.synchronizeAuthState(fakeAuthInfo);

        } catch (Exception e) {
            LOG.error("触发认证事件失败", e);
        }
    }
    
    /**
     * 启动状态监控
     */
    private void startStatusMonitoring() {
        // 每30秒检查一次绕过状态
        scheduler.scheduleAtFixedRate(() -> {
            try {
                if (!isBypassActive()) {
                    LOG.warn("检测到认证绕过失效，重新激活...");
                    activateBypass();
                }
            } catch (Exception e) {
                LOG.error("监控绕过状态失败", e);
            }
        }, 30, 30, TimeUnit.SECONDS);
        
        // 每5分钟刷新一次认证状态
        scheduler.scheduleAtFixedRate(() -> {
            try {
                refreshAuthState();
            } catch (Exception e) {
                LOG.error("刷新认证状态失败", e);
            }
        }, 5, 5, TimeUnit.MINUTES);
    }
    
    /**
     * 检查绕过是否激活（私有方法）
     */
    private boolean checkBypassActive() {
        try {
            AuthService currentService = ApplicationManager.getApplication().getService(AuthService.class);
            return currentService != null && currentService.isAuthenticated();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 刷新认证状态
     */
    private void refreshAuthState() {
        try {
            AuthInfo newAuthInfo = FakeDataGenerator.createFakeAuthInfo();
            
            // 更新代理服务的认证信息
            if (proxyAuthService != null) {
                // 由于使用动态代理，这里不需要直接更新
                LOG.debug("代理服务将自动使用新的认证信息");
            }
            
            // 重新同步状态
            SmartAuthStateManager stateManager = new SmartAuthStateManager();
            stateManager.synchronizeAuthState(newAuthInfo);
            
            LOG.debug("认证状态已刷新");
            
        } catch (Exception e) {
            LOG.error("刷新认证状态失败", e);
        }
    }
    
    /**
     * 停用认证绕过
     */
    public void deactivateBypass() {
        try {
            bypassActive = false;
            
            // 停止监控
            scheduler.shutdown();
            
            // 恢复原始服务（如果可能）
            if (originalAuthService != null) {
                AdvancedReflectionUtils.deepReplaceService(AuthService.class, originalAuthService);
            }
            
            LOG.info("认证绕过已停用");
            
        } catch (Exception e) {
            LOG.error("停用认证绕过失败", e);
        }
    }
    
    /**
     * 获取当前用户摘要信息
     */
    public String getCurrentUserSummary() {
        try {
            if (proxyAuthService != null) {
                UserData userData = proxyAuthService.getUserDataOrNull(); // 使用便利方法
                if (userData != null) {
                    return String.format("用户: %s (%s) - %s",
                        userData.getName(),
                        userData.getId(),
                        userData.getEmail());
                }
            }
            return "绕过用户信息不可用";
        } catch (Exception e) {
            return "获取用户信息失败: " + e.getMessage();
        }
    }
    
    /**
     * 检查绕过状态
     */
    public boolean isBypassActive() {
        return bypassActive && checkBypassActive();
    }
    
    /**
     * 获取绕过统计信息
     */
    public String getBypassStatistics() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("认证绕过状态: ").append(bypassActive ? "激活" : "未激活").append("\n");
            stats.append("代理服务状态: ").append(proxyAuthService != null ? "已创建" : "未创建").append("\n");
            stats.append("原始服务状态: ").append(originalAuthService != null ? "已获取" : "未获取").append("\n");
            stats.append("当前用户: ").append(getCurrentUserSummary()).append("\n");
            
            return stats.toString();
        } catch (Exception e) {
            return "获取统计信息失败: " + e.getMessage();
        }
    }
    
    /**
     * 强制重新激活绕过
     */
    public boolean forceReactivate() {
        LOG.info("强制重新激活认证绕过...");
        deactivateBypass();
        
        // 等待一秒后重新激活
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return activateBypass();
    }
}
