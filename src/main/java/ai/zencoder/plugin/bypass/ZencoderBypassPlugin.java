package ai.zencoder.plugin.bypass;

import ai.zencoder.plugin.auth.AuthService;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.ApplicationComponent;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.openapi.project.ProjectManagerListener;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Zencoder 认证绕过插件主入口
 * 在应用启动时自动激活认证绕过
 */
public class ZencoderBypassPlugin implements ApplicationComponent {
    private static final Logger LOG = Logger.getInstance(ZencoderBypassPlugin.class);
    
    private ZencoderAuthBypass authBypass;
    private BypassStatusMonitor statusMonitor;
    private SmartAuthStateManager stateManager;
    private final ScheduledExecutorService initScheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public void initComponent() {
        LOG.info("=== Zencoder 认证绕过插件初始化开始 ===");
        
        try {
            // 初始化核心组件
            authBypass = ZencoderAuthBypass.getInstance();
            statusMonitor = new BypassStatusMonitor();
            stateManager = new SmartAuthStateManager();
            
            // 延迟激活，确保目标插件已加载
            scheduleBypassActivation();
            
            // 监听项目事件
            setupProjectListener();
            
            LOG.info("Zencoder 认证绕过插件初始化完成");
            
        } catch (Exception e) {
            LOG.error("插件初始化失败", e);
        }
    }
    
    /**
     * 调度绕过激活
     */
    private void scheduleBypassActivation() {
        // 立即尝试激活
        initScheduler.execute(this::attemptBypassActivation);
        
        // 如果失败，每5秒重试一次，最多重试12次（1分钟）
        initScheduler.scheduleAtFixedRate(() -> {
            if (!authBypass.isBypassActive()) {
                attemptBypassActivation();
            } else {
                // 激活成功，停止重试
                initScheduler.shutdown();
            }
        }, 5, 5, TimeUnit.SECONDS);
        
        // 1分钟后强制停止重试
        initScheduler.schedule(() -> {
            if (!initScheduler.isShutdown()) {
                LOG.warn("绕过激活重试超时，停止尝试");
                initScheduler.shutdown();
            }
        }, 60, TimeUnit.SECONDS);
    }
    
    /**
     * 尝试激活绕过
     */
    private void attemptBypassActivation() {
        try {
            LOG.info("尝试激活 Zencoder 认证绕过...");
            
            // 等待 Zencoder 插件加载
            if (!waitForZencoderPlugin()) {
                LOG.warn("等待 Zencoder 插件加载超时");
                return;
            }
            
            // 激活认证绕过
            if (authBypass.activateBypass()) {
                LOG.info("✅ Zencoder 认证绕过激活成功！");
                LOG.info("当前用户: " + authBypass.getCurrentUserSummary());
                
                // 启动状态监控
                statusMonitor.startMonitoring();
                
                // 启动智能状态管理
                stateManager.startContinuousMonitoring();
                
                // 显示成功信息
                showSuccessNotification();
                
            } else {
                LOG.error("❌ Zencoder 认证绕过激活失败");
            }
            
        } catch (Exception e) {
            LOG.error("激活绕过过程中发生异常", e);
        }
    }
    
    /**
     * 等待 Zencoder 插件加载
     */
    private boolean waitForZencoderPlugin() {
        int maxAttempts = 20; // 最多等待20秒
        int attempts = 0;
        
        while (attempts < maxAttempts) {
            try {
                // 尝试获取 AuthService，如果成功说明 Zencoder 已加载
                AuthService service = ApplicationManager.getApplication().getService(AuthService.class);
                if (service != null) {
                    LOG.info("✅ 检测到 Zencoder AuthService，插件已加载");
                    return true;
                }
            } catch (Exception e) {
                // 忽略异常，继续等待
            }
            
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
            
            attempts++;
            
            if (attempts % 5 == 0) {
                LOG.info("等待 Zencoder 插件加载... (" + attempts + "/" + maxAttempts + ")");
            }
        }
        
        return false;
    }
    
    /**
     * 设置项目监听器
     */
    private void setupProjectListener() {
        ApplicationManager.getApplication().getMessageBus()
            .connect()
            .subscribe(ProjectManagerListener.TOPIC, new ProjectManagerListener() {
                @Override
                public void projectOpened(Project project) {
                    // 项目打开时，确保绕过状态正常
                    LOG.info("项目已打开: " + project.getName() + "，检查绕过状态");
                    
                    // 延迟检查，给项目时间完全加载
                    ApplicationManager.getApplication().executeOnPooledThread(() -> {
                        try {
                            Thread.sleep(3000); // 等待3秒
                            
                            if (!authBypass.isBypassActive()) {
                                LOG.warn("项目打开后检测到绕过未激活，重新激活...");
                                authBypass.forceReactivate();
                            }
                        } catch (Exception e) {
                            LOG.error("项目打开后检查绕过状态失败", e);
                        }
                    });
                }
                
                @Override
                public void projectClosed(Project project) {
                    LOG.debug("项目已关闭: " + project.getName());
                }
            });
    }
    
    /**
     * 显示成功通知
     */
    private void showSuccessNotification() {
        try {
            // 在控制台显示成功信息
            System.out.println("=".repeat(60));
            System.out.println("🎉 Zencoder 认证绕过已成功激活！");
            System.out.println("📊 " + authBypass.getCurrentUserSummary());
            System.out.println("🔧 所有 Zencoder 功能现在都可以使用了");
            System.out.println("=".repeat(60));
            
        } catch (Exception e) {
            LOG.error("显示成功通知失败", e);
        }
    }
    
    @Override
    public void disposeComponent() {
        try {
            LOG.info("=== Zencoder 认证绕过插件卸载开始 ===");
            
            // 停止调度器
            if (!initScheduler.isShutdown()) {
                initScheduler.shutdown();
            }
            
            // 停止状态监控
            if (statusMonitor != null) {
                statusMonitor.shutdown();
            }
            
            // 停止状态管理
            if (stateManager != null) {
                stateManager.shutdown();
            }
            
            // 停用绕过
            if (authBypass != null) {
                authBypass.deactivateBypass();
            }
            
            LOG.info("Zencoder 认证绕过插件已卸载");
            
        } catch (Exception e) {
            LOG.error("插件卸载失败", e);
        }
    }
    
    @Override
    public String getComponentName() {
        return "ZencoderBypassPlugin";
    }
    
    /**
     * 获取插件状态信息
     */
    public String getPluginStatus() {
        try {
            StringBuilder status = new StringBuilder();
            status.append("=== Zencoder 认证绕过插件状态 ===\n");
            status.append("插件状态: 运行中\n");
            status.append("绕过状态: ").append(authBypass != null && authBypass.isBypassActive() ? "激活" : "未激活").append("\n");
            
            if (authBypass != null) {
                status.append("\n").append(authBypass.getBypassStatistics());
            }
            
            if (statusMonitor != null) {
                status.append("\n").append(statusMonitor.getMonitorStats());
            }
            
            if (stateManager != null) {
                status.append("\n").append(stateManager.getStateManagerStats());
            }
            
            return status.toString();
        } catch (Exception e) {
            return "获取插件状态失败: " + e.getMessage();
        }
    }
    
    /**
     * 手动重新激活绕过
     */
    public boolean manualReactivate() {
        try {
            LOG.info("手动重新激活绕过...");
            
            if (authBypass != null) {
                return authBypass.forceReactivate();
            }
            
            return false;
        } catch (Exception e) {
            LOG.error("手动重新激活失败", e);
            return false;
        }
    }
    
    /**
     * 获取详细诊断信息
     */
    public String getDiagnosticInfo() {
        try {
            StringBuilder diag = new StringBuilder();
            diag.append("=== Zencoder 绕过诊断信息 ===\n");
            
            // 基本信息
            diag.append("时间戳: ").append(System.currentTimeMillis()).append("\n");
            diag.append("Java版本: ").append(System.getProperty("java.version")).append("\n");
            diag.append("IDE版本: ").append(ApplicationManager.getApplication().getBuild()).append("\n");
            
            // 组件状态
            diag.append("\n--- 组件状态 ---\n");
            diag.append("AuthBypass: ").append(authBypass != null ? "已创建" : "未创建").append("\n");
            diag.append("StatusMonitor: ").append(statusMonitor != null ? "已创建" : "未创建").append("\n");
            diag.append("StateManager: ").append(stateManager != null ? "已创建" : "未创建").append("\n");
            
            // 服务检测
            diag.append("\n--- 服务检测 ---\n");
            try {
                AuthService authService = ApplicationManager.getApplication().getService(AuthService.class);
                diag.append("AuthService: ").append(authService != null ? "可用 (" + authService.getClass().getSimpleName() + ")" : "不可用").append("\n");
                
                if (authService != null) {
                    diag.append("认证状态: ").append(authService.isAuthenticated() ? "已认证" : "未认证").append("\n");
                }
            } catch (Exception e) {
                diag.append("AuthService: 检测失败 - ").append(e.getMessage()).append("\n");
            }
            
            // 项目信息
            diag.append("\n--- 项目信息 ---\n");
            Project[] projects = ProjectManager.getInstance().getOpenProjects();
            diag.append("打开的项目数: ").append(projects.length).append("\n");
            for (Project project : projects) {
                diag.append("- ").append(project.getName()).append("\n");
            }
            
            return diag.toString();
        } catch (Exception e) {
            return "获取诊断信息失败: " + e.getMessage();
        }
    }
}
