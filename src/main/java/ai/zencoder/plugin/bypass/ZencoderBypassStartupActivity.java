package ai.zencoder.plugin.bypass;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.StartupActivity;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Zencoder 认证绕过插件启动活动
 * 替代已废弃的 ApplicationComponent
 */
public class ZencoderBypassStartupActivity implements StartupActivity {
    private static final Logger LOG = Logger.getInstance(ZencoderBypassStartupActivity.class);
    
    private static ZencoderAuthBypass authBypass;
    private static BypassStatusMonitor statusMonitor;
    private static SmartAuthStateManager stateManager;
    private static final ScheduledExecutorService initScheduler = Executors.newSingleThreadScheduledExecutor();
    private static boolean initialized = false;
    
    @Override
    public void runActivity(@NotNull Project project) {
        // 确保只初始化一次
        if (initialized) {
            return;
        }
        
        LOG.info("=== Zencoder 认证绕过插件启动活动开始 ===");
        
        try {
            // 初始化核心组件，添加空值检查
            try {
                authBypass = ApplicationManager.getApplication().getService(ZencoderAuthBypass.class);
                if (authBypass == null) {
                    LOG.warn("ZencoderAuthBypass 服务未能正确初始化");
                }
            } catch (Exception e) {
                LOG.error("初始化 ZencoderAuthBypass 失败", e);
            }

            try {
                statusMonitor = ApplicationManager.getApplication().getService(BypassStatusMonitor.class);
                if (statusMonitor == null) {
                    LOG.warn("BypassStatusMonitor 服务未能正确初始化");
                }
            } catch (Exception e) {
                LOG.error("初始化 BypassStatusMonitor 失败", e);
            }

            try {
                stateManager = ApplicationManager.getApplication().getService(SmartAuthStateManager.class);
                if (stateManager == null) {
                    LOG.warn("SmartAuthStateManager 服务未能正确初始化");
                }
            } catch (Exception e) {
                LOG.error("初始化 SmartAuthStateManager 失败", e);
            }

            // 延迟激活，确保目标插件已加载
            scheduleBypassActivation();

            initialized = true;
            LOG.info("Zencoder 认证绕过插件启动活动完成");

        } catch (Exception e) {
            LOG.error("插件启动活动失败", e);
        }
    }
    
    /**
     * 调度绕过激活
     */
    private void scheduleBypassActivation() {
        // 延迟3秒后开始尝试激活
        initScheduler.schedule(this::attemptBypassActivation, 3, TimeUnit.SECONDS);
        
        // 每30秒检查一次状态，如果未激活则重试
        initScheduler.scheduleWithFixedDelay(() -> {
            if (authBypass != null && !authBypass.isBypassActive()) {
                LOG.info("检测到绕过未激活，尝试重新激活...");
                attemptBypassActivation();
            }
        }, 30, 30, TimeUnit.SECONDS);
    }
    
    /**
     * 尝试激活绕过
     */
    private void attemptBypassActivation() {
        try {
            LOG.info("尝试激活 Zencoder 认证绕过...");
            
            // 等待 Zencoder 插件加载
            if (!waitForZencoderPlugin()) {
                LOG.warn("等待 Zencoder 插件加载超时");
                return;
            }
            
            // 激活认证绕过
            if (authBypass != null && authBypass.activateBypass()) {
                LOG.info("✅ Zencoder 认证绕过激活成功！");
                LOG.info("当前用户: " + authBypass.getCurrentUserSummary());
                
                // 启动状态监控
                if (statusMonitor != null) {
                    statusMonitor.startMonitoring();
                }
                
                // 启动智能状态管理
                if (stateManager != null) {
                    stateManager.startContinuousMonitoring();
                }
                
            } else {
                LOG.error("❌ Zencoder 认证绕过激活失败");
            }
            
        } catch (Exception e) {
            LOG.error("激活绕过过程中发生异常", e);
        }
    }
    
    /**
     * 等待 Zencoder 插件加载
     */
    private boolean waitForZencoderPlugin() {
        int maxAttempts = 20; // 最多等待20秒
        int attempts = 0;
        
        while (attempts < maxAttempts) {
            try {
                // 尝试通过反射检查 Zencoder 相关类是否已加载
                Class.forName("ai.zencoder.plugin.auth.AuthService");
                LOG.info("✅ 检测到 Zencoder 插件类，插件已加载");
                return true;
            } catch (ClassNotFoundException e) {
                // 类未找到，继续等待
            }
            
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
            
            attempts++;
            
            if (attempts % 5 == 0) {
                LOG.info("等待 Zencoder 插件加载... (" + attempts + "/" + maxAttempts + ")");
            }
        }
        
        return false;
    }
    
    /**
     * 获取插件实例（用于 Action 类）
     */
    public static ZencoderAuthBypass getAuthBypass() {
        return authBypass;
    }
    
    public static BypassStatusMonitor getStatusMonitor() {
        return statusMonitor;
    }
    
    public static SmartAuthStateManager getStateManager() {
        return stateManager;
    }
    
    /**
     * 手动重新激活绕过
     */
    public static boolean manualReactivate() {
        try {
            if (authBypass != null) {
                return authBypass.activateBypass();
            } else {
                LOG.warn("AuthBypass 服务未初始化，无法重新激活");
                return false;
            }
        } catch (Exception e) {
            LOG.error("手动重新激活失败", e);
            return false;
        }
    }
    
    /**
     * 获取插件状态
     */
    public static String getPluginStatus() {
        try {
            if (authBypass == null) {
                return "插件未初始化 - AuthBypass 服务不可用";
            }

            StringBuilder status = new StringBuilder();
            status.append("=== Zencoder 认证绕过状态 ===\n");

            try {
                status.append("绕过状态: ").append(authBypass.isBypassActive() ? "✅ 已激活" : "❌ 未激活").append("\n");
                status.append("当前用户: ").append(authBypass.getCurrentUserSummary()).append("\n");
            } catch (Exception e) {
                status.append("绕过状态: 获取失败 - ").append(e.getMessage()).append("\n");
            }

            if (statusMonitor != null) {
                try {
                    status.append("监控状态: ").append(statusMonitor.isMonitoring() ? "运行中" : "已停止").append("\n");
                } catch (Exception e) {
                    status.append("监控状态: 获取失败 - ").append(e.getMessage()).append("\n");
                }
            } else {
                status.append("监控状态: 服务未初始化\n");
            }

            if (stateManager != null) {
                try {
                    status.append("状态管理: ").append(stateManager.isRunning() ? "运行中" : "已停止").append("\n");
                } catch (Exception e) {
                    status.append("状态管理: 获取失败 - ").append(e.getMessage()).append("\n");
                }
            } else {
                status.append("状态管理: 服务未初始化\n");
            }

            return status.toString();
        } catch (Exception e) {
            LOG.error("获取插件状态失败", e);
            return "获取插件状态失败: " + e.getMessage();
        }
    }
    
    /**
     * 获取诊断信息
     */
    public static String getDiagnosticInfo() {
        try {
            StringBuilder diagnostics = new StringBuilder();
            diagnostics.append("=== Zencoder 认证绕过诊断信息 ===\n");
            diagnostics.append("初始化状态: ").append(initialized ? "已初始化" : "未初始化").append("\n");
            diagnostics.append("组件状态:\n");
            diagnostics.append("  - AuthBypass: ").append(authBypass != null ? "已加载" : "未加载").append("\n");
            diagnostics.append("  - StatusMonitor: ").append(statusMonitor != null ? "已加载" : "未加载").append("\n");
            diagnostics.append("  - StateManager: ").append(stateManager != null ? "已加载" : "未加载").append("\n");

            if (authBypass != null) {
                try {
                    diagnostics.append("\n绕过详细信息:\n");
                    diagnostics.append(authBypass.getDetailedStatus());
                } catch (Exception e) {
                    diagnostics.append("\n绕过详细信息获取失败: ").append(e.getMessage()).append("\n");
                }
            }

            // 添加系统信息
            diagnostics.append("\n系统信息:\n");
            diagnostics.append("  - Java 版本: ").append(System.getProperty("java.version")).append("\n");
            diagnostics.append("  - 操作系统: ").append(System.getProperty("os.name")).append("\n");
            diagnostics.append("  - 线程数: ").append(Thread.activeCount()).append("\n");

            return diagnostics.toString();
        } catch (Exception e) {
            LOG.error("获取诊断信息失败", e);
            return "获取诊断信息失败: " + e.getMessage();
        }
    }
}
