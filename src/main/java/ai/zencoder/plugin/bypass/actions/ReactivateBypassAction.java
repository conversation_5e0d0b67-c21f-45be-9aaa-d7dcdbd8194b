package ai.zencoder.plugin.bypass.actions;

import ai.zencoder.plugin.bypass.ZencoderBypassStartupActivity;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.ui.Messages;

/**
 * 重新激活绕过的 Action
 */
public class ReactivateBypassAction extends AnAction {
    
    @Override
    public void actionPerformed(AnActionEvent e) {
        try {
            boolean success = ZencoderBypassStartupActivity.manualReactivate();

            if (success) {
                Messages.showInfoMessage(
                    e.getProject(),
                    "Zencoder authentication bypass has been successfully reactivated!",
                    "Reactivation Successful"
                );
            } else {
                Messages.showWarningMessage(
                    e.getProject(),
                    "Failed to reactivate Zencoder authentication bypass. Please check the logs for details.",
                    "Reactivation Failed"
                );
            }

        } catch (Exception ex) {
            Messages.showErrorMessage(
                e.getProject(),
                "Error during reactivation: " + ex.getMessage(),
                "Error"
            );
        }
    }
}
