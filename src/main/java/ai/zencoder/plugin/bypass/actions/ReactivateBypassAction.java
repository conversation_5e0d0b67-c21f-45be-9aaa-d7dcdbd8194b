package ai.zencoder.plugin.bypass.actions;

import ai.zencoder.plugin.bypass.ZencoderBypassPlugin;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.ApplicationComponent;
import com.intellij.openapi.ui.Messages;

/**
 * 重新激活绕过的 Action
 */
public class ReactivateBypassAction extends AnAction {
    
    @Override
    public void actionPerformed(AnActionEvent e) {
        try {
            // 获取插件实例
            ZencoderBypassPlugin plugin = getBypassPlugin();
            
            if (plugin != null) {
                boolean success = plugin.manualReactivate();
                
                if (success) {
                    Messages.showInfoMessage(
                        e.getProject(),
                        "Zencoder authentication bypass has been successfully reactivated!",
                        "Reactivation Successful"
                    );
                }
            }
            
        } catch (Exception ex) {

        }
    }
    
    private ZencoderBypassPlugin getBypassPlugin() {
        try {
            // 尝试从应用组件中获取
            ApplicationComponent[] components = ApplicationManager.getApplication().getComponents();
            for (ApplicationComponent component : components) {
                if (component instanceof ZencoderBypassPlugin) {
                    return (ZencoderBypassPlugin) component;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
