package ai.zencoder.plugin.bypass.actions;

import ai.zencoder.plugin.bypass.BypassTester;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.ui.Messages;

/**
 * 运行绕过测试的 Action
 */
public class RunBypassTestAction extends AnAction {
    
    @Override
    public void actionPerformed(AnActionEvent e) {
        // 在后台线程中运行测试
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                // 先显示快速状态
                String quickStatus = BypassTester.quickStatusCheck();
                
                ApplicationManager.getApplication().invokeLater(() -> {
                    Messages.showInfoMessage(
                        e.getProject(),
                        quickStatus,
                        "Zencoder Bypass Quick Status"
                    );
                });
                
                // 运行完整测试
                boolean testResult = BypassTester.runFullTest();
                
                ApplicationManager.getApplication().invokeLater(() -> {
                    if (testResult) {
                        Messages.showInfoMessage(
                            e.getProject(),
                            "✅ All tests passed! Zencoder bypass is working correctly.\n\n" +
                            "You can now use all Zencoder features without authentication.",
                            "Test Results - SUCCESS"
                        );
                    } else {
                        Messages.showWarningMessage(
                            e.getProject(),
                            "❌ Some tests failed. Please check the logs for details.\n\n" +
                            "The bypass may not be working correctly.",
                            "Test Results - FAILED"
                        );
                    }
                });
                
            } catch (Exception ex) {
                ApplicationManager.getApplication().invokeLater(() -> {
                    Messages.showErrorMessage(
                        e.getProject(),
                        "Failed to run bypass tests: " + ex.getMessage(),
                        "Test Error"
                    );
                });
            }
        });
    }
}
