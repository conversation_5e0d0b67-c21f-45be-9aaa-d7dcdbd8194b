package ai.zencoder.plugin.bypass.actions;

import ai.zencoder.plugin.bypass.ZencoderBypassPlugin;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.ApplicationComponent;
import com.intellij.openapi.ui.Messages;

/**
 * 显示绕过状态的 Action
 */
public class ShowBypassStatusAction extends AnAction {
    
    @Override
    public void actionPerformed(AnActionEvent e) {
        try {
            // 获取插件实例
            ZencoderBypassPlugin plugin = getBypassPlugin();
            
            if (plugin != null) {
                String status = plugin.getPluginStatus();
                Messages.showInfoMessage(
                    e.getProject(),
                    status,
                    "Zencoder Bypass Status"
                );
            } else {
                Messages.showErrorMessage(
                    e.getProject(),
                    "Zencoder Bypass Plugin not found or not initialized",
                    "Error"
                );
            }
            
        } catch (Exception ex) {
            Messages.showErrorMessage(
                e.getProject(),
                "Failed to get bypass status: " + ex.getMessage(),
                "Error"
            );
        }
    }
    
    private ZencoderBypassPlugin getBypassPlugin() {
        try {
            // 尝试从应用组件中获取
            ApplicationComponent[] components = ApplicationManager.getApplication().getComponents();
            for (ApplicationComponent component : components) {
                if (component instanceof ZencoderBypassPlugin) {
                    return (ZencoderBypassPlugin) component;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
