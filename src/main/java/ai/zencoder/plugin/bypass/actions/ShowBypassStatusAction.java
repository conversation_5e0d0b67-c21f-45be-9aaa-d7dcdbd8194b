package ai.zencoder.plugin.bypass.actions;

import ai.zencoder.plugin.bypass.ZencoderBypassStartupActivity;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.ui.Messages;

/**
 * 显示绕过状态的 Action
 */
public class ShowBypassStatusAction extends AnAction {
    
    @Override
    public void actionPerformed(AnActionEvent e) {
        try {
            String status = ZencoderBypassStartupActivity.getPluginStatus();
            Messages.showInfoMessage(
                e.getProject(),
                status,
                "Zencoder Bypass Status"
            );

        } catch (Exception ex) {
            Messages.showErrorMessage(
                e.getProject(),
                "Failed to get bypass status: " + ex.getMessage(),
                "Error"
            );
        }
    }
}
