package ai.zencoder.plugin.bypass.actions;

import ai.zencoder.plugin.bypass.ZencoderBypassStartupActivity;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.ui.Messages;

/**
 * 显示诊断信息的 Action
 */
public class ShowDiagnosticsAction extends AnAction {
    
    @Override
    public void actionPerformed(AnActionEvent e) {
        try {
            String diagnostics = ZencoderBypassStartupActivity.getDiagnosticInfo();
            Messages.showInfoMessage(
                e.getProject(),
                diagnostics,
                "Zencoder Bypass Diagnostics"
            );

        } catch (Exception ex) {
            Messages.showErrorMessage(
                e.getProject(),
                "Failed to get diagnostics: " + ex.getMessage(),
                "Error"
            );
        }
    }
}
