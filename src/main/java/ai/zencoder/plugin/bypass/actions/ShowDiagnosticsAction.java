package ai.zencoder.plugin.bypass.actions;

import ai.zencoder.plugin.bypass.ZencoderBypassPlugin;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.ApplicationComponent;
import com.intellij.openapi.ui.Messages;

/**
 * 显示诊断信息的 Action
 */
public class ShowDiagnosticsAction extends AnAction {
    
    @Override
    public void actionPerformed(AnActionEvent e) {
        try {
            // 获取插件实例
            ZencoderBypassPlugin plugin = getBypassPlugin();
            
            if (plugin != null) {
                String diagnostics = plugin.getDiagnosticInfo();
                Messages.showInfoMessage(
                    e.getProject(),
                    diagnostics,
                    "Zencoder Bypass Diagnostics"
                );
            } else {
                Messages.showErrorMessage(
                    e.getProject(),
                    "Zencoder Bypass Plugin not found or not initialized",
                    "Error"
                );
            }
            
        } catch (Exception ex) {
            Messages.showErrorMessage(
                e.getProject(),
                "Failed to get diagnostics: " + ex.getMessage(),
                "Error"
            );
        }
    }
    
    private ZencoderBypassPlugin getBypassPlugin() {
        try {
            // 尝试从应用组件中获取
            ApplicationComponent[] components = ApplicationManager.getApplication().getComponents();
            for (ApplicationComponent component : components) {
                if (component instanceof ZencoderBypassPlugin) {
                    return (ZencoderBypassPlugin) component;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
