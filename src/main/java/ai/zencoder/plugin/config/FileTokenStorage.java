package ai.zencoder.plugin.config;

import ai.zencoder.plugin.auth.AuthInfo;
import java.io.*;
import java.util.Properties;

/**
 * Zencoder FileTokenStorage 的简化版本
 */
public class FileTokenStorage implements TokenStorage {
    
    private static FileTokenStorage instance;
    private final File configFile;
    private Properties properties;
    
    public FileTokenStorage() {
        // 使用临时目录存储配置
        String tempDir = System.getProperty("java.io.tmpdir");
        this.configFile = new File(tempDir, "zencoder-bypass-tokens.properties");
        this.properties = new Properties();
        loadProperties();
    }
    
    public static FileTokenStorage getInstance() {
        if (instance == null) {
            instance = new FileTokenStorage();
        }
        return instance;
    }
    
    @Override
    public AuthInfo loadAuthInfo() {
        try {
            loadProperties();
            String accessToken = properties.getProperty("access.token");
            String refreshToken = properties.getProperty("refresh.token");
            
            if (accessToken != null && refreshToken != null) {
                return new AuthInfo(accessToken, refreshToken);
            }
        } catch (Exception e) {
            System.err.println("加载认证信息失败: " + e.getMessage());
        }
        return null;
    }
    
    @Override
    public void storeAuthInfo(AuthInfo authInfo) {
        try {
            properties.setProperty("access.token", authInfo.getAccessToken());
            properties.setProperty("refresh.token", authInfo.getRefreshToken());
            saveProperties();
            System.out.println("认证信息已保存到: " + configFile.getAbsolutePath());
        } catch (Exception e) {
            System.err.println("保存认证信息失败: " + e.getMessage());
        }
    }
    
    @Override
    public void cleanAuthInfo() {
        try {
            properties.remove("access.token");
            properties.remove("refresh.token");
            saveProperties();
            System.out.println("认证信息已清除");
        } catch (Exception e) {
            System.err.println("清除认证信息失败: " + e.getMessage());
        }
    }
    
    private void loadProperties() {
        if (configFile.exists()) {
            try (FileInputStream fis = new FileInputStream(configFile)) {
                properties.load(fis);
            } catch (IOException e) {
                System.err.println("加载配置文件失败: " + e.getMessage());
            }
        }
    }
    
    private void saveProperties() {
        try (FileOutputStream fos = new FileOutputStream(configFile)) {
            properties.store(fos, "Zencoder Bypass Token Storage");
        } catch (IOException e) {
            System.err.println("保存配置文件失败: " + e.getMessage());
        }
    }
    
    // Kotlin Companion 对象的模拟
    public static class Companion {
        public TokenStorage a() {
            return getInstance();
        }
        
        public TokenStorage getInstance() {
            return FileTokenStorage.getInstance();
        }
    }
    
    public static final Companion Companion = new Companion();
}
