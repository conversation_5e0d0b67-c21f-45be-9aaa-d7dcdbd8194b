package ai.zencoder.plugin.observers.auth;

import ai.zencoder.plugin.auth.AuthInfo;

/**
 * Zencoder AuthObserver 的简化版本
 */
public class AuthObserver {
    
    private AuthListener publisher;
    
    public AuthObserver() {
        // 简化的实现，实际应该从 MessageBus 获取
        this.publisher = new DefaultAuthListener();
    }
    
    // 混淆的方法名
    public void a(AuthInfo authInfo) {
        signIn(authInfo);
    }
    
    public void a() {
        signOut();
    }
    
    // 便利方法
    public void signIn(AuthInfo authInfo) {
        if (publisher != null) {
            publisher.signIn(authInfo);
        }
    }
    
    public void signOut() {
        if (publisher != null) {
            publisher.signOut();
        }
    }
    
    /**
     * 默认的 AuthListener 实现
     */
    private static class DefaultAuthListener implements AuthListener {
        @Override
        public void signIn(AuthInfo authInfo) {
            System.out.println("AuthObserver: 用户登录 - " + authInfo.getUserData().getName());
        }
        
        @Override
        public void signOut() {
            System.out.println("AuthObserver: 用户登出");
        }
    }
}
