package com.intellij.openapi.application;

import com.intellij.openapi.components.ApplicationComponent;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * IntelliJ ApplicationManager 的简化模拟版本
 */
public class ApplicationManager {
    
    private static Application application = new MockApplication();
    
    public static Application getApplication() {
        return application;
    }
    
    /**
     * 模拟的 Application 实现
     */
    public static class MockApplication implements Application {
        private final Map<Class<?>, Object> services = new ConcurrentHashMap<>();
        private final Map<String, ApplicationComponent> components = new ConcurrentHashMap<>();
        
        @Override
        @SuppressWarnings("unchecked")
        public <T> T getService(Class<T> serviceClass) {
            return (T) services.computeIfAbsent(serviceClass, this::createService);
        }
        
        private Object createService(Class<?> serviceClass) {
            try {
                // 尝试创建服务实例
                return serviceClass.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                System.err.println("无法创建服务: " + serviceClass.getName() + " - " + e.getMessage());
                return null;
            }
        }
        
        @Override
        public void executeOnPooledThread(Runnable runnable) {
            // 在新线程中执行
            new Thread(runnable, "PooledThread").start();
        }
        
        @Override
        public void invokeLater(Runnable runnable) {
            // 简化实现，直接执行
            runnable.run();
        }
        
        @Override
        public String getBuild() {
            return "MockBuild-2024.1";
        }

        @Override
        public MessageBus getMessageBus() {
            return new MessageBus();
        }

        @Override
        public ApplicationComponent[] getComponents() {
            return components.values().toArray(new ApplicationComponent[0]);
        }

        // 用于测试的方法
        public void registerService(Class<?> serviceClass, Object serviceInstance) {
            services.put(serviceClass, serviceInstance);
        }

        public void registerComponent(String name, ApplicationComponent component) {
            components.put(name, component);
        }

        public Map<Class<?>, Object> getServices() {
            return services;
        }
    }
    
    /**
     * Application 接口
     */
    public interface Application {
        <T> T getService(Class<T> serviceClass);
        void executeOnPooledThread(Runnable runnable);
        void invokeLater(Runnable runnable);
        String getBuild();
        MessageBus getMessageBus();
        ApplicationComponent[] getComponents();
    }

    /**
     * 简化的 MessageBus 实现
     */
    public static class MessageBus {
        public MessageBusConnection connect() {
            return new MessageBusConnection();
        }
    }

    /**
     * 简化的 MessageBusConnection 实现
     */
    public static class MessageBusConnection {
        public <T> void subscribe(T topic, T handler) {
            // 简化实现，实际不做任何事情
        }
    }
}
