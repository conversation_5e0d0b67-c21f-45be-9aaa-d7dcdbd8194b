package com.intellij.openapi.components;

/**
 * IntelliJ ApplicationComponent 的简化模拟版本
 */
public interface ApplicationComponent {
    
    /**
     * 组件初始化
     */
    default void initComponent() {
        // 默认空实现
    }
    
    /**
     * 组件销毁
     */
    default void disposeComponent() {
        // 默认空实现
    }
    
    /**
     * 获取组件名称
     */
    default String getComponentName() {
        return getClass().getSimpleName();
    }
}
