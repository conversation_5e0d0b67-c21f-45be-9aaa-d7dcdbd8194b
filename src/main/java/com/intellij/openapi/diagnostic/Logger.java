package com.intellij.openapi.diagnostic;

/**
 * IntelliJ Logger 的简化模拟版本
 */
public class Logger {
    
    private final String name;
    
    private Logger(String name) {
        this.name = name;
    }
    
    public static Logger getInstance(Class<?> clazz) {
        return new Logger(clazz.getSimpleName());
    }
    
    public static Logger getInstance(String name) {
        return new Logger(name);
    }
    
    public void info(String message) {
        System.out.println("[INFO] " + name + ": " + message);
    }
    
    public void debug(String message) {
        System.out.println("[DEBUG] " + name + ": " + message);
    }
    
    public void warn(String message) {
        System.out.println("[WARN] " + name + ": " + message);
    }
    
    public void warn(String message, Throwable throwable) {
        System.out.println("[WARN] " + name + ": " + message);
        if (throwable != null) {
            throwable.printStackTrace();
        }
    }
    
    public void error(String message) {
        System.err.println("[ERROR] " + name + ": " + message);
    }
    
    public void error(String message, Throwable throwable) {
        System.err.println("[ERROR] " + name + ": " + message);
        if (throwable != null) {
            throwable.printStackTrace();
        }
    }
}
