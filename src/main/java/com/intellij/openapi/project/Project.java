package com.intellij.openapi.project;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * IntelliJ Project 的简化模拟版本
 */
public interface Project {
    
    String getName();
    
    <T> T getService(Class<T> serviceClass);
    
    /**
     * 默认的 Project 实现
     */
    class MockProject implements Project {
        private final String name;
        private final Map<Class<?>, Object> services = new ConcurrentHashMap<>();
        
        public MockProject(String name) {
            this.name = name;
        }
        
        @Override
        public String getName() {
            return name;
        }
        
        @Override
        @SuppressWarnings("unchecked")
        public <T> T getService(Class<T> serviceClass) {
            return (T) services.computeIfAbsent(serviceClass, this::createService);
        }
        
        private Object createService(Class<?> serviceClass) {
            try {
                return serviceClass.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                System.err.println("无法创建项目服务: " + serviceClass.getName() + " - " + e.getMessage());
                return null;
            }
        }
        
        public void registerService(Class<?> serviceClass, Object serviceInstance) {
            services.put(serviceClass, serviceInstance);
        }
    }
}
