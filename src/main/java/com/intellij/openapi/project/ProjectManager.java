package com.intellij.openapi.project;

/**
 * IntelliJ ProjectManager 的简化模拟版本
 */
public class ProjectManager {
    
    private static ProjectManager instance = new ProjectManager();
    private Project[] openProjects = new Project[0];
    
    public static ProjectManager getInstance() {
        return instance;
    }
    
    public Project[] getOpenProjects() {
        return openProjects;
    }
    
    // 用于测试的方法
    public void setOpenProjects(Project... projects) {
        this.openProjects = projects;
    }
}
