package com.intellij.openapi.project;

/**
 * IntelliJ ProjectManagerListener 的简化模拟版本
 */
public interface ProjectManagerListener {
    
    /**
     * 项目打开事件
     */
    default void projectOpened(Project project) {
        // 默认空实现
    }
    
    /**
     * 项目关闭事件
     */
    default void projectClosed(Project project) {
        // 默认空实现
    }
    
    /**
     * 模拟的 TOPIC 常量
     */
    ProjectManagerListener TOPIC = new ProjectManagerListener() {};
}
