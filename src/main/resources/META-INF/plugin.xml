<?xml version="1.0" encoding="UTF-8"?>
<idea-plugin>
    <id>ai.zencoder.plugin.bypass</id>
    <name>Zencoder Authentication Bypass</name>
    <version>1.0.0</version>
    <vendor email="<EMAIL>" url="https://zencoder-bypass.dev">Zencoder Bypass Team</vendor>

    <description><![CDATA[
    <h1>Zencoder Authentication Bypass</h1>
    <p>Advanced plugin that bypasses Zencoder authentication using sophisticated reflection and service hijacking techniques.</p>

    <h2>Features</h2>
    <ul>
        <li>Automatic authentication bypass with reflection technology</li>
        <li>Service hijacking and credential injection</li>
        <li>Real-time state synchronization</li>
        <li>Target discovery for different Zencoder versions</li>
        <li>Comprehensive configuration management</li>
        <li>Status monitoring and recovery mechanisms</li>
        <li>User-friendly management interface</li>
    </ul>

    <h2>Core Components</h2>
    <ul>
        <li><strong>ZencoderReflectionUtils</strong>: Advanced reflection operations with TTL caching</li>
        <li><strong>ZencoderCredentialInjector</strong>: Fake credential creation and injection</li>
        <li><strong>ZencoderServiceHijacker</strong>: Dynamic proxy-based service interception</li>
        <li><strong>ZencoderTargetDiscovery</strong>: Automatic target class discovery</li>
        <li><strong>ZencoderStateSynchronizer</strong>: Real-time state monitoring and recovery</li>
        <li><strong>ZencoderBypassInitializer</strong>: Automatic initialization on startup</li>
        <li><strong>ZencoderConfig</strong>: Comprehensive configuration management</li>
    </ul>

    <h2>Usage</h2>
    <p>Install alongside Zencoder plugin. Access via Tools menu or status bar widget.</p>

    <h2>Security Notice</h2>
    <p>This plugin is intended for development and testing purposes only.</p>
    ]]></description>

    <!-- Plugin dependencies -->
    <depends>com.intellij.modules.platform</depends>
    <depends optional="true">ai.zencoder.plugin</depends>

    <!-- Compatible IDE versions -->
    <idea-version since-build="233" until-build="243.*"/>

    <!-- Application components -->
    <application-components>
        <component>
            <implementation-class>ai.zencoder.plugin.bypass.ZencoderBypassPlugin</implementation-class>
        </component>
    </application-components>

    <!-- Extension points -->
    <extensions defaultExtensionNs="com.intellij">
        <!-- Configuration service -->
        <applicationService
            serviceImplementation="ai.zencoder.plugin.config.ZencoderConfig"/>

        <!-- New bypass services -->
        <applicationService
            serviceImplementation="ai.zencoder.plugin.bypass.ZencoderAuthBypass"/>

        <applicationService
            serviceImplementation="ai.zencoder.plugin.bypass.BypassStatusMonitor"/>

        <applicationService
            serviceImplementation="ai.zencoder.plugin.bypass.SmartAuthStateManager"/>


    </extensions>

    <!-- Actions -->
    <actions>
        <group id="ZencoderBypassGroup" text="Zencoder Bypass" description="Zencoder Authentication Bypass">
            <add-to-group group-id="ToolsMenu" anchor="last"/>

            <action id="ZencoderBypass.Status"
                    class="ai.zencoder.plugin.bypass.actions.ShowBypassStatusAction"
                    text="Show Bypass Status"
                    description="Show current bypass status and statistics"/>

            <action id="ZencoderBypass.Reactivate"
                    class="ai.zencoder.plugin.bypass.actions.ReactivateBypassAction"
                    text="Reactivate Bypass"
                    description="Manually reactivate authentication bypass"/>

            <action id="ZencoderBypass.Diagnostics"
                    class="ai.zencoder.plugin.bypass.actions.ShowDiagnosticsAction"
                    text="Show Diagnostics"
                    description="Show detailed diagnostic information"/>
        </group>
    </actions>
</idea-plugin>
