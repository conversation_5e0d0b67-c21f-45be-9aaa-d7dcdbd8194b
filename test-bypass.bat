@echo off
echo === Zencoder Authentication Bypass Test ===

REM Clean and create output directory
rmdir /s /q build\classes 2>nul
mkdir build\classes 2>nul

echo 1. Compiling auth classes...
javac -cp "src\main\java" -d build\classes src\main\java\ai\zencoder\plugin\auth\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Auth classes compilation failed
    goto :end
)
echo SUCCESS: Auth classes compiled

echo 2. Compiling config classes...
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\ai\zencoder\plugin\config\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Config classes compilation failed
    goto :end
)
echo SUCCESS: Config classes compiled

echo 3. Compiling observer classes...
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\ai\zencoder\plugin\observers\auth\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Observer classes compilation failed
    goto :end
)
echo SUCCESS: Observer classes compiled

echo 4. Compiling IntelliJ mock classes...
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\com\intellij\openapi\application\*.java
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\com\intellij\openapi\diagnostic\*.java
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\com\intellij\openapi\project\*.java
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\com\intellij\openapi\components\*.java
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: IntelliJ mock classes compilation failed
    goto :end
)
echo SUCCESS: IntelliJ mock classes compiled

echo 5. Compiling core bypass classes...
javac -cp "src\main\java;build\classes" -d build\classes src\main\java\ai\zencoder\plugin\bypass\FakeDataGenerator.java
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: FakeDataGenerator compilation failed
    goto :end
)
echo SUCCESS: FakeDataGenerator compiled

javac -cp "src\main\java;build\classes" -d build\classes src\main\java\ai\zencoder\plugin\bypass\AdvancedReflectionUtils.java
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: AdvancedReflectionUtils compilation failed
    goto :end
)
echo SUCCESS: AdvancedReflectionUtils compiled

javac -cp "src\main\java;build\classes" -d build\classes src\main\java\ai\zencoder\plugin\bypass\ReflectionBypassUtils.java
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: ReflectionBypassUtils compilation failed
    goto :end
)
echo SUCCESS: ReflectionBypassUtils compiled

echo 6. Running core logic test...
java -cp build\classes ai.zencoder.plugin.bypass.SimpleBypassTest
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Core logic test failed
    goto :end
)

echo.
echo SUCCESS: Quick test completed! Core functionality verified!
echo.
echo === Test Results Summary ===
echo [OK] JWT token generation
echo [OK] User data generation  
echo [OK] Authentication info generation
echo [OK] Dynamic proxy creation
echo [OK] Reflection utilities
echo.
echo Zencoder authentication bypass core functionality is ready!
echo Users can now access all Zencoder features without registration/login.

:end
pause
