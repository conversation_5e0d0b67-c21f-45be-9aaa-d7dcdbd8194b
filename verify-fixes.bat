@echo off
echo ========================================
echo Zencoder Plugin 修复验证脚本
echo ========================================

echo 1. 检查关键文件是否存在...

REM 检查新的启动活动类
if exist "src\main\java\ai\zencoder\plugin\bypass\ZencoderBypassStartupActivity.java" (
    echo ✅ ZencoderBypassStartupActivity.java 存在
) else (
    echo ❌ ZencoderBypassStartupActivity.java 缺失
    goto :error
)

REM 检查修复后的 Action 类
if exist "src\main\java\ai\zencoder\plugin\bypass\actions\ShowBypassStatusAction.java" (
    echo ✅ ShowBypassStatusAction.java 存在
) else (
    echo ❌ ShowBypassStatusAction.java 缺失
    goto :error
)

if exist "src\main\java\ai\zencoder\plugin\bypass\actions\ReactivateBypassAction.java" (
    echo ✅ ReactivateBypassAction.java 存在
) else (
    echo ❌ ReactivateBypassAction.java 缺失
    goto :error
)

if exist "src\main\java\ai\zencoder\plugin\bypass\actions\ShowDiagnosticsAction.java" (
    echo ✅ ShowDiagnosticsAction.java 存在
) else (
    echo ❌ ShowDiagnosticsAction.java 缺失
    goto :error
)

REM 检查核心服务类
if exist "src\main\java\ai\zencoder\plugin\bypass\ZencoderAuthBypass.java" (
    echo ✅ ZencoderAuthBypass.java 存在
) else (
    echo ❌ ZencoderAuthBypass.java 缺失
    goto :error
)

if exist "src\main\java\ai\zencoder\plugin\bypass\BypassStatusMonitor.java" (
    echo ✅ BypassStatusMonitor.java 存在
) else (
    echo ❌ BypassStatusMonitor.java 缺失
    goto :error
)

if exist "src\main\java\ai\zencoder\plugin\bypass\SmartAuthStateManager.java" (
    echo ✅ SmartAuthStateManager.java 存在
) else (
    echo ❌ SmartAuthStateManager.java 缺失
    goto :error
)

REM 检查配置文件
if exist "src\main\resources\META-INF\plugin.xml" (
    echo ✅ plugin.xml 存在
) else (
    echo ❌ plugin.xml 缺失
    goto :error
)

echo.
echo 2. 检查已删除的废弃文件...

REM 确认废弃文件已删除
if not exist "src\main\java\ai\zencoder\plugin\bypass\ZencoderBypassPlugin.java" (
    echo ✅ 废弃的 ZencoderBypassPlugin.java 已删除
) else (
    echo ❌ 废弃的 ZencoderBypassPlugin.java 仍然存在
    goto :error
)

if not exist "src\main\java\com\intellij\openapi\components\ApplicationComponent.java" (
    echo ✅ 自定义的 ApplicationComponent.java 已删除
) else (
    echo ❌ 自定义的 ApplicationComponent.java 仍然存在
    goto :error
)

if not exist "src\main\java\com\intellij\openapi\application\ApplicationManager.java" (
    echo ✅ 自定义的 ApplicationManager.java 已删除
) else (
    echo ❌ 自定义的 ApplicationManager.java 仍然存在
    goto :error
)

echo.
echo 3. 检查 plugin.xml 配置...

findstr /C:"postStartupActivity" "src\main\resources\META-INF\plugin.xml" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ plugin.xml 包含 postStartupActivity 配置
) else (
    echo ❌ plugin.xml 缺少 postStartupActivity 配置
    goto :error
)

findstr /C:"application-components" "src\main\resources\META-INF\plugin.xml" >nul
if %ERRORLEVEL% NEQ 0 (
    echo ✅ plugin.xml 已移除废弃的 application-components
) else (
    echo ❌ plugin.xml 仍包含废弃的 application-components
    goto :error
)

echo.
echo 4. 检查 Action 类的导入...

findstr /C:"ZencoderBypassStartupActivity" "src\main\java\ai\zencoder\plugin\bypass\actions\ShowBypassStatusAction.java" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ ShowBypassStatusAction 使用新的启动活动类
) else (
    echo ❌ ShowBypassStatusAction 未使用新的启动活动类
    goto :error
)

findstr /C:"ApplicationComponent" "src\main\java\ai\zencoder\plugin\bypass\actions\ShowBypassStatusAction.java" >nul
if %ERRORLEVEL% NEQ 0 (
    echo ✅ ShowBypassStatusAction 已移除 ApplicationComponent 引用
) else (
    echo ❌ ShowBypassStatusAction 仍包含 ApplicationComponent 引用
    goto :error
)

echo.
echo 5. 检查方法完整性...

findstr /C:"getDetailedStatus" "src\main\java\ai\zencoder\plugin\bypass\ZencoderAuthBypass.java" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ ZencoderAuthBypass 包含 getDetailedStatus 方法
) else (
    echo ❌ ZencoderAuthBypass 缺少 getDetailedStatus 方法
    goto :error
)

findstr /C:"isMonitoring" "src\main\java\ai\zencoder\plugin\bypass\BypassStatusMonitor.java" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ BypassStatusMonitor 包含 isMonitoring 方法
) else (
    echo ❌ BypassStatusMonitor 缺少 isMonitoring 方法
    goto :error
)

findstr /C:"isRunning" "src\main\java\ai\zencoder\plugin\bypass\SmartAuthStateManager.java" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ SmartAuthStateManager 包含 isRunning 方法
) else (
    echo ❌ SmartAuthStateManager 缺少 isRunning 方法
    goto :error
)

echo.
echo ========================================
echo ✅ 所有修复验证通过！
echo ========================================
echo.
echo 修复摘要:
echo - 移除了废弃的 ApplicationComponent 实现
echo - 实现了现代的 StartupActivity 机制
echo - 更新了所有 Action 类的服务访问方式
echo - 修复了 plugin.xml 配置
echo - 添加了缺失的状态检查方法
echo - 清理了技术债务
echo.
echo 下一步:
echo 1. 使用 Gradle 构建插件: gradlew build
echo 2. 在 IntelliJ 中安装并测试插件
echo 3. 检查日志确认问题已解决
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ 验证失败！
echo ========================================
echo 请检查上述错误并重新运行修复脚本。
exit /b 1

:end
echo 验证完成。
