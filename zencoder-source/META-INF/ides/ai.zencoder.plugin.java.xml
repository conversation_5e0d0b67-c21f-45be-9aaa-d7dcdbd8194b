<idea-plugin>
    <depends>org.jetbrains.idea.maven</depends>
    <depends>com.intellij.gradle</depends>
    <depends>org.jetbrains.kotlin</depends>

    <extensions defaultExtensionNs="com.intellij">
        <codeInsight.daemonBoundCodeVisionProvider
                implementation="ai.zencoder.plugin.providers.codevision.doccomment.JavaDocCommentCodeVisionProvider"/>
        <codeInsight.daemonBoundCodeVisionProvider
                implementation="ai.zencoder.plugin.providers.codevision.unittests.JavaUnitTestsCodeVision"/>
        <config.codeVisionGroupSettingProvider
                implementation="ai.zencoder.plugin.providers.codevision.doccomment.JavaDocCommentSettingsProvider"/>
        <config.codeVisionGroupSettingProvider
                implementation="ai.zencoder.plugin.providers.codevision.unittests.JavaUnitTestsSettingsProvider"/>
    </extensions>
    <extensions defaultExtensionNs="ai.zencoder.plugin">
        <contextGather language="JAVA" implementationClass="ai.zencoder.plugin.context.JavaContextGather"/>
        <zenProjectService language="JAVA" implementationClass="ai.zencoder.plugin.services.JvmProjectServiceFacade"/>
    </extensions>


    <actions>
        <!-- Test generation actions-->
        <action id="GenerateMenu_JavaTestForMethod"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForJavaMethod"
                text="Generate Test for Method"
                description="Generates test for a method using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_JavaTestForMethod"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForJavaMethod"
                text="Generate Test for Method"
                description="Generates test for a method using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <action id="GenerateMenu_JavaTestForClass"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForJavaClass"
                text="Generate Test for Class"
                description="Generates test for a class using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_JavaTestForClass"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForJavaClass"
                text="Generate Test for Class"
                description="Generates test for a class using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>

        <!-- Doc comment generation actions-->
        <action id="GenerateMenu_JavaDocCommentForMethod"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForJavaMethod"
                text="Generate Doc Comment for Method"
                description="Generates a doc comment for a method using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_JavaDocCommentForMethod"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForJavaMethod"
                text="Generate Doc Comment for Method"
                description="Generates a doc comment for a method using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <action id="GenerateMenu_JavaDocCommentForClass"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForJavaClass"
                text="Generate Doc Comment for Class"
                description="Generates a doc comment for a class using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_JavaDocCommentForClass"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForJavaClass"
                text="Generate Doc Comment for Class"
                description="Generates a doc comment for a class using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
    </actions>
</idea-plugin>
