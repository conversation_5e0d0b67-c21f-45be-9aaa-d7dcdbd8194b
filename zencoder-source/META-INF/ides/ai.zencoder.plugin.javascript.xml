<idea-plugin>
    <extensions defaultExtensionNs="com.intellij">
        <codeInsight.daemonBoundCodeVisionProvider
                implementation="ai.zencoder.plugin.providers.codevision.doccomment.JavaScriptDocCommentCodeVisionProvider"/>
        <codeInsight.daemonBoundCodeVisionProvider
                implementation="ai.zencoder.plugin.providers.codevision.unittests.JavaScriptUnitTestsCodeVision"/>
        <config.codeVisionGroupSettingProvider
                implementation="ai.zencoder.plugin.providers.codevision.doccomment.JavaScriptDocCommentSettingsProvider"/>
        <config.codeVisionGroupSettingProvider
                implementation="ai.zencoder.plugin.providers.codevision.unittests.JavaScriptUnitTestsSettingsProvider"/>
    </extensions>
    <extensions defaultExtensionNs="ai.zencoder.plugin">
        <contextGather language="JavaScript" implementationClass="ai.zencoder.plugin.context.JavaScriptContextGather"/>
        <zenProjectService language="JavaScript" implementationClass="ai.zencoder.plugin.services.JSProjectService"/>
    </extensions>
    <actions>
        <!-- Test generation actions-->
        <action id="GenerateMenu_JSTestForFunction"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForJSFunction"
                text="Zencoder: Generate Test for Function"
                description="Generates a test for a function using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_JSTestForFunction"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForJSFunction"
                text="Zencoder: Generate Test for Function"
                description="Generates a test for a function using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <action id="GenerateMenu_JSTestForClass"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForJSClass"
                text="Zencoder: Generate Test for Class"
                description="Generates a test for a class using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_JSTestForClass"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForJSClass"
                text="Zencoder: Generate Test for Class"
                description="Generates a test for a class using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <!-- Doc comment generation actions-->
        <action id="GenerateMenu_JSDocCommentForFunction"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForJSFunction"
                text="Zencoder: Generate Doc Comment for Function"
                description="Generates a doc comment for a function using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_JSDocCommentForFunction"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForJSFunction"
                text="Zencoder: Generate Doc Comment for Function"
                description="Generates a doc comment for a function using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <action id="GenerateMenu_JSDocCommentForClass"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForJSClass"
                text="Zencoder: Generate Doc Comment for Class"
                description="Generates a doc comment for a class using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_JSDocCommentForClass"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForJSClass"
                text="Zencoder: Generate Doc Comment for Class"
                description="Generates a doc comment for a class using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
    </actions>
</idea-plugin>

