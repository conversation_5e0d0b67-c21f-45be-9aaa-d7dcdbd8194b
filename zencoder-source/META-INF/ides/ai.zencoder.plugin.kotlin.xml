<idea-plugin>
    <extensions defaultExtensionNs="com.intellij">
        <codeInsight.daemonBoundCodeVisionProvider
                implementation="ai.zencoder.plugin.providers.codevision.doccomment.KotlinDocCommentCodeVisionProvider"/>
        <codeInsight.daemonBoundCodeVisionProvider
                implementation="ai.zencoder.plugin.providers.codevision.unittests.KotlinUnitTestsCodeVision"/>
        <config.codeVisionGroupSettingProvider
                implementation="ai.zencoder.plugin.providers.codevision.doccomment.KotlinDocCommentSettingsProvider"/>
        <config.codeVisionGroupSettingProvider
                implementation="ai.zencoder.plugin.providers.codevision.unittests.KotlinUnitTestsSettingsProvider"/>
    </extensions>
    <extensions defaultExtensionNs="ai.zencoder.plugin">
        <contextGather language="kotlin" implementationClass="ai.zencoder.plugin.context.KotlinContextGather"/>
        <zenProjectService language="kotlin" implementationClass="ai.zencoder.plugin.services.JvmProjectServiceFacade"/>
    </extensions>


    <actions>
        <!-- Test generation actions-->
        <action id="GenerateMenu_KotlinTestForMethod"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForKotlinMethod"
                text="Generate Test for Method"
                description="Generates test for a method using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_KotlinTestForMethod"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForKotlinMethod"
                text="Generate Test for Method"
                description="Generates test for a method using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <action id="GenerateMenu_KotlinTestForClass"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForKotlinClass"
                text="Generate Test for Class"
                description="Generates test for a class using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_KotlinTestForClass"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForKotlinClass"
                text="Generate Test for Class"
                description="Generates test for a class using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <!-- Doc comment generation actions-->
        <action id="GenerateMenu_KotlinDocCommentForMethod"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForKotlinMethod"
                text="Generate Doc Comment for Function"
                description="Generates a doc comment for a function using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_KotlinDocCommentForMethod"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForKotlinMethod"
                text="Generate Doc Comment for Function"
                description="Generates a doc comment for a function using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <action id="GenerateMenu_KotlinDocCommentForClass"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForKotlinClass"
                text="Generate Doc Comment for Class"
                description="Generates a doc comment for a class using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_KotlinDocCommentForClass"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForKotlinClass"
                text="Generate Doc Comment for Class"
                description="Generates a doc comment for a class using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
    </actions>
</idea-plugin>
