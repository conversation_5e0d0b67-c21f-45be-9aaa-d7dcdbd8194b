<idea-plugin>
    <extensions defaultExtensionNs="com.intellij">
        <codeInsight.daemonBoundCodeVisionProvider
                implementation="ai.zencoder.plugin.providers.codevision.doccomment.PhpDocCommentCodeVisionProvider"/>
        <config.codeVisionGroupSettingProvider
                implementation="ai.zencoder.plugin.providers.codevision.doccomment.PhpDocCommentSettingsProvider"/>
    </extensions>
    <extensions defaultExtensionNs="ai.zencoder.plugin">
        <contextGather language="PHP" implementationClass="ai.zencoder.plugin.context.PhpContextGather"/>
    </extensions>

    <actions>
        <!-- Test generation actions-->
        <!--  todo: will be added after new unittest-agent implemented     -->
        <!-- Doc comment generation actions-->
        <action id="GenerateMenu_PhpDocCommentForMethod"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForPhpMethod"
                text="Zencoder: Generate Doc Comment for Method"
                description="Generates a docstring for a method using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_PhpDocCommentForMethod"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForPhpMethod"
                text="Zencoder: Generate Doc Comment for Method"
                description="Generates a docstring for a method using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <action id="GenerateMenu_PhpDocCommentForClass"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForPhpClass"
                text="Zencoder: Generate Doc Comment for Class"
                description="Generates a docstring for a class using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_PhpDocCommentForClass"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForPhpClass"
                text="Zencoder: Generate Doc Comment for Class"
                description="Generates a docstring for a class using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
    </actions>
</idea-plugin>

