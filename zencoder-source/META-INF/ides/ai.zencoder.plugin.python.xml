<idea-plugin>
    <extensions defaultExtensionNs="com.intellij">
        <codeInsight.daemonBoundCodeVisionProvider
                implementation="ai.zencoder.plugin.providers.codevision.doccomment.PythonDocCommentCodeVisionProvider"/>
        <codeInsight.daemonBoundCodeVisionProvider
                implementation="ai.zencoder.plugin.providers.codevision.unittests.PythonUnitTestsCodeVision"/>
        <config.codeVisionGroupSettingProvider
                implementation="ai.zencoder.plugin.providers.codevision.doccomment.PythonDocCommentSettingsProvider"/>
        <config.codeVisionGroupSettingProvider
                implementation="ai.zencoder.plugin.providers.codevision.unittests.PythonUnitTestsSettingsProvider"/>
    </extensions>
    <extensions defaultExtensionNs="ai.zencoder.plugin">
        <contextGather language="Python" implementationClass="ai.zencoder.plugin.context.PythonContextGather"/>
        <zenProjectService language="Python" implementationClass="ai.zencoder.plugin.services.PythonProjectService"/>
    </extensions>
    <extensions defaultExtensionNs="com.intellij">
        <projectService serviceInterface="ai.zencoder.plugin.services.lang.LangSpecificTerminalActionsService"
                        serviceImplementation="ai.zencoder.plugin.services.lang.PythonTerminalActionsService"
                        overrides="true"/>
    </extensions>

    <actions>
        <!-- Test generation actions-->
        <action id="GenerateMenu_PythonTestForFunction"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForPythonFunction"
                text="Zencoder: Generate Test for Function"
                description="Generates a test for a function using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_PythonTestForFunction"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForPythonFunction"
                text="Zencoder: Generate Test for Function"
                description="Generates a test for a function using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <action id="GenerateMenu_PythonTestForClass"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForPythonClass"
                text="Zencoder: Generate Test for Class"
                description="Generates a test for a class using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_PythonTestForClass"
                class="ai.zencoder.plugin.providers.generate.test.GenerateTestForPythonClass"
                text="Zencoder: Generate Test for Class"
                description="Generates a test for a class using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <!-- Doc comment generation actions-->
        <action id="GenerateMenu_PythonDocCommentForFunction"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForPythonFunction"
                text="Zencoder: Generate Doc Comment for Function"
                description="Generates a doc comment for a function using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_PythonDocCommentForFunction"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForPythonFunction"
                text="Zencoder: Generate Doc Comment for Function"
                description="Generates a doc comment for a function using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
        <action id="GenerateMenu_PythonDocCommentForClass"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForPythonClass"
                text="Zencoder: Generate Doc Comment for Class"
                description="Generates a doc comment for a class using Zencoder"
                icon="/zencoder/progress/original.svg">
            <add-to-group group-id="zencoder.generate.menu.group"/>
        </action>
        <action id="EditorPopup_PythonDocCommentForClass"
                class="ai.zencoder.plugin.providers.generate.doccomment.GenerateDocCommentForPythonClass"
                text="Zencoder: Generate Doc Comment for Class"
                description="Generates a doc comment for a class using Zencoder">
            <add-to-group group-id="zencoder.editorActionGroup"/>
        </action>
    </actions>
</idea-plugin>

