<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin require-restart="true">
  <idea-version since-build="242.20224.91" />
  <change-notes />
  <description><![CDATA[<h1>Zencoder: Your Mindful AI Coding Agent</h1>

<p>Zencoder is an advanced AI platform transforming your development workflow with advanced coding agents, integrations, intelligent code generation, unit testing, and documentation capabilities.</p>

<p>By deeply understanding your codebase and leveraging specialized AI agents, Zencoder delivers high-quality code that integrates seamlessly with your existing projects.</p>

<h2>Unique Approach</h2>

<p>Zencoder stands apart from other AI coding tools through two core technologies and deep integrations support:</p>

<h3>Repo Grokking™</h3>

<p>Repo Grokking creates a comprehensive understanding of your entire codebase by analyzing code structure, dependencies, and patterns. Unlike basic retrieval approaches, it builds a deep semantic map of your repository that powers all Zencoder features.</p>

<h3>Agentic Pipeline</h3>

<p>The Agentic Pipeline orchestrates specialized AI components to handle complex coding tasks. It implements automated validation and error correction cycles, ensuring generated code meets quality standards before delivery.</p>

<h3>Powerful Integrations</h3>

<p><PERSON>coder connects with your development ecosystem through:</p>

<ul><li><strong>Native Integrations</strong> with built-in connections with essential tools like Jira</li><li><strong>Chrome Extension</strong> letting you connect to 20+ development tools through your browser</li><li><strong>Model Context Protocol (MCP)</strong> support for advanced connectivity using an open standard</li></ul>

<h2>What You Can Do with Zencoder</h2>

<h3>Coding Agent</h3>

<p>The Coding Agent creates, modifies, and refactors code across multiple files and languages with a single prompt. It automatically follows your naming conventions, imports the right dependencies according to your codebase standards. Whether extending an existing project or starting from scratch, it delivers code that works right away.</p>

<p><img src="https://zencoder.ai/hubfs/ide-marketplaces/jetbrains/chat-assistant.gif" alt="Coding Agent" /></p>

<h3>Unit Testing Agent</h3>

<p>Automatically generate comprehensive test suites that follow your project's testing patterns. The Unit Testing Agent creates tests with proper setup, assertions, and edge case handling. It analyzes your code's behavior to identify potential edge cases and generates appropriate test scenarios, ensuring thorough coverage.</p>

<p><img src="https://zencoder.ai/hubfs/ide-marketplaces/jetbrains/unit-tests.gif" alt="Unit Testing" /></p>

<h3>Chat Assistant</h3>

<p>Ask questions about your code, request explanations, or seek guidance on best practices with an AI assistant that understands your codebase context. The Chat Assistant leverages Repo Grokking™ to provide answers that are specific to your project's architecture and implementation details.</p>

<p><img src="https://zencoder.ai/hubfs/ide-marketplaces/jetbrains/chat-assistant.gif" alt="Chat Assistant" /></p>

<h3>Code Completion</h3>

<p>Get context-aware code suggestions that understand your project's specific patterns and conventions, helping you write code faster and with fewer errors.</p>

<p><img src="https://zencoder.ai/hubfs/ide-marketplaces/jetbrains/code-completion.gif" alt="Code Completion" /></p>

<h3>Documentation Generation</h3>

<p>Create comprehensive documentation and docstrings that match your codebase style. Accurately document parameters, return values, and exceptions based on actual usage patterns letting you generate contextually accurate documentation that reflects real-world usage.</p>

<p><img src="https://zencoder.ai/hubfs/ide-marketplaces/jetbrains/documentation-generation.gif" alt="Documentation" /></p>

<h2>Installation and Configuration</h2>

<ol><li>Open your JetBrains IDE (IntelliJ IDEA, PyCharm, WebStorm, etc.)</li><li>Access the Plugin view:<ul><li><strong>Windows</strong>: Go to File | Settings (or press <code>Ctrl+Alt+S</code>)</li><li><strong>Mac</strong>: Go to the IDE name in the menu bar | Preferences (or press <code>Cmd+,</code>)</li></ul></li><li>Click <strong>Plugins</strong> in the left-hand pane, then the <strong>Marketplace</strong> tab at the top</li><li>Search for &quot;Zencoder&quot; in the search bar</li><li>Click the <strong>Install</strong> button next to the Zencoder plugin</li><li>Restart your IDE if prompted to activate the plugin</li><li>You might need to configure it with your Zencoder account details. Follow any on-screen instructions to complete the configuration. If prompted, log in with your Zencoder account credentials.</li><li>Look for the Zencoder icon or commands within the editor to access all features</li></ol>

<h2>Community, Feedback and Support</h2>

<p>Join our vibrant communities to get the most out of Zencoder:</p>

<ul><li><strong><a href="https://join.slack.com/t/zencoder-community/shared_invite/zt-2k6o9dts3-JKuYxzJs0J~CFvVa6hIAqA">Slack Community</a></strong> - Connect with developers and the Zencoder team</li><li><strong><a href="https://discord.gg/YjNYBHg8Vb">Discord Server</a></strong> - Engage through topic-focused channels and live discussions</li></ul>

<h3>Socials</h3>

<ul><li><strong><a href="https://x.com/zencoderai">Twitter/X</a></strong> - Follow for quick updates and announcements</li><li><strong><a href="https://www.linkedin.com/company/zencoderai">LinkedIn</a></strong> - Professional insights and company news</li><li><strong><a href="https://www.youtube.com/@zencoderai">YouTube</a></strong> - Tutorials, demos, and feature walkthroughs</li></ul>

<h3>Documentation and Resources</h3>

<ul><li><strong><a href="https://docs.zencoder.ai">Docs</a></strong> - Comprehensive guides, changelog, FAQs and tutorials</li><li><strong><a href="https://zencoder.ai/feedback/in-trial">Feedback</a></strong> - We’d love to get your help in making Zencoder better! If you have feedback or encounter any problems, please reach out on our</li><li><strong><a href="https://zencoder.ai/">Zencoder website</a></strong></li><li><strong><EMAIL></strong> to get help with account-specific issues</li></ul>

<h2>Legal Information</h2>

<ul><li><strong><a href="https://zencoder.ai/terms">Terms of Service</a></strong>: Review our terms of service</li><li><strong><a href="https://zencoder.ai/privacy">Privacy Policy</a></strong>: Learn how we handle your data</li></ul>

<h2></h2>

<p>We hope you enjoy using Zencoder.ai and experience the future of coding!</p>]]></description>
  <version>2.12.1</version>
  <id>ai.zencoder.plugin</id>
  <name>Zencoder: Your Mindful AI Coding Agent</name>
  <vendor email="<EMAIL>" url="https://zencoder.ai">ZencoderAI</vendor>
  <depends>com.intellij.modules.platform</depends>
  <depends>com.intellij.modules.lang</depends>
  <depends>org.jetbrains.plugins.textmate</depends>
  <depends>com.jetbrains.sh</depends>
  <depends>Git4Idea</depends>
  <incompatible-with>com.intellij.modules.mps</incompatible-with>
  <depends optional="true" config-file="ides/ai.zencoder.plugin.kotlin.xml">org.jetbrains.kotlin</depends>
  <depends optional="true" config-file="ides/ai.zencoder.plugin.javascript.xml">JavaScript</depends>
  <depends optional="true" config-file="ides/ai.zencoder.plugin.java.xml">com.intellij.modules.java</depends>
  <depends optional="true" config-file="ides/ai.zencoder.plugin.python.xml">com.intellij.modules.python</depends>
  <depends optional="true" config-file="ides/ai.zencoder.plugin.php.xml">com.jetbrains.php</depends>
  <depends optional="true" config-file="ides/ai.zencoder.plugin.go.xml">org.jetbrains.plugins.go</depends>
  <extensions defaultExtensionNs="org.jetbrains.kotlin">
    <supportsKotlinPluginMode supportsK2="true" />
  </extensions>
  <extensions defaultExtensionNs="com.intellij">
    <toolWindow id="Zencoder" icon="/zencoder/progress/original.svg" anchor="right" factoryClass="ai.zencoder.plugin.webview.chat.ChatToolWindowFactory" />
    <toolWindow id="MCP Tools" icon="/icons/mcp.svg" anchor="right" factoryClass="ai.zencoder.plugin.ui.tools.MCPToolsWindowFactory" />
    <inline.completion.provider id="ZencoderCompletionProvider" implementation="ai.zencoder.plugin.providers.completion.ZencoderCompletionProvider" />
    <editorFactoryListener implementation="ai.zencoder.plugin.providers.completion.ZenEditorFactoryListener" />
    <editorFactoryListener implementation="ai.zencoder.plugin.providers.completion.nextchange.HistoryTrackingEditorFactoryListener" />
    <fileBasedIndex implementation="ai.zencoder.plugin.services.IdentifierIndex" />
    <registryKey key="zencoder.featureFlags" defaultValue="{}" description="Zencoder feature flags" />
  </extensions>
  <extensionPoints>
    <extensionPoint beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true" name="contextGather">
      <with attribute="implementationClass" implements="ai.zencoder.plugin.context.ContextGather" />
    </extensionPoint>
    <extensionPoint beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true" name="zenProjectService">
      <with attribute="implementationClass" implements="ai.zencoder.plugin.services.ProjectService" />
    </extensionPoint>
    <extensionPoint beanClass="com.intellij.lang.LanguageExtensionPoint" dynamic="true" name="zenTerminalActionsService">
      <with attribute="implementationClass" implements="ai.zencoder.plugin.services.lang.LangSpecificTerminalActionsService" />
    </extensionPoint>
  </extensionPoints>
  <projectListeners>
    <listener class="ai.zencoder.plugin.webview.ZoomChangeListener" topic="com.intellij.ide.ui.UISettingsListener" />
    <listener class="ai.zencoder.plugin.editor.OpenTabListener" topic="com.intellij.openapi.fileEditor.FileEditorManagerListener" />
    <listener class="ai.zencoder.plugin.webview.chat.ChatToolWindowListener" topic="com.intellij.openapi.wm.ex.ToolWindowManagerListener" />
  </projectListeners>
  <applicationListeners>
    <listener class="ai.zencoder.plugin.providers.completion.SmartEnterCodeCompletionAnActionListener" topic="com.intellij.openapi.actionSystem.ex.AnActionListener" />
    <listener class="ai.zencoder.plugin.webview.ThemeChangeListener" topic="com.intellij.ide.ui.LafManagerListener" />
  </applicationListeners>
  <resource-bundle>messages.zencoder</resource-bundle>
  <actions>
    <action class="ai.zencoder.plugin.providers.completion.ui.ZenCompletionFeedbackAction" id="zencoder.completionFeedbackAction" description="Send feedback for completion">
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt shift F" />
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta alt shift F" />
    </action>
    <action id="zencoder.acceptNextPrediction" class="ai.zencoder.plugin.providers.completion.nextchange.AcceptNextPredictionRevisionAction">
      <keyboard-shortcut first-keystroke="TAB" keymap="$default" />
    </action>
    <action id="zencoder.triggerCustomPrediction" class="ai.zencoder.plugin.providers.completion.nextchange.TriggerCustomPredictionAction">
      <keyboard-shortcut first-keystroke="alt shift ENTER" keymap="$default" />
    </action>
    <action class="ai.zencoder.plugin.codegen.CodeGenAction" id="zencoder.codeGenAction" description="Generate code" text="Generate Code">
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl I" />
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl shift I" />
    </action>
    <group id="ZencoderEditorPopup">
      <group id="zencoder.editorActionGroup" text="Zencoder" class="com.intellij.openapi.actionSystem.DefaultActionGroup" popup="true" icon="/zencoder/progress/original.svg" />
      <add-to-group group-id="EditorPopupMenu1" anchor="first" />
      <separator />
    </group>
    <group id="zencoder.projectViewActionGroup">
      <separator />
      <group id="zencoder.projectViewActionPopup" text="Zencoder" class="com.intellij.openapi.actionSystem.DefaultActionGroup" popup="true" icon="/zencoder/progress/original.svg" />
      <add-to-group group-id="ProjectViewPopupMenu" anchor="after" relative-to-action="CutCopyPasteGroup" />
      <separator />
    </group>
    <action class="ai.zencoder.plugin.chat.action.InsertCodeToChatAction" id="zencoder.insertCodeToChatAction" text="Insert Code to Chat" description="Insert code to chat">
      <add-to-group group-id="zencoder.editorActionGroup" />
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl shift L" />
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta shift L" />
    </action>
    <action class="ai.zencoder.plugin.chat.action.AddFileToChatAction" id="zencoder.addFileToChatAction" text="Add File to Chat" description="Add file to chat">
      <add-to-group group-id="zencoder.editorActionGroup" anchor="after" relative-to-action="zencoder.insertCodeToChatAction" />
      <add-to-group group-id="zencoder.projectViewActionPopup" anchor="last" />
    </action>
    <action class="ai.zencoder.plugin.chat.action.OpenAgentsMenuAction" id="zencoder.openAgentsMenuAction" text="Open Agents Menu" description="Open agents selection menu">
      <add-to-group group-id="zencoder.editorActionGroup" />
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl PERIOD" />
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta PERIOD" />
    </action>
    <action class="ai.zencoder.plugin.chat.action.ExplainChatAction" id="zencoder.explainCommandAction" text="Explain with Zencoder" description="Explain with Zencoder">
      <add-to-group group-id="zencoder.editorActionGroup" />
    </action>
    <action class="ai.zencoder.plugin.codegen.CodeGenAction" id="zencoder.multiLineCodeGenAction" description="Generate code" text="Generate or Edit Code">
      <add-to-group group-id="zencoder.editorActionGroup" />
    </action>
    <group id="zencoder.generate.menu.group" text="Zencoder" class="com.intellij.openapi.actionSystem.DefaultActionGroup" icon="/zencoder/progress/original.svg">
      <add-to-group group-id="GenerateGroup" anchor="last" />
      <separator />
    </group>
  </actions>
  <!--    <projectListeners>-->
  <!--        <listener-->
  <!--                class="ai.zencoder.plugin.listeners.CommitListener"-->
  <!--                topic="com.intellij.openapi.vcs.VcsListener"/>-->
  <!--    </projectListeners>-->
  <extensions defaultExtensionNs="com.intellij">
    <projectConfigurable id="ai.zencoder.plugin.settings" groupId="tools" instance="ai.zencoder.plugin.settings.ZencoderSettingsConfigurable" displayName="Zencoder" nonDefaultProject="false" />
    <applicationService serviceImplementation="ai.zencoder.plugin.settings.ZencoderSettings" />
    <projectConfigurable id="ai.zencoder.plugin.mcp.MCPServersStateConfigurable" parentId="ai.zencoder.plugin.settings" displayName="MCP Servers" instance="ai.zencoder.plugin.mcp.MCPServersStateConfigurable" nonDefaultProject="true" />
    <errorHandler implementation="ai.zencoder.plugin.log.SentryErrorSubmitter" />
    <editorFactoryListener implementation="ai.zencoder.plugin.codegen.ZencoderEditorFactoryListener" />
    <postStartupActivity implementation="ai.zencoder.plugin.ZencoderStartupActivity" />
    <notificationGroup id="Zencoder Notification Group" displayType="BALLOON" key="notification.group.name" />
    <applicationService serviceInterface="ai.zencoder.plugin.auth.AuthService" serviceImplementation="ai.zencoder.plugin.auth.AuthCodeFlowService" />
    <applicationService serviceInterface="ai.zencoder.plugin.providers.completion.CodeSuggestionService" serviceImplementation="ai.zencoder.plugin.providers.completion.CodeSuggestionServiceImpl" />
    <applicationService serviceImplementation="ai.zencoder.plugin.services.IdentifierIndexDumpService" />
    <applicationService serviceImplementation="ai.zencoder.plugin.chat.agentmodels.LLMSettingsState" />
    <projectService serviceInterface="ai.zencoder.plugin.services.lang.LangSpecificTerminalActionsService" serviceImplementation="ai.zencoder.plugin.services.lang.DefaultLangSpecificTerminalActionsService" />
    <!-- Next Edit Prediction Services -->
    <projectService serviceInterface="ai.zencoder.plugin.providers.completion.nextchange.FileHistoryProvider" serviceImplementation="ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService" />
    <highlightingPassFactory implementation="ai.zencoder.plugin.chat.inspection.CommandQuickFixHighlightPassFactory" order="last" />
    <httpRequestHandler implementation="ai.zencoder.plugin.webextension.server.WebExtensionRestHandler" />
  </extensions>
</idea-plugin>
