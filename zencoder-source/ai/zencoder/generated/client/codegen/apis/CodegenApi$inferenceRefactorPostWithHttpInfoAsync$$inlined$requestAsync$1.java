package ai.zencoder.generated.client.codegen.apis;

import ai.zencoder.generated.client.codegen.infrastructure.ApiClient;
import ai.zencoder.generated.client.codegen.infrastructure.Serializer;
import ai.zencoder.generated.client.codegen.models.CodeEditResponse;
import ai.zencoder.plugin.api.exception.RateLimitException;
import ai.zencoder.plugin.api.exception.RateLimitExceptionKt;
import java.io.Closeable;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.Locale;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import kotlin.Metadata;
import kotlin.io.ByteStreamsKt;
import kotlin.io.CloseableKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.text.StringsKt;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;
import okhttp3.ResponseBody;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000%\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000*\u0001\u0000\b\n\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0016J\u0018\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\nH\u0016\u00a8\u0006\u000b\u00b8\u0006\u0000"},
   d2 = {"ai/zencoder/generated/client/codegen/infrastructure/ApiClient$requestAsync$1", "Lokhttp3/Callback;", "onFailure", "", "call", "Lokhttp3/Call;", "e", "Ljava/io/IOException;", "onResponse", "response", "Lokhttp3/Response;", "zencoder"}
)
@SourceDebugExtension({"SMAP\nApiClient.kt\nKotlin\n*S Kotlin\n*F\n+ 1 ApiClient.kt\nai/zencoder/generated/client/codegen/infrastructure/ApiClient$requestAsync$1\n+ 2 ApiClient.kt\nai/zencoder/generated/client/codegen/infrastructure/ApiClient\n*L\n1#1,407:1\n151#2,64:408\n*S KotlinDebug\n*F\n+ 1 ApiClient.kt\nai/zencoder/generated/client/codegen/infrastructure/ApiClient$requestAsync$1\n*L\n389#1:408,64\n*E\n"})
public final class CodegenApi$inferenceRefactorPostWithHttpInfoAsync$$inlined$requestAsync$1 implements Callback {
   public CodegenApi$inferenceRefactorPostWithHttpInfoAsync$$inlined$requestAsync$1(CompletableFuture $future, ApiClient $receiver) {
      this.$future = $future;
      this.this$0 = $receiver;
   }

   public void onFailure(Call call, IOException e) {
      Intrinsics.checkNotNullParameter(call, "");
      Intrinsics.checkNotNullParameter(e, "");
      this.$future.completeExceptionally((Throwable)e);
   }

   public void onResponse(Call call, Response response) {
      String var50;
      label355: {
         Intrinsics.checkNotNullParameter(call, "");
         Intrinsics.checkNotNullParameter(response, "");
         var50 = Response.header$default(response, "Content-Type", (String)null, 2, (Object)null);
         if (var50 != null) {
            var50 = StringsKt.substringBefore$default(var50, ";", (String)null, 2, (Object)null);
            if (var50 != null) {
               String var3 = var50;
               Locale var51 = Locale.US;
               Intrinsics.checkNotNullExpressionValue(var51, "");
               var50 = var3.toLowerCase(var51);
               Intrinsics.checkNotNullExpressionValue(var50, "");
               break label355;
            }
         }

         var50 = null;
      }

      String var4 = var50;
      if (!response.isSuccessful()) {
         if (response.code() == 429) {
            var50 = response.headers().get("retry-after");
            Long var5 = var50 != null ? StringsKt.toLongOrNull(var50) : null;
            String var6 = response.headers().get("quota-key");
            String var37 = response.headers().get("x-limit-reason");
            RateLimitExceptionKt.a(this.this$0.b(), var5, var6);
            this.$future.completeExceptionally((Throwable)(new RateLimitException(var5, var37, (String)null, (String)null, (Throwable)null, 28, (DefaultConstructorMarker)null)));
         } else {
            this.$future.completeExceptionally((Throwable)(new IOException("Unexpected response code: " + response.code())));
         }
      } else {
         ApiClient var39 = this.this$0;
         CompletableFuture var7 = this.$future;
         boolean var40 = false;
         ResponseBody var38 = response.body();
         Object var54;
         if (var38 == null) {
            var54 = null;
         } else if (Intrinsics.areEqual(CodeEditResponse.class, File.class)) {
            String var41 = Response.header$default(response, "Content-Disposition", (String)null, 2, (Object)null);
            String var55;
            if (var41 != null) {
               Pattern var9 = Pattern.compile("filename=['\"]?([^'\"\\s]+)['\"]?");
               Matcher var10 = var9.matcher((CharSequence)var41);
               if (var10.find()) {
                  label333: {
                     String var11 = var10.group(1);
                     if (var11 != null) {
                        String var12 = StringsKt.replace$default(var11, ".*[/\\\\]", "", false, 4, (Object)null);
                        if (var12 != null) {
                           var55 = StringsKt.replace$default(var12, ";", "", false, 4, (Object)null);
                           break label333;
                        }
                     }

                     var55 = null;
                  }
               } else {
                  var55 = null;
               }
            } else {
               var55 = null;
            }

            String var13 = var55;
            Object var42 = null;
            Object var44 = null;
            String var43;
            String var45;
            if (var13 == null) {
               var43 = "download";
               var45 = "";
            } else {
               int var46 = StringsKt.lastIndexOf$default((CharSequence)var13, ".", 0, false, 6, (Object)null);
               if (var46 == -1) {
                  var43 = var13;
                  var45 = null;
               } else {
                  var55 = var13.substring(0, var46);
                  Intrinsics.checkNotNullExpressionValue(var55, "");
                  var43 = var55;
                  var55 = var13.substring(var46);
                  Intrinsics.checkNotNullExpressionValue(var55, "");
                  var45 = var55;
               }

               if (var43.length() < 3) {
                  var43 = "download";
               }
            }

            File var47 = Files.createTempFile(var43, var45).toFile();
            var47.deleteOnExit();
            Closeable var48 = (Closeable)var38.byteStream();
            Throwable var14 = null;

            try {
               InputStream var15 = (InputStream)var48;
               boolean var16 = false;
               Intrinsics.checkNotNull(var47);
               Closeable var17 = (Closeable)(new FileOutputStream(var47));
               Throwable var18 = null;

               try {
                  FileOutputStream var19 = (FileOutputStream)var17;
                  boolean var20 = false;
                  long var21 = ByteStreamsKt.copyTo$default(var15, (OutputStream)var19, 0, 2, (Object)null);
               } catch (Throwable var33) {
                  var18 = var33;
                  throw var33;
               } finally {
                  CloseableKt.closeFinally(var17, var18);
               }
            } catch (Throwable var35) {
               var14 = var35;
               throw var35;
            } finally {
               CloseableKt.closeFinally(var48, var14);
            }

            var54 = (CodeEditResponse)var47;
         } else if (var4 == null || StringsKt.startsWith$default(var4, "application/", false, 2, (Object)null) && StringsKt.endsWith$default(var4, "json", false, 2, (Object)null)) {
            String var8 = var38.string();
            var54 = ((CharSequence)var8).length() == 0 ? null : Serializer.a().readValue(var8, new CodegenApi$inferenceRefactorPostWithHttpInfoAsync$$inlined$requestAsync$1$1());
         } else {
            if (!Intrinsics.areEqual(var4, "application/octet-stream")) {
               throw new UnsupportedOperationException("responseBody currently only supports JSON body.");
            }

            byte[] var53 = var38.bytes();
            if (!(var53 instanceof CodeEditResponse)) {
               var53 = null;
            }

            var54 = (CodeEditResponse)var53;
         }

         var7.complete(var54);
      }

   }
}
