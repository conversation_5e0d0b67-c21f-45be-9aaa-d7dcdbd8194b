package ai.zencoder.generated.client.codegen.apis;

import ai.zencoder.generated.client.codegen.infrastructure.ApiClient;
import ai.zencoder.generated.client.codegen.infrastructure.ApiResponse;
import ai.zencoder.generated.client.codegen.infrastructure.ClientError;
import ai.zencoder.generated.client.codegen.infrastructure.ClientException;
import ai.zencoder.generated.client.codegen.infrastructure.RequestConfig;
import ai.zencoder.generated.client.codegen.infrastructure.RequestMethod;
import ai.zencoder.generated.client.codegen.infrastructure.ServerError;
import ai.zencoder.generated.client.codegen.infrastructure.ServerException;
import ai.zencoder.generated.client.codegen.infrastructure.Success;
import ai.zencoder.generated.client.codegen.models.CodeEditRequest;
import ai.zencoder.generated.client.codegen.models.CodeEditResponse;
import ai.zencoder.plugin.api.exception.RateLimitException;
import ai.zencoder.plugin.api.exception.RateLimitExceptionKt;
import ai.zencoder.plugin.api.exception.RequestDelayedException;
import java.io.IOException;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.Future;
import kotlin.Lazy;
import kotlin.LazyKt;
import kotlin.Metadata;
import kotlin.NoWhenBranchMatchedException;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.Reflection;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.reflect.KProperty1;
import kotlin.reflect.full.KClasses;
import kotlin.text.StringsKt;
import okhttp3.Call;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B\u001b\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000e\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u000f2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00122\u0006\u0010\n\u001a\u00020\u000bJ\u0010\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u0003H\u0002J\u0010\u0010\u0015\u001a\u00020\u00002\b\u0010\u0016\u001a\u0004\u0018\u00010\u0003\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/codegen/apis/CodegenApi;", "Lai/zencoder/generated/client/codegen/infrastructure/ApiClient;", "basePath", "", "client", "Lokhttp3/OkHttpClient;", "<init>", "(Ljava/lang/String;Lokhttp3/OkHttpClient;)V", "inferenceRefactorPost", "Lai/zencoder/generated/client/codegen/models/CodeEditResponse;", "codeEditRequest", "Lai/zencoder/generated/client/codegen/models/CodeEditRequest;", "inferenceRefactorPostAsync", "Ljava/util/concurrent/Future;", "inferenceRefactorPostWithHttpInfo", "Lai/zencoder/generated/client/codegen/infrastructure/ApiResponse;", "inferenceRefactorPostWithHttpInfoAsync", "inferenceRefactorPostRequestConfig", "Lai/zencoder/generated/client/codegen/infrastructure/RequestConfig;", "encodeURIComponent", "uriComponent", "withAuth", "token", "Companion", "zencoder"}
)
@SourceDebugExtension({"SMAP\nCodegenApi.kt\nKotlin\n*S Kotlin\n*F\n+ 1 CodegenApi.kt\nai/zencoder/generated/client/codegen/apis/CodegenApi\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n+ 3 fake.kt\nkotlin/jvm/internal/FakeKt\n+ 4 ApiClient.kt\nai/zencoder/generated/client/codegen/infrastructure/ApiClient\n+ 5 _Maps.kt\nkotlin/collections/MapsKt___MapsKt\n*L\n1#1,197:1\n288#2,2:198\n1855#2:212\n1856#2:215\n1855#2:558\n1856#2:561\n1#3:200\n227#4,9:201\n236#4:211\n237#4,2:213\n239#4:216\n240#4,24:218\n100#4,49:242\n264#4,3:291\n100#4,49:294\n267#4:343\n100#4,49:344\n268#4:393\n100#4,49:394\n269#4,24:443\n151#4,64:467\n293#4,16:531\n322#4,9:547\n331#4:557\n332#4,2:559\n334#4:562\n335#4,24:564\n100#4,49:588\n359#4,3:637\n100#4,49:640\n362#4:689\n100#4,49:690\n363#4:739\n100#4,49:740\n364#4,7:789\n393#4:796\n215#5:210\n216#5:217\n215#5:556\n216#5:563\n*S KotlinDebug\n*F\n+ 1 CodegenApi.kt\nai/zencoder/generated/client/codegen/apis/CodegenApi\n*L\n72#1:198,2\n139#1:212\n139#1:215\n158#1:558\n158#1:561\n139#1:201,9\n139#1:211\n139#1:213,2\n139#1:216\n139#1:218,24\n139#1:242,49\n139#1:291,3\n139#1:294,49\n139#1:343\n139#1:344,49\n139#1:393\n139#1:394,49\n139#1:443,24\n139#1:467,64\n139#1:531,16\n158#1:547,9\n158#1:557\n158#1:559,2\n158#1:562\n158#1:564,24\n158#1:588,49\n158#1:637,3\n158#1:640,49\n158#1:689\n158#1:690,49\n158#1:739\n158#1:740,49\n158#1:789,7\n158#1:796\n139#1:210\n139#1:217\n158#1:556\n158#1:563\n*E\n"})
public final class CodegenApi extends ApiClient {
   @NotNull
   public static final Companion Companion = new Companion((DefaultConstructorMarker)null);
   @NotNull
   private static final Lazy<String> defaultBasePath$delegate = LazyKt.lazy(CodegenApi::f);

   public CodegenApi(@NotNull String basePath, @NotNull OkHttpClient client) {
      Intrinsics.checkNotNullParameter(basePath, "");
      Intrinsics.checkNotNullParameter(client, "");
      super(basePath, (Call.Factory)client);
   }

   @NotNull
   public final CodeEditResponse a(@NotNull CodeEditRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      ApiResponse var2 = this.a(var1);
      Iterable var5 = (Iterable)KClasses.getDeclaredMemberProperties(Reflection.getOrCreateKotlinClass(var1.getClass()));
      boolean var6 = false;
      Iterator var7 = var5.iterator();

      Object var10000;
      while(true) {
         if (!var7.hasNext()) {
            var10000 = null;
            break;
         }

         Object var8 = var7.next();
         KProperty1 var9 = (KProperty1)var8;
         boolean var10 = false;
         if (Intrinsics.areEqual(var9.getName(), "requestId") || Intrinsics.areEqual(var9.getName(), "operationId")) {
            var10000 = var8;
            break;
         }
      }

      label80: {
         KProperty1 var22 = (KProperty1)var10000;
         if (var22 != null) {
            KProperty1.Getter var23 = var22.getGetter();
            if (var23 != null) {
               Object[] var14 = new Object[]{var1};
               var10000 = var23.call(var14);
               break label80;
            }
         }

         var10000 = null;
      }

      Object var4 = var10000;
      String var3 = var4 instanceof String ? (String)var4 : null;
      switch (CodegenApi.WhenMappings.$EnumSwitchMapping$0[var2.a().ordinal()]) {
         case 1:
            Intrinsics.checkNotNull(var2);
            var10000 = ((Success)var2).a();
            Intrinsics.checkNotNull(var10000);
            return (CodeEditResponse)var10000;
         case 2:
            throw new UnsupportedOperationException("Client does not support Informational responses.");
         case 3:
            throw new UnsupportedOperationException("Client does not support Redirection responses.");
         case 4:
            Intrinsics.checkNotNull(var2);
            ClientError var13 = (ClientError)var2;
            if (var13.a() != 429) {
               StringBuilder var18 = new StringBuilder();
               boolean var21 = false;
               int var10001 = var13.a();
               String var34 = var13.a();
               if (var34 == null) {
                  var34 = "";
               }

               var18.append("Client error : " + var10001 + " " + var34);
               if (var3 != null) {
                  boolean var11 = false;
                  var18.append(" Request ID: " + var3);
               }

               String var32 = var18.toString();
               Intrinsics.checkNotNullExpressionValue(var32, "");
               String var16 = var32;
               throw new ClientException(var16, var13.a(), var2);
            } else {
               label72: {
                  List var26 = (List)var13.a().get("retry-after");
                  if (var26 != null) {
                     String var27 = (String)CollectionsKt.firstOrNull(var26);
                     if (var27 != null) {
                        var28 = StringsKt.toLongOrNull(var27);
                        break label72;
                     }
                  }

                  var28 = null;
               }

               Long var15 = var28;
               List var29 = (List)var13.a().get("quota-key");
               String var17 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-request-delayed");
               String var19 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-limit-reason");
               String var20 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               RateLimitExceptionKt.a(this.b(), var15, var17);
               if (Intrinsics.areEqual(var19, "1") && var15 != null) {
                  throw new RequestDelayedException(var15);
               }

               throw new RateLimitException(var15, var20, (String)null, (String)null, (Throwable)null, 28, (DefaultConstructorMarker)null);
            }
         case 5:
            Intrinsics.checkNotNull(var2);
            ServerError var12 = (ServerError)var2;
            ServerException var25 = new ServerException;
            int var10002 = var12.a();
            String var10003 = var12.a();
            if (var10003 == null) {
               var10003 = "";
            }

            var25.<init>("Server error : " + var10002 + " " + var10003 + " " + var12.a(), var12.a(), var2);
            throw var25;
         default:
            throw new NoWhenBranchMatchedException();
      }
   }

   @NotNull
   public final Future<CodeEditResponse> a(@NotNull CodeEditRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      Future var2 = this.b(var1);
      return var2;
   }

   @NotNull
   public final ApiResponse<CodeEditResponse> a(@NotNull CodeEditRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final Future<CodeEditResponse> b(@NotNull CodeEditRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final RequestConfig<CodeEditRequest> a(@NotNull CodeEditRequest var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      Map var3 = (Map)(new LinkedHashMap());
      Map var4 = (Map)(new LinkedHashMap());
      var4.put("Content-Type", "application/json");
      var4.put("Accept", "application/json");
      RequestMethod var5 = RequestMethod.POST;
      return new RequestConfig<CodeEditRequest>(var5, "/edit", var4, (Map)null, var3, false, var1, 8, (DefaultConstructorMarker)null);
   }

   private final String a(String var1) {
      return (String)(new HttpUrl.Builder()).scheme("http").host("localhost").addPathSegment(var1).build().encodedPathSegments().get(0);
   }

   @NotNull
   public final CodegenApi a(@Nullable String var1) {
      ApiClient.Companion var10000 = ApiClient.Companion;
      String var10001 = var1;
      if (var1 == null) {
         var10001 = "";
      }

      var10000.c(var10001);
      return this;
   }

   private static final String f() {
      return System.getProperties().getProperty("ai.zencoder.generated.client.codegen.baseUrl", "/codegen");
   }

   public CodegenApi() {
      this((String)null, (OkHttpClient)null, 3, (DefaultConstructorMarker)null);
   }

   @NotNull
   public static final String a() {
      return Companion.a();
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R!\u0010\u0004\u001a\u00020\u00058FX\u0087\u0084\u0002\u00a2\u0006\u0012\n\u0004\b\t\u0010\n\u0012\u0004\b\u0006\u0010\u0003\u001a\u0004\b\u0007\u0010\b\u00a8\u0006\u000b"},
      d2 = {"Lai/zencoder/generated/client/codegen/apis/CodegenApi$Companion;", "", "<init>", "()V", "defaultBasePath", "", "getDefaultBasePath$annotations", "getDefaultBasePath", "()Ljava/lang/String;", "defaultBasePath$delegate", "Lkotlin/Lazy;", "zencoder"}
   )
   public static final class Companion {
      private Companion() {
      }

      @NotNull
      public final String a() {
         Lazy var1 = CodegenApi.defaultBasePath$delegate;
         Object var10000 = var1.getValue();
         Intrinsics.checkNotNullExpressionValue(var10000, "");
         return (String)var10000;
      }
   }
}
