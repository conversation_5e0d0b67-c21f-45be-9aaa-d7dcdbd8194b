package ai.zencoder.generated.client.codegen.infrastructure;

import ai.zencoder.plugin.utils.HttpUtilsKt;
import ai.zencoder.plugin.utils.auth.ZenRefreshAuthenticator;
import io.sentry.okhttp.SentryOkHttpEventListener;
import io.sentry.okhttp.SentryOkHttpInterceptor;
import java.io.File;
import java.net.URLConnection;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import kotlin.Metadata;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.text.StringsKt;
import okhttp3.Call;
import okhttp3.EventListener;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0016\u0018\u0000 &2\u00020\u0001:\u0001&B\u0019\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0010\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u000eH\u0004J(\u0010\u000f\u001a\u00020\u0010\"\u0006\b\u0000\u0010\u0011\u0018\u00012\u0006\u0010\u0012\u001a\u0002H\u00112\b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u0084\b\u00a2\u0006\u0002\u0010\u0014J2\u0010\u0015\u001a\u0004\u0018\u0001H\u0011\"\f\b\u0000\u0010\u0011\u0018\u0001*\u0004\u0018\u00010\u00012\u0006\u0010\u0016\u001a\u00020\u00172\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u0084\b\u00a2\u0006\u0002\u0010\u0018J\u001c\u0010\u0019\u001a\u00020\u001a\"\u0004\b\u0000\u0010\u00112\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u0002H\u00110\u001cH\u0004J5\u0010\u001d\u001a\n\u0012\u0006\u0012\u0004\u0018\u0001H\u00110\u001e\"\u0006\b\u0000\u0010\u001f\u0018\u0001\"\f\b\u0001\u0010\u0011\u0018\u0001*\u0004\u0018\u00010\u00012\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u0002H\u001f0\u001cH\u0084\bJ\u0012\u0010 \u001a\u00020\u00032\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u0004J3\u0010\"\u001a\b\u0012\u0004\u0012\u0002H\u00110#\"\u0006\b\u0000\u0010\u001f\u0018\u0001\"\f\b\u0001\u0010\u0011\u0018\u0001*\u0004\u0018\u00010\u00012\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u0002H\u001f0\u001cH\u0084\bJ\"\u0010$\u001a\u00020\u0003\"\n\b\u0000\u0010\u0011\u0018\u0001*\u00020\u00012\u0006\u0010!\u001a\u0002H\u0011H\u0084\b\u00a2\u0006\u0002\u0010%R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006'"},
   d2 = {"Lai/zencoder/generated/client/codegen/infrastructure/ApiClient;", "", "baseUrl", "", "client", "Lokhttp3/Call$Factory;", "<init>", "(Ljava/lang/String;Lokhttp3/Call$Factory;)V", "getBaseUrl", "()Ljava/lang/String;", "getClient", "()Lokhttp3/Call$Factory;", "guessContentTypeFromFile", "file", "Ljava/io/File;", "requestBody", "Lokhttp3/RequestBody;", "T", "content", "mediaType", "(Ljava/lang/Object;Ljava/lang/String;)Lokhttp3/RequestBody;", "responseBody", "response", "Lokhttp3/Response;", "(Lokhttp3/Response;Ljava/lang/String;)Ljava/lang/Object;", "updateAuthParams", "", "requestConfig", "Lai/zencoder/generated/client/codegen/infrastructure/RequestConfig;", "request", "Lai/zencoder/generated/client/codegen/infrastructure/ApiResponse;", "I", "parameterToString", "value", "requestAsync", "Ljava/util/concurrent/Future;", "parseDateToQueryString", "(Ljava/lang/Object;)Ljava/lang/String;", "Companion", "zencoder"}
)
@SourceDebugExtension({"SMAP\nApiClient.kt\nKotlin\n*S Kotlin\n*F\n+ 1 ApiClient.kt\nai/zencoder/generated/client/codegen/infrastructure/ApiClient\n+ 2 _Maps.kt\nkotlin/collections/MapsKt___MapsKt\n+ 3 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,407:1\n100#1,9:416\n109#1,17:426\n126#1,6:444\n132#1,2:451\n134#1,15:454\n100#1,9:469\n109#1,17:479\n126#1,6:497\n132#1,2:504\n134#1,15:507\n100#1,9:522\n109#1,17:532\n126#1,6:550\n132#1,2:557\n134#1,15:560\n100#1,9:575\n109#1,17:585\n126#1,6:603\n132#1,2:610\n134#1,15:613\n151#1,64:630\n404#1:694\n100#1,9:699\n109#1,17:709\n126#1,6:727\n132#1,2:734\n134#1,15:737\n100#1,9:752\n109#1,17:762\n126#1,6:780\n132#1,2:787\n134#1,15:790\n100#1,9:805\n109#1,17:815\n126#1,6:833\n132#1,2:840\n134#1,15:843\n100#1,9:858\n109#1,17:868\n126#1,6:886\n132#1,2:893\n134#1,15:896\n215#2,2:408\n215#2,2:410\n215#2:412\n216#2:415\n215#2:425\n216#2:443\n215#2:450\n216#2:453\n215#2:478\n216#2:496\n215#2:503\n216#2:506\n215#2:531\n216#2:549\n215#2:556\n216#2:559\n215#2:584\n216#2:602\n215#2:609\n216#2:612\n215#2,2:628\n215#2:695\n216#2:698\n215#2:708\n216#2:726\n215#2:733\n216#2:736\n215#2:761\n216#2:779\n215#2:786\n216#2:789\n215#2:814\n216#2:832\n215#2:839\n216#2:842\n215#2:867\n216#2:885\n215#2:892\n216#2:895\n215#2,2:911\n1855#3,2:413\n1855#3,2:696\n*S KotlinDebug\n*F\n+ 1 ApiClient.kt\nai/zencoder/generated/client/codegen/infrastructure/ApiClient\n*L\n263#1:416,9\n263#1:426,17\n263#1:444,6\n263#1:451,2\n263#1:454,15\n266#1:469,9\n266#1:479,17\n266#1:497,6\n266#1:504,2\n266#1:507,15\n267#1:522,9\n267#1:532,17\n267#1:550,6\n267#1:557,2\n267#1:560,15\n268#1:575,9\n268#1:585,17\n268#1:603,6\n268#1:610,2\n268#1:613,15\n292#1:630,64\n317#1:694\n358#1:699,9\n358#1:709,17\n358#1:727,6\n358#1:734,2\n358#1:737,15\n361#1:752,9\n361#1:762,17\n361#1:780,6\n361#1:787,2\n361#1:790,15\n362#1:805,9\n362#1:815,17\n362#1:833,6\n362#1:840,2\n362#1:843,15\n363#1:858,9\n363#1:868,17\n363#1:886,6\n363#1:893,2\n363#1:896,15\n108#1:408,2\n131#1:410,2\n235#1:412\n235#1:415\n263#1:425\n263#1:443\n263#1:450\n263#1:453\n266#1:478\n266#1:496\n266#1:503\n266#1:506\n267#1:531\n267#1:549\n267#1:556\n267#1:559\n268#1:584\n268#1:602\n268#1:609\n268#1:612\n271#1:628,2\n330#1:695\n330#1:698\n358#1:708\n358#1:726\n358#1:733\n358#1:736\n361#1:761\n361#1:779\n361#1:786\n361#1:789\n362#1:814\n362#1:832\n362#1:839\n362#1:842\n363#1:867\n363#1:885\n363#1:892\n363#1:895\n366#1:911,2\n236#1:413,2\n331#1:696,2\n*E\n"})
public class ApiClient {
   @NotNull
   public static final Companion Companion = new Companion((DefaultConstructorMarker)null);
   @NotNull
   private final String baseUrl;
   @NotNull
   private final Call.Factory client;
   @NotNull
   public static final String CONTENT_TYPE = "Content-Type";
   @NotNull
   public static final String ACCEPT = "Accept";
   @NotNull
   public static final String AUTHORIZATION = "Authorization";
   @NotNull
   public static final String CONTENT_DISPOSITION = "Content-Disposition";
   @NotNull
   protected static final String JSON_MEDIA_TYPE = "application/json";
   @NotNull
   protected static final String FORM_DATA_MEDIA_TYPE = "multipart/form-data";
   @NotNull
   protected static final String FORM_URL_ENC_MEDIA_TYPE = "application/x-www-form-urlencoded";
   @NotNull
   protected static final String XML_MEDIA_TYPE = "application/xml";
   @NotNull
   protected static final String OCTET_MEDIA_TYPE = "application/octet-stream";
   @NotNull
   public static final String RETRY_AFTER = "retry-after";
   @NotNull
   public static final String QUOTA_KEY = "quota-key";
   @NotNull
   public static final String REQUEST_DELAYED = "x-request-delayed";
   @NotNull
   public static final String LIMIT_REASON = "x-limit-reason";
   @NotNull
   private static final Map<String, String> apiKey = (Map)(new LinkedHashMap());
   @NotNull
   private static final Map<String, String> apiKeyPrefix = (Map)(new LinkedHashMap());
   @Nullable
   private static String username;
   @Nullable
   private static String password;
   @Nullable
   private static String accessToken;
   @NotNull
   public static final String BASE_URL_KEY = "ai.zencoder.generated.client.codegen.baseUrl";
   @NotNull
   private static final OkHttpClient defaultClient;
   @NotNull
   private static final OkHttpClient.Builder builder;

   public ApiClient(@NotNull String baseUrl, @NotNull Call.Factory client) {
      Intrinsics.checkNotNullParameter(baseUrl, "");
      Intrinsics.checkNotNullParameter(client, "");
      super();
      this.baseUrl = baseUrl;
      this.client = client;
   }

   @NotNull
   public final String b() {
      return this.baseUrl;
   }

   @NotNull
   public final Call.Factory a() {
      return this.client;
   }

   @NotNull
   protected final String a(@NotNull File var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      String var2 = URLConnection.guessContentTypeFromName(var1.getName());
      String var10000 = var2;
      if (var2 == null) {
         var10000 = "application/octet-stream";
      }

      return var10000;
   }

   protected final <T> void a(@NotNull RequestConfig<T> var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      CharSequence var2 = (CharSequence)var1.a().get("Authorization");
      if (var2 == null || var2.length() == 0) {
         String var10000 = accessToken;
         if (var10000 != null) {
            String var3 = var10000;
            boolean var4 = false;
            var1.a().put("Authorization", "Bearer " + var3 + " ");
         }
      }

   }

   @NotNull
   protected final String a(@Nullable Object var1) {
      String var10000;
      if (var1 == null) {
         var10000 = "";
      } else if (var1 instanceof Object[]) {
         var10000 = ApiAbstractionsKt.a((Object[])(var1), "csv", (Function1)null, 4, (Object)null).toString();
      } else if (var1 instanceof Iterable) {
         var10000 = ApiAbstractionsKt.a((Iterable)((Iterable)var1), "csv", (Function1)null, 4, (Object)null).toString();
      } else if (!(var1 instanceof OffsetDateTime) && !(var1 instanceof OffsetTime) && !(var1 instanceof LocalDateTime) && !(var1 instanceof LocalDate) && !(var1 instanceof LocalTime)) {
         var10000 = var1.toString();
      } else {
         boolean var4 = false;
         var10000 = Serializer.a().writeValueAsString(var1);
         Intrinsics.checkNotNullExpressionValue(var10000, "");
         var10000 = StringsKt.replace$default(var10000, "\"", "", false, 4, (Object)null);
      }

      return var10000;
   }

   @NotNull
   public static final OkHttpClient a() {
      return Companion.a();
   }

   @NotNull
   public static final OkHttpClient.Builder a() {
      return Companion.a();
   }

   static {
      defaultClient = HttpUtilsKt.a((new OkHttpClient()).newBuilder()).eventListener((EventListener)(new SentryOkHttpEventListener())).addInterceptor((Interceptor)(new SentryOkHttpInterceptor())).authenticator(ZenRefreshAuthenticator.INSTANCE).readTimeout(150L, TimeUnit.SECONDS).build();
      builder = HttpUtilsKt.a(new OkHttpClient.Builder()).eventListener((EventListener)(new SentryOkHttpEventListener())).addInterceptor((Interceptor)(new SentryOkHttpInterceptor()));
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010%\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0005X\u0084T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0005X\u0084T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0005X\u0084T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0005X\u0084T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0005X\u0084T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u001d\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u001c\u0010\u0018\u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0019\u0010\u001a\"\u0004\b\u001b\u0010\u001cR\u001c\u0010\u001d\u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001e\u0010\u001a\"\u0004\b\u001f\u0010\u001cR\u001c\u0010 \u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b!\u0010\u001a\"\u0004\b\"\u0010\u001cR\u000e\u0010#\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u001c\u0010$\u001a\u00020%8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b&\u0010\u0003\u001a\u0004\b'\u0010(R\u001c\u0010)\u001a\u00020*8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b+\u0010\u0003\u001a\u0004\b,\u0010-\u00a8\u0006."},
      d2 = {"Lai/zencoder/generated/client/codegen/infrastructure/ApiClient$Companion;", "", "<init>", "()V", "CONTENT_TYPE", "", "ACCEPT", "AUTHORIZATION", "CONTENT_DISPOSITION", "JSON_MEDIA_TYPE", "FORM_DATA_MEDIA_TYPE", "FORM_URL_ENC_MEDIA_TYPE", "XML_MEDIA_TYPE", "OCTET_MEDIA_TYPE", "RETRY_AFTER", "QUOTA_KEY", "REQUEST_DELAYED", "LIMIT_REASON", "apiKey", "", "getApiKey", "()Ljava/util/Map;", "apiKeyPrefix", "getApiKeyPrefix", "username", "getUsername", "()Ljava/lang/String;", "setUsername", "(Ljava/lang/String;)V", "password", "getPassword", "setPassword", "accessToken", "getAccessToken", "setAccessToken", "BASE_URL_KEY", "defaultClient", "Lokhttp3/OkHttpClient;", "getDefaultClient$annotations", "getDefaultClient", "()Lokhttp3/OkHttpClient;", "builder", "Lokhttp3/OkHttpClient$Builder;", "getBuilder$annotations", "getBuilder", "()Lokhttp3/OkHttpClient$Builder;", "zencoder"}
   )
   public static final class Companion {
      private Companion() {
      }

      @NotNull
      public final Map<String, String> a() {
         return ApiClient.apiKey;
      }

      @NotNull
      public final Map<String, String> b() {
         return ApiClient.apiKeyPrefix;
      }

      @Nullable
      public final String a() {
         return ApiClient.username;
      }

      public final void a(@Nullable String var1) {
         ApiClient.username = var1;
      }

      @Nullable
      public final String b() {
         return ApiClient.password;
      }

      public final void b(@Nullable String var1) {
         ApiClient.password = var1;
      }

      @Nullable
      public final String c() {
         return ApiClient.accessToken;
      }

      public final void c(@Nullable String var1) {
         ApiClient.accessToken = var1;
      }

      @NotNull
      public final OkHttpClient a() {
         return ApiClient.defaultClient;
      }

      @NotNull
      public final OkHttpClient.Builder a() {
         return ApiClient.builder;
      }
   }
}
