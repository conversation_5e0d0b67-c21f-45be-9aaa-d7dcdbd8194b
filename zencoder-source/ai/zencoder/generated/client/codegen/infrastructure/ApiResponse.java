package ai.zencoder.generated.client.codegen.infrastructure;

import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010 \n\u0002\b\u0003\b&\u0018\u0000*\u0004\b\u0000\u0010\u00012\u00020\u0002B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0004\b\u0005\u0010\u0006R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0012\u0010\t\u001a\u00020\nX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u000b\u0010\fR$\u0010\r\u001a\u0014\u0012\u0004\u0012\u00020\u000f\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u00100\u000eX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006\u0013"},
   d2 = {"Lai/zencoder/generated/client/codegen/infrastructure/ApiResponse;", "T", "Lai/zencoder/generated/client/codegen/infrastructure/Response;", "responseType", "Lai/zencoder/generated/client/codegen/infrastructure/ResponseType;", "<init>", "(Lai/zencoder/generated/client/codegen/infrastructure/ResponseType;)V", "getResponseType", "()Lai/zencoder/generated/client/codegen/infrastructure/ResponseType;", "statusCode", "", "getStatusCode", "()I", "headers", "", "", "", "getHeaders", "()Ljava/util/Map;", "zencoder"}
)
public abstract class ApiResponse<T> implements Response {
   @NotNull
   private final ResponseType responseType;

   public ApiResponse(@NotNull ResponseType responseType) {
      Intrinsics.checkNotNullParameter(responseType, "");
      super();
      this.responseType = responseType;
   }

   @NotNull
   public final ResponseType a() {
      return this.responseType;
   }

   public abstract int a();

   @NotNull
   public abstract Map<String, List<String>> a();
}
