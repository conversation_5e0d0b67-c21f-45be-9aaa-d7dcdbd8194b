package ai.zencoder.generated.client.codegen.infrastructure;

import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010$\n\u0002\u0010 \n\u0002\b\u000b\u0018\u0000*\u0004\b\u0000\u0010\u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002BE\u0012\n\b\u0002\u0010\u0003\u001a\u0004\u0018\u00010\u0004\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\u001a\b\u0002\u0010\t\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u000b0\n\u00a2\u0006\u0004\b\f\u0010\rR\u0013\u0010\u0003\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0014\u0010\u0007\u001a\u00020\bX\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R&\u0010\t\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u000b0\nX\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/codegen/infrastructure/ClientError;", "T", "Lai/zencoder/generated/client/codegen/infrastructure/ApiResponse;", "message", "", "body", "", "statusCode", "", "headers", "", "", "<init>", "(Ljava/lang/String;Ljava/lang/Object;ILjava/util/Map;)V", "getMessage", "()Ljava/lang/String;", "getBody", "()Ljava/lang/Object;", "getStatusCode", "()I", "getHeaders", "()Ljava/util/Map;", "zencoder"}
)
public final class ClientError<T> extends ApiResponse<T> {
   @Nullable
   private final String message;
   @Nullable
   private final Object body;
   private final int statusCode;
   @NotNull
   private final Map<String, List<String>> headers;

   public ClientError(@Nullable String message, @Nullable Object body, int statusCode, @NotNull Map<String, ? extends List<String>> headers) {
      Intrinsics.checkNotNullParameter(headers, "");
      super(ResponseType.ClientError);
      this.message = message;
      this.body = body;
      this.statusCode = statusCode;
      this.headers = headers;
   }

   @Nullable
   public final String a() {
      return this.message;
   }

   @Nullable
   public final Object a() {
      return this.body;
   }

   public int a() {
      return this.statusCode;
   }

   @NotNull
   public Map<String, List<String>> a() {
      return this.headers;
   }

   public ClientError() {
      this((String)null, (Object)null, 0, (Map)null, 15, (DefaultConstructorMarker)null);
   }
}
