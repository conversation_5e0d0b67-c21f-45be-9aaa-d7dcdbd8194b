package ai.zencoder.generated.client.codegen.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BM\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0006\u0012\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0006\u00a2\u0006\u0004\b\f\u0010\rJ\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\n0\u0006H\u00c6\u0003J\u0011\u0010\u001b\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0006H\u00c6\u0003JY\u0010\u001c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\b\b\u0002\u0010\b\u001a\u00020\u00032\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u00062\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020\nH\u00d6\u0001J\t\u0010!\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u0019\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0013\u0010\b\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000fR\u0019\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u001b\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0012\u00a8\u0006\""},
   d2 = {"Lai/zencoder/generated/client/codegen/models/CodeEditRequest;", "", "requestId", "", "userQuery", "openedTabs", "", "Lai/zencoder/generated/client/codegen/models/TabItem;", "activeFilePath", "selectedCodeSpan", "", "multiShot", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V", "getRequestId", "()Ljava/lang/String;", "getUserQuery", "getOpenedTabs", "()Ljava/util/List;", "getActiveFilePath", "getSelectedCodeSpan", "getMultiShot", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class CodeEditRequest {
   @NotNull
   private final String requestId;
   @NotNull
   private final String userQuery;
   @NotNull
   private final List<TabItem> openedTabs;
   @NotNull
   private final String activeFilePath;
   @NotNull
   private final List<Integer> selectedCodeSpan;
   @Nullable
   private final List<Object> multiShot;

   public CodeEditRequest(@NotNull String requestId, @NotNull String userQuery, @NotNull List<TabItem> openedTabs, @NotNull String activeFilePath, @NotNull List<Integer> selectedCodeSpan, @Nullable List<? extends Object> multiShot) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(userQuery, "");
      Intrinsics.checkNotNullParameter(openedTabs, "");
      Intrinsics.checkNotNullParameter(activeFilePath, "");
      Intrinsics.checkNotNullParameter(selectedCodeSpan, "");
      super();
      this.requestId = requestId;
      this.userQuery = userQuery;
      this.openedTabs = openedTabs;
      this.activeFilePath = activeFilePath;
      this.selectedCodeSpan = selectedCodeSpan;
      this.multiShot = multiShot;
   }

   @JsonProperty("requestId")
   @NotNull
   public final String getRequestId() {
      return this.requestId;
   }

   @JsonProperty("userQuery")
   @NotNull
   public final String getUserQuery() {
      return this.userQuery;
   }

   @JsonProperty("openedTabs")
   @NotNull
   public final List<TabItem> getOpenedTabs() {
      return this.openedTabs;
   }

   @JsonProperty("activeFilePath")
   @NotNull
   public final String getActiveFilePath() {
      return this.activeFilePath;
   }

   @JsonProperty("selectedCodeSpan")
   @NotNull
   public final List<Integer> getSelectedCodeSpan() {
      return this.selectedCodeSpan;
   }

   @JsonProperty("multiShot")
   @Nullable
   public final List<Object> getMultiShot() {
      return this.multiShot;
   }

   @NotNull
   public final String component1() {
      return this.requestId;
   }

   @NotNull
   public final String component2() {
      return this.userQuery;
   }

   @NotNull
   public final List<TabItem> component3() {
      return this.openedTabs;
   }

   @NotNull
   public final String component4() {
      return this.activeFilePath;
   }

   @NotNull
   public final List<Integer> component5() {
      return this.selectedCodeSpan;
   }

   @Nullable
   public final List<Object> component6() {
      return this.multiShot;
   }

   @NotNull
   public final CodeEditRequest copy(@NotNull String requestId, @NotNull String userQuery, @NotNull List<TabItem> openedTabs, @NotNull String activeFilePath, @NotNull List<Integer> selectedCodeSpan, @Nullable List<? extends Object> multiShot) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(userQuery, "");
      Intrinsics.checkNotNullParameter(openedTabs, "");
      Intrinsics.checkNotNullParameter(activeFilePath, "");
      Intrinsics.checkNotNullParameter(selectedCodeSpan, "");
      return new CodeEditRequest(requestId, userQuery, openedTabs, activeFilePath, selectedCodeSpan, multiShot);
   }

   @NotNull
   public String toString() {
      return "CodeEditRequest(requestId=" + this.requestId + ", userQuery=" + this.userQuery + ", openedTabs=" + this.openedTabs + ", activeFilePath=" + this.activeFilePath + ", selectedCodeSpan=" + this.selectedCodeSpan + ", multiShot=" + this.multiShot + ")";
   }

   public int hashCode() {
      int var1 = this.requestId.hashCode();
      var1 = var1 * 31 + this.userQuery.hashCode();
      var1 = var1 * 31 + this.openedTabs.hashCode();
      var1 = var1 * 31 + this.activeFilePath.hashCode();
      var1 = var1 * 31 + this.selectedCodeSpan.hashCode();
      var1 = var1 * 31 + (this.multiShot == null ? 0 : this.multiShot.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CodeEditRequest)) {
         return false;
      } else {
         CodeEditRequest var2 = (CodeEditRequest)other;
         if (!Intrinsics.areEqual(this.requestId, var2.requestId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.userQuery, var2.userQuery)) {
            return false;
         } else if (!Intrinsics.areEqual(this.openedTabs, var2.openedTabs)) {
            return false;
         } else if (!Intrinsics.areEqual(this.activeFilePath, var2.activeFilePath)) {
            return false;
         } else if (!Intrinsics.areEqual(this.selectedCodeSpan, var2.selectedCodeSpan)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.multiShot, var2.multiShot);
         }
      }
   }
}
