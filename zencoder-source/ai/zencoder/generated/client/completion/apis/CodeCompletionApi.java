package ai.zencoder.generated.client.completion.apis;

import ai.zencoder.generated.client.completion.infrastructure.ApiClient;
import ai.zencoder.generated.client.completion.infrastructure.ApiResponse;
import ai.zencoder.generated.client.completion.infrastructure.ClientError;
import ai.zencoder.generated.client.completion.infrastructure.ClientException;
import ai.zencoder.generated.client.completion.infrastructure.RequestConfig;
import ai.zencoder.generated.client.completion.infrastructure.RequestMethod;
import ai.zencoder.generated.client.completion.infrastructure.ServerError;
import ai.zencoder.generated.client.completion.infrastructure.ServerException;
import ai.zencoder.generated.client.completion.infrastructure.Success;
import ai.zencoder.generated.client.completion.models.CodeCompletionRequest;
import ai.zencoder.generated.client.completion.models.CodeCompletionResponse;
import ai.zencoder.generated.client.completion.models.EditPredictionRequest;
import ai.zencoder.generated.client.completion.models.EditPredictionResponse;
import ai.zencoder.plugin.api.exception.RateLimitException;
import ai.zencoder.plugin.api.exception.RateLimitExceptionKt;
import ai.zencoder.plugin.api.exception.RequestDelayedException;
import java.io.IOException;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.Future;
import kotlin.Deprecated;
import kotlin.Lazy;
import kotlin.LazyKt;
import kotlin.Metadata;
import kotlin.NoWhenBranchMatchedException;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.Reflection;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.reflect.KProperty1;
import kotlin.reflect.full.KClasses;
import kotlin.text.StringsKt;
import okhttp3.Call;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\u0018\u0000 %2\u00020\u0001:\u0001%B\u001b\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0007J\u0016\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\n\u001a\u00020\u000bH\u0007J\u0018\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u000f2\u0006\u0010\n\u001a\u00020\u000bH\u0007J\u0016\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\n\u001a\u00020\u000bH\u0007J\u0016\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00122\u0006\u0010\n\u001a\u00020\u000bH\u0007J\u0016\u0010\u0013\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bJ\u001c\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\u0014\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bJ\u001e\u0010\u0016\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u000f2\u0006\u0010\u0014\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bJ\u001c\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\u0014\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bJ\u001c\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00122\u0006\u0010\u0014\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bJ\u000e\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001cJ\u0014\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001a0\r2\u0006\u0010\u001b\u001a\u00020\u001cJ\u0016\u0010\u001e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u001a0\u000f2\u0006\u0010\u001b\u001a\u00020\u001cJ\u0014\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u001a0\r2\u0006\u0010\u001b\u001a\u00020\u001cJ\u0014\u0010 \u001a\b\u0012\u0004\u0012\u00020\u001c0\u00122\u0006\u0010\u001b\u001a\u00020\u001cJ\u0010\u0010!\u001a\u00020\u00032\u0006\u0010\"\u001a\u00020\u0003H\u0002J\u0010\u0010#\u001a\u00020\u00002\b\u0010$\u001a\u0004\u0018\u00010\u0003\u00a8\u0006&"},
   d2 = {"Lai/zencoder/generated/client/completion/apis/CodeCompletionApi;", "Lai/zencoder/generated/client/completion/infrastructure/ApiClient;", "basePath", "", "client", "Lokhttp3/OkHttpClient;", "<init>", "(Ljava/lang/String;Lokhttp3/OkHttpClient;)V", "fireworksFireworksPost", "Lai/zencoder/generated/client/completion/models/CodeCompletionResponse;", "codeCompletionRequest", "Lai/zencoder/generated/client/completion/models/CodeCompletionRequest;", "fireworksFireworksPostAsync", "Ljava/util/concurrent/Future;", "fireworksFireworksPostWithHttpInfo", "Lai/zencoder/generated/client/completion/infrastructure/ApiResponse;", "fireworksFireworksPostWithHttpInfoAsync", "fireworksFireworksPostRequestConfig", "Lai/zencoder/generated/client/completion/infrastructure/RequestConfig;", "inferenceInferencePost", "sentryTrace", "inferenceInferencePostAsync", "inferenceInferencePostWithHttpInfo", "inferenceInferencePostWithHttpInfoAsync", "inferenceInferencePostRequestConfig", "nextEditPredictionPost", "Lai/zencoder/generated/client/completion/models/EditPredictionResponse;", "editPredictionRequest", "Lai/zencoder/generated/client/completion/models/EditPredictionRequest;", "nextEditPredictionPostAsync", "nextEditPredictionPostWithHttpInfo", "nextEditPredictionPostWithHttpInfoAsync", "nextEditPredictionPostRequestConfig", "encodeURIComponent", "uriComponent", "withAuth", "token", "Companion", "zencoder"}
)
@SourceDebugExtension({"SMAP\nCodeCompletionApi.kt\nKotlin\n*S Kotlin\n*F\n+ 1 CodeCompletionApi.kt\nai/zencoder/generated/client/completion/apis/CodeCompletionApi\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n+ 3 fake.kt\nkotlin/jvm/internal/FakeKt\n+ 4 ApiClient.kt\nai/zencoder/generated/client/completion/infrastructure/ApiClient\n+ 5 _Maps.kt\nkotlin/collections/MapsKt___MapsKt\n*L\n1#1,476:1\n288#2,2:477\n1855#2:491\n1856#2:494\n1855#2:837\n1856#2:840\n288#2,2:1076\n1855#2:1089\n1856#2:1092\n1855#2:1435\n1856#2:1438\n288#2,2:1674\n1855#2:1687\n1856#2:1690\n1855#2:2033\n1856#2:2036\n1#3:479\n227#4,9:480\n236#4:490\n237#4,2:492\n239#4:495\n240#4,24:497\n100#4,49:521\n264#4,3:570\n100#4,49:573\n267#4:622\n100#4,49:623\n268#4:672\n100#4,49:673\n269#4,24:722\n151#4,64:746\n293#4,16:810\n322#4,9:826\n331#4:836\n332#4,2:838\n334#4:841\n335#4,24:843\n100#4,49:867\n359#4,3:916\n100#4,49:919\n362#4:968\n100#4,49:969\n363#4:1018\n100#4,49:1019\n364#4,7:1068\n393#4:1075\n227#4,9:1078\n236#4:1088\n237#4,2:1090\n239#4:1093\n240#4,24:1095\n100#4,49:1119\n264#4,3:1168\n100#4,49:1171\n267#4:1220\n100#4,49:1221\n268#4:1270\n100#4,49:1271\n269#4,24:1320\n151#4,64:1344\n293#4,16:1408\n322#4,9:1424\n331#4:1434\n332#4,2:1436\n334#4:1439\n335#4,24:1441\n100#4,49:1465\n359#4,3:1514\n100#4,49:1517\n362#4:1566\n100#4,49:1567\n363#4:1616\n100#4,49:1617\n364#4,7:1666\n393#4:1673\n227#4,9:1676\n236#4:1686\n237#4,2:1688\n239#4:1691\n240#4,24:1693\n100#4,49:1717\n264#4,3:1766\n100#4,49:1769\n267#4:1818\n100#4,49:1819\n268#4:1868\n100#4,49:1869\n269#4,24:1918\n151#4,64:1942\n293#4,16:2006\n322#4,9:2022\n331#4:2032\n332#4,2:2034\n334#4:2037\n335#4,24:2039\n100#4,49:2063\n359#4,3:2112\n100#4,49:2115\n362#4:2164\n100#4,49:2165\n363#4:2214\n100#4,49:2215\n364#4,7:2264\n393#4:2271\n215#5:489\n216#5:496\n215#5:835\n216#5:842\n215#5:1087\n216#5:1094\n215#5:1433\n216#5:1440\n215#5:1685\n216#5:1692\n215#5:2031\n216#5:2038\n*S KotlinDebug\n*F\n+ 1 CodeCompletionApi.kt\nai/zencoder/generated/client/completion/apis/CodeCompletionApi\n*L\n76#1:477,2\n147#1:491\n147#1:494\n168#1:837\n168#1:840\n215#1:1076,2\n284#1:1089\n284#1:1092\n304#1:1435\n304#1:1438\n351#1:1674,2\n418#1:1687\n418#1:1690\n437#1:2033\n437#1:2036\n147#1:480,9\n147#1:490\n147#1:492,2\n147#1:495\n147#1:497,24\n147#1:521,49\n147#1:570,3\n147#1:573,49\n147#1:622\n147#1:623,49\n147#1:672\n147#1:673,49\n147#1:722,24\n147#1:746,64\n147#1:810,16\n168#1:826,9\n168#1:836\n168#1:838,2\n168#1:841\n168#1:843,24\n168#1:867,49\n168#1:916,3\n168#1:919,49\n168#1:968\n168#1:969,49\n168#1:1018\n168#1:1019,49\n168#1:1068,7\n168#1:1075\n284#1:1078,9\n284#1:1088\n284#1:1090,2\n284#1:1093\n284#1:1095,24\n284#1:1119,49\n284#1:1168,3\n284#1:1171,49\n284#1:1220\n284#1:1221,49\n284#1:1270\n284#1:1271,49\n284#1:1320,24\n284#1:1344,64\n284#1:1408,16\n304#1:1424,9\n304#1:1434\n304#1:1436,2\n304#1:1439\n304#1:1441,24\n304#1:1465,49\n304#1:1514,3\n304#1:1517,49\n304#1:1566\n304#1:1567,49\n304#1:1616\n304#1:1617,49\n304#1:1666,7\n304#1:1673\n418#1:1676,9\n418#1:1686\n418#1:1688,2\n418#1:1691\n418#1:1693,24\n418#1:1717,49\n418#1:1766,3\n418#1:1769,49\n418#1:1818\n418#1:1819,49\n418#1:1868\n418#1:1869,49\n418#1:1918,24\n418#1:1942,64\n418#1:2006,16\n437#1:2022,9\n437#1:2032\n437#1:2034,2\n437#1:2037\n437#1:2039,24\n437#1:2063,49\n437#1:2112,3\n437#1:2115,49\n437#1:2164\n437#1:2165,49\n437#1:2214\n437#1:2215,49\n437#1:2264,7\n437#1:2271\n147#1:489\n147#1:496\n168#1:835\n168#1:842\n284#1:1087\n284#1:1094\n304#1:1433\n304#1:1440\n418#1:1685\n418#1:1692\n437#1:2031\n437#1:2038\n*E\n"})
public final class CodeCompletionApi extends ApiClient {
   @NotNull
   public static final Companion Companion = new Companion((DefaultConstructorMarker)null);
   @NotNull
   private static final Lazy<String> defaultBasePath$delegate = LazyKt.lazy(CodeCompletionApi::f);

   public CodeCompletionApi(@NotNull String basePath, @NotNull OkHttpClient client) {
      Intrinsics.checkNotNullParameter(basePath, "");
      Intrinsics.checkNotNullParameter(client, "");
      super(basePath, (Call.Factory)client);
   }

   /** @deprecated */
   @Deprecated(
      message = "This operation is deprecated."
   )
   @NotNull
   public final CodeCompletionResponse a(@NotNull CodeCompletionRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      ApiResponse var2 = this.a(var1);
      Iterable var5 = (Iterable)KClasses.getDeclaredMemberProperties(Reflection.getOrCreateKotlinClass(var1.getClass()));
      boolean var6 = false;
      Iterator var7 = var5.iterator();

      Object var10000;
      while(true) {
         if (!var7.hasNext()) {
            var10000 = null;
            break;
         }

         Object var8 = var7.next();
         KProperty1 var9 = (KProperty1)var8;
         boolean var10 = false;
         if (Intrinsics.areEqual(var9.getName(), "requestId") || Intrinsics.areEqual(var9.getName(), "operationId")) {
            var10000 = var8;
            break;
         }
      }

      label80: {
         KProperty1 var22 = (KProperty1)var10000;
         if (var22 != null) {
            KProperty1.Getter var23 = var22.getGetter();
            if (var23 != null) {
               Object[] var14 = new Object[]{var1};
               var10000 = var23.call(var14);
               break label80;
            }
         }

         var10000 = null;
      }

      Object var4 = var10000;
      String var3 = var4 instanceof String ? (String)var4 : null;
      switch (CodeCompletionApi.WhenMappings.$EnumSwitchMapping$0[var2.a().ordinal()]) {
         case 1:
            Intrinsics.checkNotNull(var2);
            var10000 = ((Success)var2).a();
            Intrinsics.checkNotNull(var10000);
            return (CodeCompletionResponse)var10000;
         case 2:
            throw new UnsupportedOperationException("Client does not support Informational responses.");
         case 3:
            throw new UnsupportedOperationException("Client does not support Redirection responses.");
         case 4:
            Intrinsics.checkNotNull(var2);
            ClientError var13 = (ClientError)var2;
            if (var13.a() != 429) {
               StringBuilder var18 = new StringBuilder();
               boolean var21 = false;
               int var10001 = var13.a();
               String var34 = var13.a();
               if (var34 == null) {
                  var34 = "";
               }

               var18.append("Client error : " + var10001 + " " + var34);
               if (var3 != null) {
                  boolean var11 = false;
                  var18.append(" Request ID: " + var3);
               }

               String var32 = var18.toString();
               Intrinsics.checkNotNullExpressionValue(var32, "");
               String var16 = var32;
               throw new ClientException(var16, var13.a(), var2);
            } else {
               label72: {
                  List var26 = (List)var13.a().get("retry-after");
                  if (var26 != null) {
                     String var27 = (String)CollectionsKt.firstOrNull(var26);
                     if (var27 != null) {
                        var28 = StringsKt.toLongOrNull(var27);
                        break label72;
                     }
                  }

                  var28 = null;
               }

               Long var15 = var28;
               List var29 = (List)var13.a().get("quota-key");
               String var17 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-request-delayed");
               String var19 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-limit-reason");
               String var20 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               RateLimitExceptionKt.a(this.b(), var15, var17);
               if (Intrinsics.areEqual(var19, "1") && var15 != null) {
                  throw new RequestDelayedException(var15);
               }

               throw new RateLimitException(var15, var20, (String)null, (String)null, (Throwable)null, 28, (DefaultConstructorMarker)null);
            }
         case 5:
            Intrinsics.checkNotNull(var2);
            ServerError var12 = (ServerError)var2;
            ServerException var25 = new ServerException;
            int var10002 = var12.a();
            String var10003 = var12.a();
            if (var10003 == null) {
               var10003 = "";
            }

            var25.<init>("Server error : " + var10002 + " " + var10003 + " " + var12.a(), var12.a(), var2);
            throw var25;
         default:
            throw new NoWhenBranchMatchedException();
      }
   }

   /** @deprecated */
   @Deprecated(
      message = "This operation is deprecated."
   )
   @NotNull
   public final Future<CodeCompletionResponse> a(@NotNull CodeCompletionRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      Future var2 = this.b(var1);
      return var2;
   }

   /** @deprecated */
   @Deprecated(
      message = "This operation is deprecated."
   )
   @NotNull
   public final ApiResponse<CodeCompletionResponse> a(@NotNull CodeCompletionRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   /** @deprecated */
   @Deprecated(
      message = "This operation is deprecated."
   )
   @NotNull
   public final Future<CodeCompletionResponse> b(@NotNull CodeCompletionRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   /** @deprecated */
   @Deprecated(
      message = "This operation is deprecated."
   )
   @NotNull
   public final RequestConfig<CodeCompletionRequest> a(@NotNull CodeCompletionRequest var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      Map var3 = (Map)(new LinkedHashMap());
      Map var4 = (Map)(new LinkedHashMap());
      var4.put("Content-Type", "application/json");
      var4.put("Accept", "application/json");
      RequestMethod var5 = RequestMethod.POST;
      return new RequestConfig<CodeCompletionRequest>(var5, "/fireworks", var4, (Map)null, var3, false, var1, 8, (DefaultConstructorMarker)null);
   }

   @NotNull
   public final CodeCompletionResponse a(@NotNull String var1, @NotNull CodeCompletionRequest var2) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      Intrinsics.checkNotNullParameter(var2, "");
      ApiResponse var3 = this.a(var1, var2);
      Iterable var6 = (Iterable)KClasses.getDeclaredMemberProperties(Reflection.getOrCreateKotlinClass(var2.getClass()));
      boolean var7 = false;
      Iterator var8 = var6.iterator();

      Object var10000;
      while(true) {
         if (!var8.hasNext()) {
            var10000 = null;
            break;
         }

         Object var9 = var8.next();
         KProperty1 var10 = (KProperty1)var9;
         boolean var11 = false;
         if (Intrinsics.areEqual(var10.getName(), "requestId") || Intrinsics.areEqual(var10.getName(), "operationId")) {
            var10000 = var9;
            break;
         }
      }

      label80: {
         KProperty1 var23 = (KProperty1)var10000;
         if (var23 != null) {
            KProperty1.Getter var24 = var23.getGetter();
            if (var24 != null) {
               Object[] var15 = new Object[]{var2};
               var10000 = var24.call(var15);
               break label80;
            }
         }

         var10000 = null;
      }

      Object var5 = var10000;
      String var4 = var5 instanceof String ? (String)var5 : null;
      switch (CodeCompletionApi.WhenMappings.$EnumSwitchMapping$0[var3.a().ordinal()]) {
         case 1:
            Intrinsics.checkNotNull(var3);
            var10000 = ((Success)var3).a();
            Intrinsics.checkNotNull(var10000);
            return (CodeCompletionResponse)var10000;
         case 2:
            throw new UnsupportedOperationException("Client does not support Informational responses.");
         case 3:
            throw new UnsupportedOperationException("Client does not support Redirection responses.");
         case 4:
            Intrinsics.checkNotNull(var3);
            ClientError var14 = (ClientError)var3;
            if (var14.a() != 429) {
               StringBuilder var19 = new StringBuilder();
               boolean var22 = false;
               int var10001 = var14.a();
               String var35 = var14.a();
               if (var35 == null) {
                  var35 = "";
               }

               var19.append("Client error : " + var10001 + " " + var35);
               if (var4 != null) {
                  boolean var12 = false;
                  var19.append(" Request ID: " + var4);
               }

               String var33 = var19.toString();
               Intrinsics.checkNotNullExpressionValue(var33, "");
               String var17 = var33;
               throw new ClientException(var17, var14.a(), var3);
            } else {
               label72: {
                  List var27 = (List)var14.a().get("retry-after");
                  if (var27 != null) {
                     String var28 = (String)CollectionsKt.firstOrNull(var27);
                     if (var28 != null) {
                        var29 = StringsKt.toLongOrNull(var28);
                        break label72;
                     }
                  }

                  var29 = null;
               }

               Long var16 = var29;
               List var30 = (List)var14.a().get("quota-key");
               String var18 = var30 != null ? (String)CollectionsKt.firstOrNull(var30) : null;
               var30 = (List)var14.a().get("x-request-delayed");
               String var20 = var30 != null ? (String)CollectionsKt.firstOrNull(var30) : null;
               var30 = (List)var14.a().get("x-limit-reason");
               String var21 = var30 != null ? (String)CollectionsKt.firstOrNull(var30) : null;
               RateLimitExceptionKt.a(this.b(), var16, var18);
               if (Intrinsics.areEqual(var20, "1") && var16 != null) {
                  throw new RequestDelayedException(var16);
               }

               throw new RateLimitException(var16, var21, (String)null, (String)null, (Throwable)null, 28, (DefaultConstructorMarker)null);
            }
         case 5:
            Intrinsics.checkNotNull(var3);
            ServerError var13 = (ServerError)var3;
            ServerException var26 = new ServerException;
            int var10002 = var13.a();
            String var10003 = var13.a();
            if (var10003 == null) {
               var10003 = "";
            }

            var26.<init>("Server error : " + var10002 + " " + var10003 + " " + var13.a(), var13.a(), var3);
            throw var26;
         default:
            throw new NoWhenBranchMatchedException();
      }
   }

   @NotNull
   public final Future<CodeCompletionResponse> a(@NotNull String var1, @NotNull CodeCompletionRequest var2) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      Intrinsics.checkNotNullParameter(var2, "");
      Future var3 = this.b(var1, var2);
      return var3;
   }

   @NotNull
   public final ApiResponse<CodeCompletionResponse> a(@NotNull String var1, @NotNull CodeCompletionRequest var2) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final Future<CodeCompletionResponse> b(@NotNull String var1, @NotNull CodeCompletionRequest var2) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final RequestConfig<CodeCompletionRequest> a(@NotNull String var1, @NotNull CodeCompletionRequest var2) {
      Intrinsics.checkNotNullParameter(var1, "");
      Intrinsics.checkNotNullParameter(var2, "");
      Map var4 = (Map)(new LinkedHashMap());
      Map var5 = (Map)(new LinkedHashMap());
      boolean var8 = false;
      var5.put("sentry-trace", var1.toString());
      var5.put("Content-Type", "application/json");
      var5.put("Accept", "application/json");
      RequestMethod var6 = RequestMethod.POST;
      return new RequestConfig<CodeCompletionRequest>(var6, "/inference", var5, (Map)null, var4, false, var2, 8, (DefaultConstructorMarker)null);
   }

   @NotNull
   public final EditPredictionResponse a(@NotNull EditPredictionRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      ApiResponse var2 = this.a(var1);
      Iterable var5 = (Iterable)KClasses.getDeclaredMemberProperties(Reflection.getOrCreateKotlinClass(var1.getClass()));
      boolean var6 = false;
      Iterator var7 = var5.iterator();

      Object var10000;
      while(true) {
         if (!var7.hasNext()) {
            var10000 = null;
            break;
         }

         Object var8 = var7.next();
         KProperty1 var9 = (KProperty1)var8;
         boolean var10 = false;
         if (Intrinsics.areEqual(var9.getName(), "requestId") || Intrinsics.areEqual(var9.getName(), "operationId")) {
            var10000 = var8;
            break;
         }
      }

      label80: {
         KProperty1 var22 = (KProperty1)var10000;
         if (var22 != null) {
            KProperty1.Getter var23 = var22.getGetter();
            if (var23 != null) {
               Object[] var14 = new Object[]{var1};
               var10000 = var23.call(var14);
               break label80;
            }
         }

         var10000 = null;
      }

      Object var4 = var10000;
      String var3 = var4 instanceof String ? (String)var4 : null;
      switch (CodeCompletionApi.WhenMappings.$EnumSwitchMapping$0[var2.a().ordinal()]) {
         case 1:
            Intrinsics.checkNotNull(var2);
            var10000 = ((Success)var2).a();
            Intrinsics.checkNotNull(var10000);
            return (EditPredictionResponse)var10000;
         case 2:
            throw new UnsupportedOperationException("Client does not support Informational responses.");
         case 3:
            throw new UnsupportedOperationException("Client does not support Redirection responses.");
         case 4:
            Intrinsics.checkNotNull(var2);
            ClientError var13 = (ClientError)var2;
            if (var13.a() != 429) {
               StringBuilder var18 = new StringBuilder();
               boolean var21 = false;
               int var10001 = var13.a();
               String var34 = var13.a();
               if (var34 == null) {
                  var34 = "";
               }

               var18.append("Client error : " + var10001 + " " + var34);
               if (var3 != null) {
                  boolean var11 = false;
                  var18.append(" Request ID: " + var3);
               }

               String var32 = var18.toString();
               Intrinsics.checkNotNullExpressionValue(var32, "");
               String var16 = var32;
               throw new ClientException(var16, var13.a(), var2);
            } else {
               label72: {
                  List var26 = (List)var13.a().get("retry-after");
                  if (var26 != null) {
                     String var27 = (String)CollectionsKt.firstOrNull(var26);
                     if (var27 != null) {
                        var28 = StringsKt.toLongOrNull(var27);
                        break label72;
                     }
                  }

                  var28 = null;
               }

               Long var15 = var28;
               List var29 = (List)var13.a().get("quota-key");
               String var17 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-request-delayed");
               String var19 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-limit-reason");
               String var20 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               RateLimitExceptionKt.a(this.b(), var15, var17);
               if (Intrinsics.areEqual(var19, "1") && var15 != null) {
                  throw new RequestDelayedException(var15);
               }

               throw new RateLimitException(var15, var20, (String)null, (String)null, (Throwable)null, 28, (DefaultConstructorMarker)null);
            }
         case 5:
            Intrinsics.checkNotNull(var2);
            ServerError var12 = (ServerError)var2;
            ServerException var25 = new ServerException;
            int var10002 = var12.a();
            String var10003 = var12.a();
            if (var10003 == null) {
               var10003 = "";
            }

            var25.<init>("Server error : " + var10002 + " " + var10003 + " " + var12.a(), var12.a(), var2);
            throw var25;
         default:
            throw new NoWhenBranchMatchedException();
      }
   }

   @NotNull
   public final Future<EditPredictionResponse> a(@NotNull EditPredictionRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      Future var2 = this.b(var1);
      return var2;
   }

   @NotNull
   public final ApiResponse<EditPredictionResponse> a(@NotNull EditPredictionRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final Future<EditPredictionResponse> b(@NotNull EditPredictionRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final RequestConfig<EditPredictionRequest> a(@NotNull EditPredictionRequest var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      Map var3 = (Map)(new LinkedHashMap());
      Map var4 = (Map)(new LinkedHashMap());
      var4.put("Content-Type", "application/json");
      var4.put("Accept", "application/json");
      RequestMethod var5 = RequestMethod.POST;
      return new RequestConfig<EditPredictionRequest>(var5, "/predict-next-edit", var4, (Map)null, var3, false, var1, 8, (DefaultConstructorMarker)null);
   }

   private final String a(String var1) {
      return (String)(new HttpUrl.Builder()).scheme("http").host("localhost").addPathSegment(var1).build().encodedPathSegments().get(0);
   }

   @NotNull
   public final CodeCompletionApi a(@Nullable String var1) {
      ApiClient.Companion var10000 = ApiClient.Companion;
      String var10001 = var1;
      if (var1 == null) {
         var10001 = "";
      }

      var10000.c(var10001);
      return this;
   }

   private static final String f() {
      return System.getProperties().getProperty("ai.zencoder.generated.client.completion.baseUrl", "http://localhost/code-completion-service");
   }

   public CodeCompletionApi() {
      this((String)null, (OkHttpClient)null, 3, (DefaultConstructorMarker)null);
   }

   @NotNull
   public static final String a() {
      return Companion.a();
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R!\u0010\u0004\u001a\u00020\u00058FX\u0087\u0084\u0002\u00a2\u0006\u0012\n\u0004\b\t\u0010\n\u0012\u0004\b\u0006\u0010\u0003\u001a\u0004\b\u0007\u0010\b\u00a8\u0006\u000b"},
      d2 = {"Lai/zencoder/generated/client/completion/apis/CodeCompletionApi$Companion;", "", "<init>", "()V", "defaultBasePath", "", "getDefaultBasePath$annotations", "getDefaultBasePath", "()Ljava/lang/String;", "defaultBasePath$delegate", "Lkotlin/Lazy;", "zencoder"}
   )
   public static final class Companion {
      private Companion() {
      }

      @NotNull
      public final String a() {
         Lazy var1 = CodeCompletionApi.defaultBasePath$delegate;
         Object var10000 = var1.getValue();
         Intrinsics.checkNotNullExpressionValue(var10000, "");
         return (String)var10000;
      }
   }
}
