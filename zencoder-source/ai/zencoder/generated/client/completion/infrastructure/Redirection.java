package ai.zencoder.generated.client.completion.infrastructure;

import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010 \n\u0002\b\u0007\u0018\u0000*\u0004\b\u0000\u0010\u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B-\u0012\b\b\u0002\u0010\u0003\u001a\u00020\u0004\u0012\u001a\b\u0002\u0010\u0005\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\b0\u0006\u00a2\u0006\u0004\b\t\u0010\nR\u0014\u0010\u0003\u001a\u00020\u0004X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR&\u0010\u0005\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\b0\u0006X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u000f"},
   d2 = {"Lai/zencoder/generated/client/completion/infrastructure/Redirection;", "T", "Lai/zencoder/generated/client/completion/infrastructure/ApiResponse;", "statusCode", "", "headers", "", "", "", "<init>", "(ILjava/util/Map;)V", "getStatusCode", "()I", "getHeaders", "()Ljava/util/Map;", "zencoder"}
)
public final class Redirection<T> extends ApiResponse<T> {
   private final int statusCode;
   @NotNull
   private final Map<String, List<String>> headers;

   public Redirection(int statusCode, @NotNull Map<String, ? extends List<String>> headers) {
      Intrinsics.checkNotNullParameter(headers, "");
      super(ResponseType.Redirection);
      this.statusCode = statusCode;
      this.headers = headers;
   }

   public int a() {
      return this.statusCode;
   }

   @NotNull
   public Map<String, List<String>> a() {
      return this.headers;
   }

   public Redirection() {
      this(0, (Map)null, 3, (DefaultConstructorMarker)null);
   }
}
