package ai.zencoder.generated.client.completion.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u001a\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bi\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n\u0012\u0010\b\u0002\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\n\u0012\u0010\b\u0002\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\n\u00a2\u0006\u0004\b\u000e\u0010\u000fJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u00c6\u0003J\u0011\u0010\"\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\nH\u00c6\u0003J\u0011\u0010#\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\nH\u00c6\u0003Jy\u0010$\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0010\b\u0002\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\n2\u0010\b\u0002\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\nH\u00c6\u0001J\u0013\u0010%\u001a\u00020&2\b\u0010'\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010(\u001a\u00020)H\u00d6\u0001J\t\u0010*\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0013\u0010\u0005\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0013\u0010\u0006\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u0013\u0010\u0007\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0011R\u0013\u0010\b\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0011R\u0019\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u001b\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\n8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0018R\u001b\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\n8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0018\u00a8\u0006+"},
   d2 = {"Lai/zencoder/generated/client/completion/models/CodeCompletionContext;", "", "activeFilePath", "", "projectName", "prefix", "suffix", "language", "importsSection", "importsSignatures", "", "Lai/zencoder/generated/client/completion/models/ExternalSignature;", "usedSignatures", "openedTabsSignatures", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getActiveFilePath", "()Ljava/lang/String;", "getProjectName", "getPrefix", "getSuffix", "getLanguage", "getImportsSection", "getImportsSignatures", "()Ljava/util/List;", "getUsedSignatures", "getOpenedTabsSignatures", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CodeCompletionContext {
   @NotNull
   private final String activeFilePath;
   @NotNull
   private final String projectName;
   @NotNull
   private final String prefix;
   @NotNull
   private final String suffix;
   @NotNull
   private final String language;
   @NotNull
   private final String importsSection;
   @NotNull
   private final List<ExternalSignature> importsSignatures;
   @Nullable
   private final List<ExternalSignature> usedSignatures;
   @Nullable
   private final List<ExternalSignature> openedTabsSignatures;

   public CodeCompletionContext(@NotNull String activeFilePath, @NotNull String projectName, @NotNull String prefix, @NotNull String suffix, @NotNull String language, @NotNull String importsSection, @NotNull List<ExternalSignature> importsSignatures, @Nullable List<ExternalSignature> usedSignatures, @Nullable List<ExternalSignature> openedTabsSignatures) {
      Intrinsics.checkNotNullParameter(activeFilePath, "");
      Intrinsics.checkNotNullParameter(projectName, "");
      Intrinsics.checkNotNullParameter(prefix, "");
      Intrinsics.checkNotNullParameter(suffix, "");
      Intrinsics.checkNotNullParameter(language, "");
      Intrinsics.checkNotNullParameter(importsSection, "");
      Intrinsics.checkNotNullParameter(importsSignatures, "");
      super();
      this.activeFilePath = activeFilePath;
      this.projectName = projectName;
      this.prefix = prefix;
      this.suffix = suffix;
      this.language = language;
      this.importsSection = importsSection;
      this.importsSignatures = importsSignatures;
      this.usedSignatures = usedSignatures;
      this.openedTabsSignatures = openedTabsSignatures;
   }

   @JsonProperty("active_file_path")
   @NotNull
   public final String getActiveFilePath() {
      return this.activeFilePath;
   }

   @JsonProperty("project_name")
   @NotNull
   public final String getProjectName() {
      return this.projectName;
   }

   @JsonProperty("prefix")
   @NotNull
   public final String getPrefix() {
      return this.prefix;
   }

   @JsonProperty("suffix")
   @NotNull
   public final String getSuffix() {
      return this.suffix;
   }

   @JsonProperty("language")
   @NotNull
   public final String getLanguage() {
      return this.language;
   }

   @JsonProperty("imports_section")
   @NotNull
   public final String getImportsSection() {
      return this.importsSection;
   }

   @JsonProperty("imports_signatures")
   @NotNull
   public final List<ExternalSignature> getImportsSignatures() {
      return this.importsSignatures;
   }

   @JsonProperty("used_signatures")
   @Nullable
   public final List<ExternalSignature> getUsedSignatures() {
      return this.usedSignatures;
   }

   @JsonProperty("opened_tabs_signatures")
   @Nullable
   public final List<ExternalSignature> getOpenedTabsSignatures() {
      return this.openedTabsSignatures;
   }

   @NotNull
   public final String component1() {
      return this.activeFilePath;
   }

   @NotNull
   public final String component2() {
      return this.projectName;
   }

   @NotNull
   public final String component3() {
      return this.prefix;
   }

   @NotNull
   public final String component4() {
      return this.suffix;
   }

   @NotNull
   public final String component5() {
      return this.language;
   }

   @NotNull
   public final String component6() {
      return this.importsSection;
   }

   @NotNull
   public final List<ExternalSignature> component7() {
      return this.importsSignatures;
   }

   @Nullable
   public final List<ExternalSignature> component8() {
      return this.usedSignatures;
   }

   @Nullable
   public final List<ExternalSignature> component9() {
      return this.openedTabsSignatures;
   }

   @NotNull
   public final CodeCompletionContext copy(@NotNull String activeFilePath, @NotNull String projectName, @NotNull String prefix, @NotNull String suffix, @NotNull String language, @NotNull String importsSection, @NotNull List<ExternalSignature> importsSignatures, @Nullable List<ExternalSignature> usedSignatures, @Nullable List<ExternalSignature> openedTabsSignatures) {
      Intrinsics.checkNotNullParameter(activeFilePath, "");
      Intrinsics.checkNotNullParameter(projectName, "");
      Intrinsics.checkNotNullParameter(prefix, "");
      Intrinsics.checkNotNullParameter(suffix, "");
      Intrinsics.checkNotNullParameter(language, "");
      Intrinsics.checkNotNullParameter(importsSection, "");
      Intrinsics.checkNotNullParameter(importsSignatures, "");
      return new CodeCompletionContext(activeFilePath, projectName, prefix, suffix, language, importsSection, importsSignatures, usedSignatures, openedTabsSignatures);
   }

   @NotNull
   public String toString() {
      return "CodeCompletionContext(activeFilePath=" + this.activeFilePath + ", projectName=" + this.projectName + ", prefix=" + this.prefix + ", suffix=" + this.suffix + ", language=" + this.language + ", importsSection=" + this.importsSection + ", importsSignatures=" + this.importsSignatures + ", usedSignatures=" + this.usedSignatures + ", openedTabsSignatures=" + this.openedTabsSignatures + ")";
   }

   public int hashCode() {
      int var1 = this.activeFilePath.hashCode();
      var1 = var1 * 31 + this.projectName.hashCode();
      var1 = var1 * 31 + this.prefix.hashCode();
      var1 = var1 * 31 + this.suffix.hashCode();
      var1 = var1 * 31 + this.language.hashCode();
      var1 = var1 * 31 + this.importsSection.hashCode();
      var1 = var1 * 31 + this.importsSignatures.hashCode();
      var1 = var1 * 31 + (this.usedSignatures == null ? 0 : this.usedSignatures.hashCode());
      var1 = var1 * 31 + (this.openedTabsSignatures == null ? 0 : this.openedTabsSignatures.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CodeCompletionContext)) {
         return false;
      } else {
         CodeCompletionContext var2 = (CodeCompletionContext)other;
         if (!Intrinsics.areEqual(this.activeFilePath, var2.activeFilePath)) {
            return false;
         } else if (!Intrinsics.areEqual(this.projectName, var2.projectName)) {
            return false;
         } else if (!Intrinsics.areEqual(this.prefix, var2.prefix)) {
            return false;
         } else if (!Intrinsics.areEqual(this.suffix, var2.suffix)) {
            return false;
         } else if (!Intrinsics.areEqual(this.language, var2.language)) {
            return false;
         } else if (!Intrinsics.areEqual(this.importsSection, var2.importsSection)) {
            return false;
         } else if (!Intrinsics.areEqual(this.importsSignatures, var2.importsSignatures)) {
            return false;
         } else if (!Intrinsics.areEqual(this.usedSignatures, var2.usedSignatures)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.openedTabsSignatures, var2.openedTabsSignatures);
         }
      }
   }
}
