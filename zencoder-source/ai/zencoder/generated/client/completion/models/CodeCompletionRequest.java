package ai.zencoder.generated.client.completion.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010$\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0016\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BC\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0004\b\u000b\u0010\fJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\u0015\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u0006H\u00c6\u0003J\u0010\u0010\u001a\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\nH\u00c6\u0003JP\u0010\u001c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u0014\b\u0002\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\nH\u00c6\u0001\u00a2\u0006\u0002\u0010\u001dJ\u0013\u0010\u001e\u001a\u00020\b2\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020!H\u00d6\u0001J\t\u0010\"\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u001f\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0017\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b\u0012\u0010\u0013R\u0015\u0010\t\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006#"},
   d2 = {"Lai/zencoder/generated/client/completion/models/CodeCompletionRequest;", "", "requestId", "", "language", "segments", "", "multiLine", "", "context", "Lai/zencoder/generated/client/completion/models/CodeCompletionContext;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;Ljava/lang/Boolean;Lai/zencoder/generated/client/completion/models/CodeCompletionContext;)V", "getRequestId", "()Ljava/lang/String;", "getLanguage", "getSegments", "()Ljava/util/Map;", "getMultiLine", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getContext", "()Lai/zencoder/generated/client/completion/models/CodeCompletionContext;", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;Ljava/lang/Boolean;Lai/zencoder/generated/client/completion/models/CodeCompletionContext;)Lai/zencoder/generated/client/completion/models/CodeCompletionRequest;", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CodeCompletionRequest {
   @NotNull
   private final String requestId;
   @NotNull
   private final String language;
   @NotNull
   private final Map<String, String> segments;
   @Nullable
   private final Boolean multiLine;
   @Nullable
   private final CodeCompletionContext context;

   public CodeCompletionRequest(@NotNull String requestId, @NotNull String language, @NotNull Map<String, String> segments, @Nullable Boolean multiLine, @Nullable CodeCompletionContext context) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(language, "");
      Intrinsics.checkNotNullParameter(segments, "");
      super();
      this.requestId = requestId;
      this.language = language;
      this.segments = segments;
      this.multiLine = multiLine;
      this.context = context;
   }

   @JsonProperty("requestId")
   @NotNull
   public final String getRequestId() {
      return this.requestId;
   }

   @JsonProperty("language")
   @NotNull
   public final String getLanguage() {
      return this.language;
   }

   @JsonProperty("segments")
   @NotNull
   public final Map<String, String> getSegments() {
      return this.segments;
   }

   @JsonProperty("multiLine")
   @Nullable
   public final Boolean getMultiLine() {
      return this.multiLine;
   }

   @JsonProperty("context")
   @Nullable
   public final CodeCompletionContext getContext() {
      return this.context;
   }

   @NotNull
   public final String component1() {
      return this.requestId;
   }

   @NotNull
   public final String component2() {
      return this.language;
   }

   @NotNull
   public final Map<String, String> component3() {
      return this.segments;
   }

   @Nullable
   public final Boolean component4() {
      return this.multiLine;
   }

   @Nullable
   public final CodeCompletionContext component5() {
      return this.context;
   }

   @NotNull
   public final CodeCompletionRequest copy(@NotNull String requestId, @NotNull String language, @NotNull Map<String, String> segments, @Nullable Boolean multiLine, @Nullable CodeCompletionContext context) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(language, "");
      Intrinsics.checkNotNullParameter(segments, "");
      return new CodeCompletionRequest(requestId, language, segments, multiLine, context);
   }

   @NotNull
   public String toString() {
      return "CodeCompletionRequest(requestId=" + this.requestId + ", language=" + this.language + ", segments=" + this.segments + ", multiLine=" + this.multiLine + ", context=" + this.context + ")";
   }

   public int hashCode() {
      int var1 = this.requestId.hashCode();
      var1 = var1 * 31 + this.language.hashCode();
      var1 = var1 * 31 + this.segments.hashCode();
      var1 = var1 * 31 + (this.multiLine == null ? 0 : this.multiLine.hashCode());
      var1 = var1 * 31 + (this.context == null ? 0 : this.context.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CodeCompletionRequest)) {
         return false;
      } else {
         CodeCompletionRequest var2 = (CodeCompletionRequest)other;
         if (!Intrinsics.areEqual(this.requestId, var2.requestId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.language, var2.language)) {
            return false;
         } else if (!Intrinsics.areEqual(this.segments, var2.segments)) {
            return false;
         } else if (!Intrinsics.areEqual(this.multiLine, var2.multiLine)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.context, var2.context);
         }
      }
   }
}
