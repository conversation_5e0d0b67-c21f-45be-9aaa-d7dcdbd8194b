package ai.zencoder.generated.client.completion.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BA\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\u0006\u0010\t\u001a\u00020\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\b\f\u0010\rJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003JM\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000bH\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\u001f2\b\u0010 \u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010!\u001a\u00020\"H\u00d6\u0001J\t\u0010#\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\u0007\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000fR\u0013\u0010\b\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000fR\u0013\u0010\t\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u000fR\u0015\u0010\n\u001a\u0004\u0018\u00010\u000b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006$"},
   d2 = {"Lai/zencoder/generated/client/completion/models/EditPredictionRequest;", "", "requestId", "", "fileStates", "", "Lai/zencoder/generated/client/completion/models/FileState;", "fileContent", "language", "filePath", "cursorPosition", "Lai/zencoder/generated/client/completion/models/CursorPosition;", "<init>", "(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/completion/models/CursorPosition;)V", "getRequestId", "()Ljava/lang/String;", "getFileStates", "()Ljava/util/List;", "getFileContent", "getLanguage", "getFilePath", "getCursorPosition", "()Lai/zencoder/generated/client/completion/models/CursorPosition;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class EditPredictionRequest {
   @NotNull
   private final String requestId;
   @NotNull
   private final List<FileState> fileStates;
   @NotNull
   private final String fileContent;
   @NotNull
   private final String language;
   @NotNull
   private final String filePath;
   @Nullable
   private final CursorPosition cursorPosition;

   public EditPredictionRequest(@NotNull String requestId, @NotNull List<FileState> fileStates, @NotNull String fileContent, @NotNull String language, @NotNull String filePath, @Nullable CursorPosition cursorPosition) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(fileStates, "");
      Intrinsics.checkNotNullParameter(fileContent, "");
      Intrinsics.checkNotNullParameter(language, "");
      Intrinsics.checkNotNullParameter(filePath, "");
      super();
      this.requestId = requestId;
      this.fileStates = fileStates;
      this.fileContent = fileContent;
      this.language = language;
      this.filePath = filePath;
      this.cursorPosition = cursorPosition;
   }

   @JsonProperty("requestId")
   @NotNull
   public final String getRequestId() {
      return this.requestId;
   }

   @JsonProperty("fileStates")
   @NotNull
   public final List<FileState> getFileStates() {
      return this.fileStates;
   }

   @JsonProperty("fileContent")
   @NotNull
   public final String getFileContent() {
      return this.fileContent;
   }

   @JsonProperty("language")
   @NotNull
   public final String getLanguage() {
      return this.language;
   }

   @JsonProperty("filePath")
   @NotNull
   public final String getFilePath() {
      return this.filePath;
   }

   @JsonProperty("cursorPosition")
   @Nullable
   public final CursorPosition getCursorPosition() {
      return this.cursorPosition;
   }

   @NotNull
   public final String component1() {
      return this.requestId;
   }

   @NotNull
   public final List<FileState> component2() {
      return this.fileStates;
   }

   @NotNull
   public final String component3() {
      return this.fileContent;
   }

   @NotNull
   public final String component4() {
      return this.language;
   }

   @NotNull
   public final String component5() {
      return this.filePath;
   }

   @Nullable
   public final CursorPosition component6() {
      return this.cursorPosition;
   }

   @NotNull
   public final EditPredictionRequest copy(@NotNull String requestId, @NotNull List<FileState> fileStates, @NotNull String fileContent, @NotNull String language, @NotNull String filePath, @Nullable CursorPosition cursorPosition) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(fileStates, "");
      Intrinsics.checkNotNullParameter(fileContent, "");
      Intrinsics.checkNotNullParameter(language, "");
      Intrinsics.checkNotNullParameter(filePath, "");
      return new EditPredictionRequest(requestId, fileStates, fileContent, language, filePath, cursorPosition);
   }

   @NotNull
   public String toString() {
      return "EditPredictionRequest(requestId=" + this.requestId + ", fileStates=" + this.fileStates + ", fileContent=" + this.fileContent + ", language=" + this.language + ", filePath=" + this.filePath + ", cursorPosition=" + this.cursorPosition + ")";
   }

   public int hashCode() {
      int var1 = this.requestId.hashCode();
      var1 = var1 * 31 + this.fileStates.hashCode();
      var1 = var1 * 31 + this.fileContent.hashCode();
      var1 = var1 * 31 + this.language.hashCode();
      var1 = var1 * 31 + this.filePath.hashCode();
      var1 = var1 * 31 + (this.cursorPosition == null ? 0 : this.cursorPosition.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof EditPredictionRequest)) {
         return false;
      } else {
         EditPredictionRequest var2 = (EditPredictionRequest)other;
         if (!Intrinsics.areEqual(this.requestId, var2.requestId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.fileStates, var2.fileStates)) {
            return false;
         } else if (!Intrinsics.areEqual(this.fileContent, var2.fileContent)) {
            return false;
         } else if (!Intrinsics.areEqual(this.language, var2.language)) {
            return false;
         } else if (!Intrinsics.areEqual(this.filePath, var2.filePath)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.cursorPosition, var2.cursorPosition);
         }
      }
   }
}
