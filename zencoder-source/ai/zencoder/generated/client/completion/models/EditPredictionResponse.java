package ai.zencoder.generated.client.completion.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0006H\u00c6\u0003J'\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0013\u0010\u0005\u001a\u00020\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/completion/models/EditPredictionResponse;", "", "originalStr", "", "modifiedStr", "cursorPosition", "Lai/zencoder/generated/client/completion/models/CursorPosition;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/completion/models/CursorPosition;)V", "getOriginalStr", "()Ljava/lang/String;", "getModifiedStr", "getCursorPosition", "()Lai/zencoder/generated/client/completion/models/CursorPosition;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class EditPredictionResponse {
   @NotNull
   private final String originalStr;
   @NotNull
   private final String modifiedStr;
   @NotNull
   private final CursorPosition cursorPosition;

   public EditPredictionResponse(@NotNull String originalStr, @NotNull String modifiedStr, @NotNull CursorPosition cursorPosition) {
      Intrinsics.checkNotNullParameter(originalStr, "");
      Intrinsics.checkNotNullParameter(modifiedStr, "");
      Intrinsics.checkNotNullParameter(cursorPosition, "");
      super();
      this.originalStr = originalStr;
      this.modifiedStr = modifiedStr;
      this.cursorPosition = cursorPosition;
   }

   @JsonProperty("original_str")
   @NotNull
   public final String getOriginalStr() {
      return this.originalStr;
   }

   @JsonProperty("modified_str")
   @NotNull
   public final String getModifiedStr() {
      return this.modifiedStr;
   }

   @JsonProperty("cursor_position")
   @NotNull
   public final CursorPosition getCursorPosition() {
      return this.cursorPosition;
   }

   @NotNull
   public final String component1() {
      return this.originalStr;
   }

   @NotNull
   public final String component2() {
      return this.modifiedStr;
   }

   @NotNull
   public final CursorPosition component3() {
      return this.cursorPosition;
   }

   @NotNull
   public final EditPredictionResponse copy(@NotNull String originalStr, @NotNull String modifiedStr, @NotNull CursorPosition cursorPosition) {
      Intrinsics.checkNotNullParameter(originalStr, "");
      Intrinsics.checkNotNullParameter(modifiedStr, "");
      Intrinsics.checkNotNullParameter(cursorPosition, "");
      return new EditPredictionResponse(originalStr, modifiedStr, cursorPosition);
   }

   @NotNull
   public String toString() {
      return "EditPredictionResponse(originalStr=" + this.originalStr + ", modifiedStr=" + this.modifiedStr + ", cursorPosition=" + this.cursorPosition + ")";
   }

   public int hashCode() {
      int var1 = this.originalStr.hashCode();
      var1 = var1 * 31 + this.modifiedStr.hashCode();
      var1 = var1 * 31 + this.cursorPosition.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof EditPredictionResponse)) {
         return false;
      } else {
         EditPredictionResponse var2 = (EditPredictionResponse)other;
         if (!Intrinsics.areEqual(this.originalStr, var2.originalStr)) {
            return false;
         } else if (!Intrinsics.areEqual(this.modifiedStr, var2.modifiedStr)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.cursorPosition, var2.cursorPosition);
         }
      }
   }
}
