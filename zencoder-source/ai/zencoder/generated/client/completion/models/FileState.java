package ai.zencoder.generated.client.completion.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"},
   d2 = {"Lai/zencoder/generated/client/completion/models/FileState;", "", "timestamp", "", "content", "", "<init>", "(JLjava/lang/String;)V", "getTimestamp", "()J", "getContent", "()Ljava/lang/String;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class FileState {
   private final long timestamp;
   @NotNull
   private final String content;

   public FileState(long timestamp, @NotNull String content) {
      Intrinsics.checkNotNullParameter(content, "");
      super();
      this.timestamp = timestamp;
      this.content = content;
   }

   @JsonProperty("timestamp")
   public final long getTimestamp() {
      return this.timestamp;
   }

   @JsonProperty("content")
   @NotNull
   public final String getContent() {
      return this.content;
   }

   public final long component1() {
      return this.timestamp;
   }

   @NotNull
   public final String component2() {
      return this.content;
   }

   @NotNull
   public final FileState copy(long timestamp, @NotNull String content) {
      Intrinsics.checkNotNullParameter(content, "");
      return new FileState(timestamp, content);
   }

   @NotNull
   public String toString() {
      return "FileState(timestamp=" + this.timestamp + ", content=" + this.content + ")";
   }

   public int hashCode() {
      int var1 = Long.hashCode(this.timestamp);
      var1 = var1 * 31 + this.content.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof FileState)) {
         return false;
      } else {
         FileState var2 = (FileState)other;
         if (this.timestamp != var2.timestamp) {
            return false;
         } else {
            return Intrinsics.areEqual(this.content, var2.content);
         }
      }
   }
}
