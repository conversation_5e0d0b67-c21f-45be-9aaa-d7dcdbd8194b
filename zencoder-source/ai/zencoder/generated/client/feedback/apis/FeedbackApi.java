package ai.zencoder.generated.client.feedback.apis;

import ai.zencoder.generated.client.feedback.infrastructure.ApiClient;
import ai.zencoder.generated.client.feedback.infrastructure.ApiResponse;
import ai.zencoder.generated.client.feedback.infrastructure.ClientError;
import ai.zencoder.generated.client.feedback.infrastructure.ClientException;
import ai.zencoder.generated.client.feedback.infrastructure.RequestConfig;
import ai.zencoder.generated.client.feedback.infrastructure.RequestMethod;
import ai.zencoder.generated.client.feedback.infrastructure.ServerError;
import ai.zencoder.generated.client.feedback.infrastructure.ServerException;
import ai.zencoder.generated.client.feedback.infrastructure.Success;
import ai.zencoder.generated.client.feedback.models.Response;
import ai.zencoder.generated.client.feedback.models.UserFeedbackRequest;
import ai.zencoder.plugin.api.exception.RateLimitException;
import ai.zencoder.plugin.api.exception.RateLimitExceptionKt;
import ai.zencoder.plugin.api.exception.RequestDelayedException;
import java.io.IOException;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.Future;
import kotlin.Lazy;
import kotlin.LazyKt;
import kotlin.Metadata;
import kotlin.NoWhenBranchMatchedException;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.Reflection;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.reflect.KProperty1;
import kotlin.reflect.full.KClasses;
import kotlin.text.StringsKt;
import okhttp3.Call;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\u0018\u0000 \u001c2\u00020\u0001:\u0001\u001cB\u001b\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000e\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u000f2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00122\u0006\u0010\n\u001a\u00020\u000bJ\u000e\u0010\u0013\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\u0015\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u000f2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00122\u0006\u0010\n\u001a\u00020\u000bJ\u0010\u0010\u0018\u001a\u00020\u00032\u0006\u0010\u0019\u001a\u00020\u0003H\u0002J\u0010\u0010\u001a\u001a\u00020\u00002\b\u0010\u001b\u001a\u0004\u0018\u00010\u0003\u00a8\u0006\u001d"},
   d2 = {"Lai/zencoder/generated/client/feedback/apis/FeedbackApi;", "Lai/zencoder/generated/client/feedback/infrastructure/ApiClient;", "basePath", "", "client", "Lokhttp3/OkHttpClient;", "<init>", "(Ljava/lang/String;Lokhttp3/OkHttpClient;)V", "userFeedbackDeliberateUserFeedbackDeliberatePost", "Lai/zencoder/generated/client/feedback/models/Response;", "userFeedbackRequest", "Lai/zencoder/generated/client/feedback/models/UserFeedbackRequest;", "userFeedbackDeliberateUserFeedbackDeliberatePostAsync", "Ljava/util/concurrent/Future;", "userFeedbackDeliberateUserFeedbackDeliberatePostWithHttpInfo", "Lai/zencoder/generated/client/feedback/infrastructure/ApiResponse;", "userFeedbackDeliberateUserFeedbackDeliberatePostWithHttpInfoAsync", "userFeedbackDeliberateUserFeedbackDeliberatePostRequestConfig", "Lai/zencoder/generated/client/feedback/infrastructure/RequestConfig;", "userFeedbackUserFeedbackPost", "userFeedbackUserFeedbackPostAsync", "userFeedbackUserFeedbackPostWithHttpInfo", "userFeedbackUserFeedbackPostWithHttpInfoAsync", "userFeedbackUserFeedbackPostRequestConfig", "encodeURIComponent", "uriComponent", "withAuth", "token", "Companion", "zencoder"}
)
@SourceDebugExtension({"SMAP\nFeedbackApi.kt\nKotlin\n*S Kotlin\n*F\n+ 1 FeedbackApi.kt\nai/zencoder/generated/client/feedback/apis/FeedbackApi\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n+ 3 fake.kt\nkotlin/jvm/internal/FakeKt\n+ 4 ApiClient.kt\nai/zencoder/generated/client/feedback/infrastructure/ApiClient\n+ 5 _Maps.kt\nkotlin/collections/MapsKt___MapsKt\n*L\n1#1,328:1\n288#2,2:329\n1855#2:343\n1856#2:346\n1855#2:689\n1856#2:692\n288#2,2:928\n1855#2:941\n1856#2:944\n1855#2:1287\n1856#2:1290\n1#3:331\n227#4,9:332\n236#4:342\n237#4,2:344\n239#4:347\n240#4,24:349\n100#4,49:373\n264#4,3:422\n100#4,49:425\n267#4:474\n100#4,49:475\n268#4:524\n100#4,49:525\n269#4,24:574\n151#4,64:598\n293#4,16:662\n322#4,9:678\n331#4:688\n332#4,2:690\n334#4:693\n335#4,24:695\n100#4,49:719\n359#4,3:768\n100#4,49:771\n362#4:820\n100#4,49:821\n363#4:870\n100#4,49:871\n364#4,7:920\n393#4:927\n227#4,9:930\n236#4:940\n237#4,2:942\n239#4:945\n240#4,24:947\n100#4,49:971\n264#4,3:1020\n100#4,49:1023\n267#4:1072\n100#4,49:1073\n268#4:1122\n100#4,49:1123\n269#4,24:1172\n151#4,64:1196\n293#4,16:1260\n322#4,9:1276\n331#4:1286\n332#4,2:1288\n334#4:1291\n335#4,24:1293\n100#4,49:1317\n359#4,3:1366\n100#4,49:1369\n362#4:1418\n100#4,49:1419\n363#4:1468\n100#4,49:1469\n364#4,7:1518\n393#4:1525\n215#5:341\n216#5:348\n215#5:687\n216#5:694\n215#5:939\n216#5:946\n215#5:1285\n216#5:1292\n*S KotlinDebug\n*F\n+ 1 FeedbackApi.kt\nai/zencoder/generated/client/feedback/apis/FeedbackApi\n*L\n72#1:329,2\n139#1:343\n139#1:346\n158#1:689\n158#1:692\n203#1:928,2\n270#1:941\n270#1:944\n289#1:1287\n289#1:1290\n139#1:332,9\n139#1:342\n139#1:344,2\n139#1:347\n139#1:349,24\n139#1:373,49\n139#1:422,3\n139#1:425,49\n139#1:474\n139#1:475,49\n139#1:524\n139#1:525,49\n139#1:574,24\n139#1:598,64\n139#1:662,16\n158#1:678,9\n158#1:688\n158#1:690,2\n158#1:693\n158#1:695,24\n158#1:719,49\n158#1:768,3\n158#1:771,49\n158#1:820\n158#1:821,49\n158#1:870\n158#1:871,49\n158#1:920,7\n158#1:927\n270#1:930,9\n270#1:940\n270#1:942,2\n270#1:945\n270#1:947,24\n270#1:971,49\n270#1:1020,3\n270#1:1023,49\n270#1:1072\n270#1:1073,49\n270#1:1122\n270#1:1123,49\n270#1:1172,24\n270#1:1196,64\n270#1:1260,16\n289#1:1276,9\n289#1:1286\n289#1:1288,2\n289#1:1291\n289#1:1293,24\n289#1:1317,49\n289#1:1366,3\n289#1:1369,49\n289#1:1418\n289#1:1419,49\n289#1:1468\n289#1:1469,49\n289#1:1518,7\n289#1:1525\n139#1:341\n139#1:348\n158#1:687\n158#1:694\n270#1:939\n270#1:946\n289#1:1285\n289#1:1292\n*E\n"})
public final class FeedbackApi extends ApiClient {
   @NotNull
   public static final Companion Companion = new Companion((DefaultConstructorMarker)null);
   @NotNull
   private static final Lazy<String> defaultBasePath$delegate = LazyKt.lazy(FeedbackApi::f);

   public FeedbackApi(@NotNull String basePath, @NotNull OkHttpClient client) {
      Intrinsics.checkNotNullParameter(basePath, "");
      Intrinsics.checkNotNullParameter(client, "");
      super(basePath, (Call.Factory)client);
   }

   @NotNull
   public final Response a(@NotNull UserFeedbackRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      ApiResponse var2 = this.a(var1);
      Iterable var5 = (Iterable)KClasses.getDeclaredMemberProperties(Reflection.getOrCreateKotlinClass(var1.getClass()));
      boolean var6 = false;
      Iterator var7 = var5.iterator();

      Object var10000;
      while(true) {
         if (!var7.hasNext()) {
            var10000 = null;
            break;
         }

         Object var8 = var7.next();
         KProperty1 var9 = (KProperty1)var8;
         boolean var10 = false;
         if (Intrinsics.areEqual(var9.getName(), "requestId") || Intrinsics.areEqual(var9.getName(), "operationId")) {
            var10000 = var8;
            break;
         }
      }

      label80: {
         KProperty1 var22 = (KProperty1)var10000;
         if (var22 != null) {
            KProperty1.Getter var23 = var22.getGetter();
            if (var23 != null) {
               Object[] var14 = new Object[]{var1};
               var10000 = var23.call(var14);
               break label80;
            }
         }

         var10000 = null;
      }

      Object var4 = var10000;
      String var3 = var4 instanceof String ? (String)var4 : null;
      switch (FeedbackApi.WhenMappings.$EnumSwitchMapping$0[var2.a().ordinal()]) {
         case 1:
            Intrinsics.checkNotNull(var2);
            var10000 = ((Success)var2).a();
            Intrinsics.checkNotNull(var10000);
            return (Response)var10000;
         case 2:
            throw new UnsupportedOperationException("Client does not support Informational responses.");
         case 3:
            throw new UnsupportedOperationException("Client does not support Redirection responses.");
         case 4:
            Intrinsics.checkNotNull(var2);
            ClientError var13 = (ClientError)var2;
            if (var13.a() != 429) {
               StringBuilder var18 = new StringBuilder();
               boolean var21 = false;
               int var10001 = var13.a();
               String var34 = var13.a();
               if (var34 == null) {
                  var34 = "";
               }

               var18.append("Client error : " + var10001 + " " + var34);
               if (var3 != null) {
                  boolean var11 = false;
                  var18.append(" Request ID: " + var3);
               }

               String var32 = var18.toString();
               Intrinsics.checkNotNullExpressionValue(var32, "");
               String var16 = var32;
               throw new ClientException(var16, var13.a(), var2);
            } else {
               label72: {
                  List var26 = (List)var13.a().get("retry-after");
                  if (var26 != null) {
                     String var27 = (String)CollectionsKt.firstOrNull(var26);
                     if (var27 != null) {
                        var28 = StringsKt.toLongOrNull(var27);
                        break label72;
                     }
                  }

                  var28 = null;
               }

               Long var15 = var28;
               List var29 = (List)var13.a().get("quota-key");
               String var17 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-request-delayed");
               String var19 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-limit-reason");
               String var20 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               RateLimitExceptionKt.a(this.b(), var15, var17);
               if (Intrinsics.areEqual(var19, "1") && var15 != null) {
                  throw new RequestDelayedException(var15);
               }

               throw new RateLimitException(var15, var20, (String)null, (String)null, (Throwable)null, 28, (DefaultConstructorMarker)null);
            }
         case 5:
            Intrinsics.checkNotNull(var2);
            ServerError var12 = (ServerError)var2;
            ServerException var25 = new ServerException;
            int var10002 = var12.a();
            String var10003 = var12.a();
            if (var10003 == null) {
               var10003 = "";
            }

            var25.<init>("Server error : " + var10002 + " " + var10003 + " " + var12.a(), var12.a(), var2);
            throw var25;
         default:
            throw new NoWhenBranchMatchedException();
      }
   }

   @NotNull
   public final Future<Response> a(@NotNull UserFeedbackRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      Future var2 = this.b(var1);
      return var2;
   }

   @NotNull
   public final ApiResponse<Response> a(@NotNull UserFeedbackRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final Future<Response> b(@NotNull UserFeedbackRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final RequestConfig<UserFeedbackRequest> a(@NotNull UserFeedbackRequest var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      Map var3 = (Map)(new LinkedHashMap());
      Map var4 = (Map)(new LinkedHashMap());
      var4.put("Content-Type", "application/json");
      var4.put("Accept", "application/json");
      RequestMethod var5 = RequestMethod.POST;
      return new RequestConfig<UserFeedbackRequest>(var5, "/user_feedback_deliberate", var4, (Map)null, var3, false, var1, 8, (DefaultConstructorMarker)null);
   }

   @NotNull
   public final Response b(@NotNull UserFeedbackRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      ApiResponse var2 = this.b(var1);
      Iterable var5 = (Iterable)KClasses.getDeclaredMemberProperties(Reflection.getOrCreateKotlinClass(var1.getClass()));
      boolean var6 = false;
      Iterator var7 = var5.iterator();

      Object var10000;
      while(true) {
         if (!var7.hasNext()) {
            var10000 = null;
            break;
         }

         Object var8 = var7.next();
         KProperty1 var9 = (KProperty1)var8;
         boolean var10 = false;
         if (Intrinsics.areEqual(var9.getName(), "requestId") || Intrinsics.areEqual(var9.getName(), "operationId")) {
            var10000 = var8;
            break;
         }
      }

      label80: {
         KProperty1 var22 = (KProperty1)var10000;
         if (var22 != null) {
            KProperty1.Getter var23 = var22.getGetter();
            if (var23 != null) {
               Object[] var14 = new Object[]{var1};
               var10000 = var23.call(var14);
               break label80;
            }
         }

         var10000 = null;
      }

      Object var4 = var10000;
      String var3 = var4 instanceof String ? (String)var4 : null;
      switch (FeedbackApi.WhenMappings.$EnumSwitchMapping$0[var2.a().ordinal()]) {
         case 1:
            Intrinsics.checkNotNull(var2);
            var10000 = ((Success)var2).a();
            Intrinsics.checkNotNull(var10000);
            return (Response)var10000;
         case 2:
            throw new UnsupportedOperationException("Client does not support Informational responses.");
         case 3:
            throw new UnsupportedOperationException("Client does not support Redirection responses.");
         case 4:
            Intrinsics.checkNotNull(var2);
            ClientError var13 = (ClientError)var2;
            if (var13.a() != 429) {
               StringBuilder var18 = new StringBuilder();
               boolean var21 = false;
               int var10001 = var13.a();
               String var34 = var13.a();
               if (var34 == null) {
                  var34 = "";
               }

               var18.append("Client error : " + var10001 + " " + var34);
               if (var3 != null) {
                  boolean var11 = false;
                  var18.append(" Request ID: " + var3);
               }

               String var32 = var18.toString();
               Intrinsics.checkNotNullExpressionValue(var32, "");
               String var16 = var32;
               throw new ClientException(var16, var13.a(), var2);
            } else {
               label72: {
                  List var26 = (List)var13.a().get("retry-after");
                  if (var26 != null) {
                     String var27 = (String)CollectionsKt.firstOrNull(var26);
                     if (var27 != null) {
                        var28 = StringsKt.toLongOrNull(var27);
                        break label72;
                     }
                  }

                  var28 = null;
               }

               Long var15 = var28;
               List var29 = (List)var13.a().get("quota-key");
               String var17 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-request-delayed");
               String var19 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-limit-reason");
               String var20 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               RateLimitExceptionKt.a(this.b(), var15, var17);
               if (Intrinsics.areEqual(var19, "1") && var15 != null) {
                  throw new RequestDelayedException(var15);
               }

               throw new RateLimitException(var15, var20, (String)null, (String)null, (Throwable)null, 28, (DefaultConstructorMarker)null);
            }
         case 5:
            Intrinsics.checkNotNull(var2);
            ServerError var12 = (ServerError)var2;
            ServerException var25 = new ServerException;
            int var10002 = var12.a();
            String var10003 = var12.a();
            if (var10003 == null) {
               var10003 = "";
            }

            var25.<init>("Server error : " + var10002 + " " + var10003 + " " + var12.a(), var12.a(), var2);
            throw var25;
         default:
            throw new NoWhenBranchMatchedException();
      }
   }

   @NotNull
   public final Future<Response> c(@NotNull UserFeedbackRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      Future var2 = this.d(var1);
      return var2;
   }

   @NotNull
   public final ApiResponse<Response> b(@NotNull UserFeedbackRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final Future<Response> d(@NotNull UserFeedbackRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final RequestConfig<UserFeedbackRequest> b(@NotNull UserFeedbackRequest var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      Map var3 = (Map)(new LinkedHashMap());
      Map var4 = (Map)(new LinkedHashMap());
      var4.put("Content-Type", "application/json");
      var4.put("Accept", "application/json");
      RequestMethod var5 = RequestMethod.POST;
      return new RequestConfig<UserFeedbackRequest>(var5, "/user_feedback", var4, (Map)null, var3, false, var1, 8, (DefaultConstructorMarker)null);
   }

   private final String a(String var1) {
      return (String)(new HttpUrl.Builder()).scheme("http").host("localhost").addPathSegment(var1).build().encodedPathSegments().get(0);
   }

   @NotNull
   public final FeedbackApi a(@Nullable String var1) {
      ApiClient.Companion var10000 = ApiClient.Companion;
      String var10001 = var1;
      if (var1 == null) {
         var10001 = "";
      }

      var10000.c(var10001);
      return this;
   }

   private static final String f() {
      return System.getProperties().getProperty("ai.zencoder.generated.client.feedback.baseUrl", "http://localhost/feedback-service");
   }

   public FeedbackApi() {
      this((String)null, (OkHttpClient)null, 3, (DefaultConstructorMarker)null);
   }

   @NotNull
   public static final String a() {
      return Companion.a();
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R!\u0010\u0004\u001a\u00020\u00058FX\u0087\u0084\u0002\u00a2\u0006\u0012\n\u0004\b\t\u0010\n\u0012\u0004\b\u0006\u0010\u0003\u001a\u0004\b\u0007\u0010\b\u00a8\u0006\u000b"},
      d2 = {"Lai/zencoder/generated/client/feedback/apis/FeedbackApi$Companion;", "", "<init>", "()V", "defaultBasePath", "", "getDefaultBasePath$annotations", "getDefaultBasePath", "()Ljava/lang/String;", "defaultBasePath$delegate", "Lkotlin/Lazy;", "zencoder"}
   )
   public static final class Companion {
      private Companion() {
      }

      @NotNull
      public final String a() {
         Lazy var1 = FeedbackApi.defaultBasePath$delegate;
         Object var10000 = var1.getValue();
         Intrinsics.checkNotNullExpressionValue(var10000, "");
         return (String)var10000;
      }
   }
}
