package ai.zencoder.generated.client.feedback.infrastructure;

import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u0000*\u0004\b\u0000\u0010\u00012\u00020\u0002B)\u0012\u0014\b\u0002\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u0004\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00018\u0000\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0015\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u0004H\u00c6\u0003J\u0010\u0010\u000f\u001a\u0004\u0018\u00018\u0000H\u00c6\u0003\u00a2\u0006\u0002\u0010\fJ6\u0010\u0010\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\u0014\b\u0002\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u00042\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00018\u0000H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0011J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0002H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0005H\u00d6\u0001R\u001d\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0006\u001a\u0004\u0018\u00018\u0000\u00a2\u0006\n\n\u0002\u0010\r\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/feedback/infrastructure/PartConfig;", "T", "", "headers", "", "", "body", "<init>", "(Ljava/util/Map;Ljava/lang/Object;)V", "getHeaders", "()Ljava/util/Map;", "getBody", "()Ljava/lang/Object;", "Ljava/lang/Object;", "component1", "component2", "copy", "(Ljava/util/Map;Ljava/lang/Object;)Lai/zencoder/generated/client/feedback/infrastructure/PartConfig;", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class PartConfig<T> {
   @NotNull
   private final Map<String, String> headers;
   @Nullable
   private final T body;

   public PartConfig(@NotNull Map<String, String> headers, @Nullable T body) {
      Intrinsics.checkNotNullParameter(headers, "");
      super();
      this.headers = headers;
      this.body = (T)body;
   }

   @NotNull
   public final Map<String, String> a() {
      return this.headers;
   }

   @Nullable
   public final T a() {
      return this.body;
   }

   @NotNull
   public final Map<String, String> b() {
      return this.headers;
   }

   @Nullable
   public final T b() {
      return this.body;
   }

   @NotNull
   public final PartConfig<T> a(@NotNull Map<String, String> var1, @Nullable T var2) {
      Intrinsics.checkNotNullParameter(var1, "");
      return new PartConfig<T>(var1, var2);
   }

   @NotNull
   public String toString() {
      return "PartConfig(headers=" + this.headers + ", body=" + this.body + ")";
   }

   public int hashCode() {
      int var1 = this.headers.hashCode();
      var1 = var1 * 31 + (this.body == null ? 0 : this.body.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof PartConfig)) {
         return false;
      } else {
         PartConfig var2 = (PartConfig)other;
         if (!Intrinsics.areEqual(this.headers, var2.headers)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.body, var2.body);
         }
      }
   }

   public PartConfig() {
      this((Map)null, (Object)null, 3, (DefaultConstructorMarker)null);
   }
}
