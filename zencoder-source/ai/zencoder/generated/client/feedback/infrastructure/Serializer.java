package ai.zencoder.generated.client.feedback.infrastructure;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.kotlin.ExtensionsKt;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u001c\u0010\u0004\u001a\u00020\u00058\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0006\u0010\u0003\u001a\u0004\b\u0007\u0010\b\u00a8\u0006\t"},
   d2 = {"Lai/zencoder/generated/client/feedback/infrastructure/Serializer;", "", "<init>", "()V", "jacksonObjectMapper", "Lcom/fasterxml/jackson/databind/ObjectMapper;", "getJacksonObjectMapper$annotations", "getJacksonObjectMapper", "()Lcom/fasterxml/jackson/databind/ObjectMapper;", "zencoder"}
)
public final class Serializer {
   @NotNull
   public static final Serializer INSTANCE = new Serializer();
   @NotNull
   private static final ObjectMapper jacksonObjectMapper;

   private Serializer() {
   }

   @NotNull
   public static final ObjectMapper a() {
      return jacksonObjectMapper;
   }

   static {
      ObjectMapper var10000 = ExtensionsKt.jacksonObjectMapper().registerModule((Module)(new JavaTimeModule())).setSerializationInclusion(Include.NON_ABSENT).configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      Intrinsics.checkNotNullExpressionValue(var10000, "");
      jacksonObjectMapper = var10000;
   }
}
