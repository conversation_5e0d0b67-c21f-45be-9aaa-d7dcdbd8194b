package ai.zencoder.generated.client.feedback.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u000f\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B#\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J)\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00052\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\n\u00a8\u0006\u0017"},
   d2 = {"Lai/zencoder/generated/client/feedback/models/UserFeedback;", "", "text", "", "accepted", "", "modification", "<init>", "(Ljava/lang/String;ZLjava/lang/String;)V", "getText", "()Ljava/lang/String;", "getAccepted", "()Z", "getModification", "component1", "component2", "component3", "copy", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class UserFeedback {
   @NotNull
   private final String text;
   private final boolean accepted;
   @Nullable
   private final String modification;

   public UserFeedback(@NotNull String text, boolean accepted, @Nullable String modification) {
      Intrinsics.checkNotNullParameter(text, "");
      super();
      this.text = text;
      this.accepted = accepted;
      this.modification = modification;
   }

   @JsonProperty("text")
   @NotNull
   public final String getText() {
      return this.text;
   }

   @JsonProperty("accepted")
   public final boolean getAccepted() {
      return this.accepted;
   }

   @JsonProperty("modification")
   @Nullable
   public final String getModification() {
      return this.modification;
   }

   @NotNull
   public final String component1() {
      return this.text;
   }

   public final boolean component2() {
      return this.accepted;
   }

   @Nullable
   public final String component3() {
      return this.modification;
   }

   @NotNull
   public final UserFeedback copy(@NotNull String text, boolean accepted, @Nullable String modification) {
      Intrinsics.checkNotNullParameter(text, "");
      return new UserFeedback(text, accepted, modification);
   }

   @NotNull
   public String toString() {
      return "UserFeedback(text=" + this.text + ", accepted=" + this.accepted + ", modification=" + this.modification + ")";
   }

   public int hashCode() {
      int var1 = this.text.hashCode();
      var1 = var1 * 31 + Boolean.hashCode(this.accepted);
      var1 = var1 * 31 + (this.modification == null ? 0 : this.modification.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof UserFeedback)) {
         return false;
      } else {
         UserFeedback var2 = (UserFeedback)other;
         if (!Intrinsics.areEqual(this.text, var2.text)) {
            return false;
         } else if (this.accepted != var2.accepted) {
            return false;
         } else {
            return Intrinsics.areEqual(this.modification, var2.modification);
         }
      }
   }
}
