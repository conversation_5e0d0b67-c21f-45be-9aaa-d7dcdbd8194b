package ai.zencoder.generated.client.feedback.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u001bB+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\u0004\b\t\u0010\nJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J\u0015\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J3\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u0014\b\u0002\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u001f\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\b0\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/feedback/models/UserFeedbackRequest;", "", "requestId", "", "ideName", "Lai/zencoder/generated/client/feedback/models/UserFeedbackRequest$IdeName;", "feedback", "", "Lai/zencoder/generated/client/feedback/models/UserFeedback;", "<init>", "(Ljava/lang/String;Lai/zencoder/generated/client/feedback/models/UserFeedbackRequest$IdeName;Ljava/util/Map;)V", "getRequestId", "()Ljava/lang/String;", "getIdeName", "()Lai/zencoder/generated/client/feedback/models/UserFeedbackRequest$IdeName;", "getFeedback", "()Ljava/util/Map;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "IdeName", "zencoder"}
)
public final class UserFeedbackRequest {
   @NotNull
   private final String requestId;
   @NotNull
   private final IdeName ideName;
   @NotNull
   private final Map<String, UserFeedback> feedback;

   public UserFeedbackRequest(@NotNull String requestId, @NotNull IdeName ideName, @NotNull Map<String, UserFeedback> feedback) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(ideName, "");
      Intrinsics.checkNotNullParameter(feedback, "");
      super();
      this.requestId = requestId;
      this.ideName = ideName;
      this.feedback = feedback;
   }

   @JsonProperty("request_id")
   @NotNull
   public final String getRequestId() {
      return this.requestId;
   }

   @JsonProperty("ide_name")
   @NotNull
   public final IdeName getIdeName() {
      return this.ideName;
   }

   @JsonProperty("feedback")
   @NotNull
   public final Map<String, UserFeedback> getFeedback() {
      return this.feedback;
   }

   @NotNull
   public final String component1() {
      return this.requestId;
   }

   @NotNull
   public final IdeName component2() {
      return this.ideName;
   }

   @NotNull
   public final Map<String, UserFeedback> component3() {
      return this.feedback;
   }

   @NotNull
   public final UserFeedbackRequest copy(@NotNull String requestId, @NotNull IdeName ideName, @NotNull Map<String, UserFeedback> feedback) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(ideName, "");
      Intrinsics.checkNotNullParameter(feedback, "");
      return new UserFeedbackRequest(requestId, ideName, feedback);
   }

   @NotNull
   public String toString() {
      return "UserFeedbackRequest(requestId=" + this.requestId + ", ideName=" + this.ideName + ", feedback=" + this.feedback + ")";
   }

   public int hashCode() {
      int var1 = this.requestId.hashCode();
      var1 = var1 * 31 + this.ideName.hashCode();
      var1 = var1 * 31 + this.feedback.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof UserFeedbackRequest)) {
         return false;
      } else {
         UserFeedbackRequest var2 = (UserFeedbackRequest)other;
         if (!Intrinsics.areEqual(this.requestId, var2.requestId)) {
            return false;
         } else if (this.ideName != var2.ideName) {
            return false;
         } else {
            return Intrinsics.areEqual(this.feedback, var2.feedback);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/feedback/models/UserFeedbackRequest$IdeName;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "vscode", "jetbrains", "zencoder"}
   )
   public static enum IdeName {
      @NotNull
      private final String value;
      @JsonProperty("vscode")
      vscode("vscode"),
      @JsonProperty("jetbrains")
      jetbrains("jetbrains");

      private IdeName(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<IdeName> getEntries() {
         return $ENTRIES;
      }
   }
}
