package ai.zencoder.generated.client.feedback.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0004\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0004H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0004H\u00c6\u0003J-\u0010\u0011\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u0004H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0004H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0005\u001a\u00020\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0006\u001a\u00020\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\f\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/feedback/models/ValidationError;", "", "loc", "", "", "msg", "type", "<init>", "(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;)V", "getLoc", "()Ljava/util/List;", "getMsg", "()Ljava/lang/String;", "getType", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class ValidationError {
   @NotNull
   private final List<String> loc;
   @NotNull
   private final String msg;
   @NotNull
   private final String type;

   public ValidationError(@NotNull List<String> loc, @NotNull String msg, @NotNull String type) {
      Intrinsics.checkNotNullParameter(loc, "");
      Intrinsics.checkNotNullParameter(msg, "");
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.loc = loc;
      this.msg = msg;
      this.type = type;
   }

   @JsonProperty("loc")
   @NotNull
   public final List<String> getLoc() {
      return this.loc;
   }

   @JsonProperty("msg")
   @NotNull
   public final String getMsg() {
      return this.msg;
   }

   @JsonProperty("type")
   @NotNull
   public final String getType() {
      return this.type;
   }

   @NotNull
   public final List<String> component1() {
      return this.loc;
   }

   @NotNull
   public final String component2() {
      return this.msg;
   }

   @NotNull
   public final String component3() {
      return this.type;
   }

   @NotNull
   public final ValidationError copy(@NotNull List<String> loc, @NotNull String msg, @NotNull String type) {
      Intrinsics.checkNotNullParameter(loc, "");
      Intrinsics.checkNotNullParameter(msg, "");
      Intrinsics.checkNotNullParameter(type, "");
      return new ValidationError(loc, msg, type);
   }

   @NotNull
   public String toString() {
      return "ValidationError(loc=" + this.loc + ", msg=" + this.msg + ", type=" + this.type + ")";
   }

   public int hashCode() {
      int var1 = this.loc.hashCode();
      var1 = var1 * 31 + this.msg.hashCode();
      var1 = var1 * 31 + this.type.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ValidationError)) {
         return false;
      } else {
         ValidationError var2 = (ValidationError)other;
         if (!Intrinsics.areEqual(this.loc, var2.loc)) {
            return false;
         } else if (!Intrinsics.areEqual(this.msg, var2.msg)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.type, var2.type);
         }
      }
   }
}
