package ai.zencoder.generated.client.jira.infrastructure;

import ai.zencoder.plugin.utils.HttpUtilsKt;
import ai.zencoder.plugin.utils.auth.ZenRefreshAuthenticator;
import io.sentry.okhttp.SentryOkHttpEventListener;
import io.sentry.okhttp.SentryOkHttpInterceptor;
import java.io.File;
import java.net.URLConnection;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import kotlin.Metadata;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.text.StringsKt;
import okhttp3.Call;
import okhttp3.Credentials;
import okhttp3.EventListener;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0016\u0018\u0000 &2\u00020\u0001:\u0001&B\u0019\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0010\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u000eH\u0004J(\u0010\u000f\u001a\u00020\u0010\"\u0006\b\u0000\u0010\u0011\u0018\u00012\u0006\u0010\u0012\u001a\u0002H\u00112\b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u0084\b\u00a2\u0006\u0002\u0010\u0014J2\u0010\u0015\u001a\u0004\u0018\u0001H\u0011\"\f\b\u0000\u0010\u0011\u0018\u0001*\u0004\u0018\u00010\u00012\u0006\u0010\u0016\u001a\u00020\u00172\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u0084\b\u00a2\u0006\u0002\u0010\u0018J\u001c\u0010\u0019\u001a\u00020\u001a\"\u0004\b\u0000\u0010\u00112\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u0002H\u00110\u001cH\u0004J5\u0010\u001d\u001a\n\u0012\u0006\u0012\u0004\u0018\u0001H\u00110\u001e\"\u0006\b\u0000\u0010\u001f\u0018\u0001\"\f\b\u0001\u0010\u0011\u0018\u0001*\u0004\u0018\u00010\u00012\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u0002H\u001f0\u001cH\u0084\bJ\u0012\u0010 \u001a\u00020\u00032\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u0004J3\u0010\"\u001a\b\u0012\u0004\u0012\u0002H\u00110#\"\u0006\b\u0000\u0010\u001f\u0018\u0001\"\f\b\u0001\u0010\u0011\u0018\u0001*\u0004\u0018\u00010\u00012\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u0002H\u001f0\u001cH\u0084\bJ\"\u0010$\u001a\u00020\u0003\"\n\b\u0000\u0010\u0011\u0018\u0001*\u00020\u00012\u0006\u0010!\u001a\u0002H\u0011H\u0084\b\u00a2\u0006\u0002\u0010%R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006'"},
   d2 = {"Lai/zencoder/generated/client/jira/infrastructure/ApiClient;", "", "baseUrl", "", "client", "Lokhttp3/Call$Factory;", "<init>", "(Ljava/lang/String;Lokhttp3/Call$Factory;)V", "getBaseUrl", "()Ljava/lang/String;", "getClient", "()Lokhttp3/Call$Factory;", "guessContentTypeFromFile", "file", "Ljava/io/File;", "requestBody", "Lokhttp3/RequestBody;", "T", "content", "mediaType", "(Ljava/lang/Object;Ljava/lang/String;)Lokhttp3/RequestBody;", "responseBody", "response", "Lokhttp3/Response;", "(Lokhttp3/Response;Ljava/lang/String;)Ljava/lang/Object;", "updateAuthParams", "", "requestConfig", "Lai/zencoder/generated/client/jira/infrastructure/RequestConfig;", "request", "Lai/zencoder/generated/client/jira/infrastructure/ApiResponse;", "I", "parameterToString", "value", "requestAsync", "Ljava/util/concurrent/Future;", "parseDateToQueryString", "(Ljava/lang/Object;)Ljava/lang/String;", "Companion", "zencoder"}
)
@SourceDebugExtension({"SMAP\nApiClient.kt\nKotlin\n*S Kotlin\n*F\n+ 1 ApiClient.kt\nai/zencoder/generated/client/jira/infrastructure/ApiClient\n+ 2 _Maps.kt\nkotlin/collections/MapsKt___MapsKt\n+ 3 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,414:1\n100#1,9:423\n109#1,17:433\n126#1,6:451\n132#1,2:458\n134#1,15:461\n100#1,9:476\n109#1,17:486\n126#1,6:504\n132#1,2:511\n134#1,15:514\n100#1,9:529\n109#1,17:539\n126#1,6:557\n132#1,2:564\n134#1,15:567\n100#1,9:582\n109#1,17:592\n126#1,6:610\n132#1,2:617\n134#1,15:620\n151#1,64:637\n411#1:701\n100#1,9:706\n109#1,17:716\n126#1,6:734\n132#1,2:741\n134#1,15:744\n100#1,9:759\n109#1,17:769\n126#1,6:787\n132#1,2:794\n134#1,15:797\n100#1,9:812\n109#1,17:822\n126#1,6:840\n132#1,2:847\n134#1,15:850\n100#1,9:865\n109#1,17:875\n126#1,6:893\n132#1,2:900\n134#1,15:903\n215#2,2:415\n215#2,2:417\n215#2:419\n216#2:422\n215#2:432\n216#2:450\n215#2:457\n216#2:460\n215#2:485\n216#2:503\n215#2:510\n216#2:513\n215#2:538\n216#2:556\n215#2:563\n216#2:566\n215#2:591\n216#2:609\n215#2:616\n216#2:619\n215#2,2:635\n215#2:702\n216#2:705\n215#2:715\n216#2:733\n215#2:740\n216#2:743\n215#2:768\n216#2:786\n215#2:793\n216#2:796\n215#2:821\n216#2:839\n215#2:846\n216#2:849\n215#2:874\n216#2:892\n215#2:899\n216#2:902\n215#2,2:918\n1855#3,2:420\n1855#3,2:703\n*S KotlinDebug\n*F\n+ 1 ApiClient.kt\nai/zencoder/generated/client/jira/infrastructure/ApiClient\n*L\n270#1:423,9\n270#1:433,17\n270#1:451,6\n270#1:458,2\n270#1:461,15\n273#1:476,9\n273#1:486,17\n273#1:504,6\n273#1:511,2\n273#1:514,15\n274#1:529,9\n274#1:539,17\n274#1:557,6\n274#1:564,2\n274#1:567,15\n275#1:582,9\n275#1:592,17\n275#1:610,6\n275#1:617,2\n275#1:620,15\n299#1:637,64\n324#1:701\n365#1:706,9\n365#1:716,17\n365#1:734,6\n365#1:741,2\n365#1:744,15\n368#1:759,9\n368#1:769,17\n368#1:787,6\n368#1:794,2\n368#1:797,15\n369#1:812,9\n369#1:822,17\n369#1:840,6\n369#1:847,2\n369#1:850,15\n370#1:865,9\n370#1:875,17\n370#1:893,6\n370#1:900,2\n370#1:903,15\n108#1:415,2\n131#1:417,2\n242#1:419\n242#1:422\n270#1:432\n270#1:450\n270#1:457\n270#1:460\n273#1:485\n273#1:503\n273#1:510\n273#1:513\n274#1:538\n274#1:556\n274#1:563\n274#1:566\n275#1:591\n275#1:609\n275#1:616\n275#1:619\n278#1:635,2\n337#1:702\n337#1:705\n365#1:715\n365#1:733\n365#1:740\n365#1:743\n368#1:768\n368#1:786\n368#1:793\n368#1:796\n369#1:821\n369#1:839\n369#1:846\n369#1:849\n370#1:874\n370#1:892\n370#1:899\n370#1:902\n373#1:918,2\n243#1:420,2\n338#1:703,2\n*E\n"})
public class ApiClient {
   @NotNull
   public static final Companion Companion = new Companion((DefaultConstructorMarker)null);
   @NotNull
   private final String baseUrl;
   @NotNull
   private final Call.Factory client;
   @NotNull
   public static final String CONTENT_TYPE = "Content-Type";
   @NotNull
   public static final String ACCEPT = "Accept";
   @NotNull
   public static final String AUTHORIZATION = "Authorization";
   @NotNull
   public static final String CONTENT_DISPOSITION = "Content-Disposition";
   @NotNull
   protected static final String JSON_MEDIA_TYPE = "application/json";
   @NotNull
   protected static final String FORM_DATA_MEDIA_TYPE = "multipart/form-data";
   @NotNull
   protected static final String FORM_URL_ENC_MEDIA_TYPE = "application/x-www-form-urlencoded";
   @NotNull
   protected static final String XML_MEDIA_TYPE = "application/xml";
   @NotNull
   protected static final String OCTET_MEDIA_TYPE = "application/octet-stream";
   @NotNull
   public static final String RETRY_AFTER = "retry-after";
   @NotNull
   public static final String QUOTA_KEY = "quota-key";
   @NotNull
   public static final String REQUEST_DELAYED = "x-request-delayed";
   @NotNull
   public static final String LIMIT_REASON = "x-limit-reason";
   @NotNull
   private static final Map<String, String> apiKey = (Map)(new LinkedHashMap());
   @NotNull
   private static final Map<String, String> apiKeyPrefix = (Map)(new LinkedHashMap());
   @Nullable
   private static String username;
   @Nullable
   private static String password;
   @Nullable
   private static String accessToken;
   @NotNull
   public static final String BASE_URL_KEY = "ai.zencoder.generated.client.jira.baseUrl";
   @NotNull
   private static final OkHttpClient defaultClient;
   @NotNull
   private static final OkHttpClient.Builder builder;

   public ApiClient(@NotNull String baseUrl, @NotNull Call.Factory client) {
      Intrinsics.checkNotNullParameter(baseUrl, "");
      Intrinsics.checkNotNullParameter(client, "");
      super();
      this.baseUrl = baseUrl;
      this.client = client;
   }

   @NotNull
   public final String b() {
      return this.baseUrl;
   }

   @NotNull
   public final Call.Factory a() {
      return this.client;
   }

   @NotNull
   protected final String a(@NotNull File var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      String var2 = URLConnection.guessContentTypeFromName(var1.getName());
      String var10000 = var2;
      if (var2 == null) {
         var10000 = "application/octet-stream";
      }

      return var10000;
   }

   protected final <T> void a(@NotNull RequestConfig<T> var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      CharSequence var2 = (CharSequence)var1.a().get("Authorization");
      if (var2 == null || var2.length() == 0) {
         String var10000 = accessToken;
         if (var10000 != null) {
            String var3 = var10000;
            boolean var4 = false;
            var1.a().put("Authorization", "Bearer " + var3 + " ");
         }
      }

      var2 = (CharSequence)var1.a().get("Authorization");
      if (var2 == null || var2.length() == 0) {
         String var10 = username;
         if (var10 != null) {
            String var8 = var10;
            boolean var9 = false;
            var10 = password;
            if (var10 != null) {
               String var5 = var10;
               boolean var6 = false;
               var1.a().put("Authorization", Credentials.basic$default(var8, var5, (Charset)null, 4, (Object)null));
            }
         }
      }

   }

   @NotNull
   protected final String a(@Nullable Object var1) {
      String var10000;
      if (var1 == null) {
         var10000 = "";
      } else if (var1 instanceof Object[]) {
         var10000 = ApiAbstractionsKt.a((Object[])(var1), "csv", (Function1)null, 4, (Object)null).toString();
      } else if (var1 instanceof Iterable) {
         var10000 = ApiAbstractionsKt.a((Iterable)((Iterable)var1), "csv", (Function1)null, 4, (Object)null).toString();
      } else if (!(var1 instanceof OffsetDateTime) && !(var1 instanceof OffsetTime) && !(var1 instanceof LocalDateTime) && !(var1 instanceof LocalDate) && !(var1 instanceof LocalTime)) {
         var10000 = var1.toString();
      } else {
         boolean var4 = false;
         var10000 = Serializer.a().writeValueAsString(var1);
         Intrinsics.checkNotNullExpressionValue(var10000, "");
         var10000 = StringsKt.replace$default(var10000, "\"", "", false, 4, (Object)null);
      }

      return var10000;
   }

   @NotNull
   public static final OkHttpClient a() {
      return Companion.a();
   }

   @NotNull
   public static final OkHttpClient.Builder a() {
      return Companion.a();
   }

   static {
      defaultClient = HttpUtilsKt.a((new OkHttpClient()).newBuilder()).eventListener((EventListener)(new SentryOkHttpEventListener())).addInterceptor((Interceptor)(new SentryOkHttpInterceptor())).authenticator(ZenRefreshAuthenticator.INSTANCE).readTimeout(150L, TimeUnit.SECONDS).build();
      builder = HttpUtilsKt.a(new OkHttpClient.Builder()).eventListener((EventListener)(new SentryOkHttpEventListener())).addInterceptor((Interceptor)(new SentryOkHttpInterceptor()));
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010%\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0005X\u0084T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0005X\u0084T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0005X\u0084T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0005X\u0084T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0005X\u0084T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u001d\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u001c\u0010\u0018\u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0019\u0010\u001a\"\u0004\b\u001b\u0010\u001cR\u001c\u0010\u001d\u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001e\u0010\u001a\"\u0004\b\u001f\u0010\u001cR\u001c\u0010 \u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b!\u0010\u001a\"\u0004\b\"\u0010\u001cR\u000e\u0010#\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u001c\u0010$\u001a\u00020%8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b&\u0010\u0003\u001a\u0004\b'\u0010(R\u001c\u0010)\u001a\u00020*8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b+\u0010\u0003\u001a\u0004\b,\u0010-\u00a8\u0006."},
      d2 = {"Lai/zencoder/generated/client/jira/infrastructure/ApiClient$Companion;", "", "<init>", "()V", "CONTENT_TYPE", "", "ACCEPT", "AUTHORIZATION", "CONTENT_DISPOSITION", "JSON_MEDIA_TYPE", "FORM_DATA_MEDIA_TYPE", "FORM_URL_ENC_MEDIA_TYPE", "XML_MEDIA_TYPE", "OCTET_MEDIA_TYPE", "RETRY_AFTER", "QUOTA_KEY", "REQUEST_DELAYED", "LIMIT_REASON", "apiKey", "", "getApiKey", "()Ljava/util/Map;", "apiKeyPrefix", "getApiKeyPrefix", "username", "getUsername", "()Ljava/lang/String;", "setUsername", "(Ljava/lang/String;)V", "password", "getPassword", "setPassword", "accessToken", "getAccessToken", "setAccessToken", "BASE_URL_KEY", "defaultClient", "Lokhttp3/OkHttpClient;", "getDefaultClient$annotations", "getDefaultClient", "()Lokhttp3/OkHttpClient;", "builder", "Lokhttp3/OkHttpClient$Builder;", "getBuilder$annotations", "getBuilder", "()Lokhttp3/OkHttpClient$Builder;", "zencoder"}
   )
   public static final class Companion {
      private Companion() {
      }

      @NotNull
      public final Map<String, String> a() {
         return ApiClient.apiKey;
      }

      @NotNull
      public final Map<String, String> b() {
         return ApiClient.apiKeyPrefix;
      }

      @Nullable
      public final String a() {
         return ApiClient.username;
      }

      public final void a(@Nullable String var1) {
         ApiClient.username = var1;
      }

      @Nullable
      public final String b() {
         return ApiClient.password;
      }

      public final void b(@Nullable String var1) {
         ApiClient.password = var1;
      }

      @Nullable
      public final String c() {
         return ApiClient.accessToken;
      }

      public final void c(@Nullable String var1) {
         ApiClient.accessToken = var1;
      }

      @NotNull
      public final OkHttpClient a() {
         return ApiClient.defaultClient;
      }

      @NotNull
      public final OkHttpClient.Builder a() {
         return ApiClient.builder;
      }
   }
}
