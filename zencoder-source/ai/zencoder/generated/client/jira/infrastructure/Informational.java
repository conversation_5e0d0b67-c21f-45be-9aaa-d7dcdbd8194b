package ai.zencoder.generated.client.jira.infrastructure;

import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010$\n\u0002\u0010 \n\u0002\b\t\u0018\u0000*\u0004\b\u0000\u0010\u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B5\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\u001a\b\u0002\u0010\u0007\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\t0\b\u00a2\u0006\u0004\b\n\u0010\u000bR\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0014\u0010\u0005\u001a\u00020\u0006X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR&\u0010\u0007\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\t0\bX\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u0006\u0012"},
   d2 = {"Lai/zencoder/generated/client/jira/infrastructure/Informational;", "T", "Lai/zencoder/generated/client/jira/infrastructure/ApiResponse;", "statusText", "", "statusCode", "", "headers", "", "", "<init>", "(Ljava/lang/String;ILjava/util/Map;)V", "getStatusText", "()Ljava/lang/String;", "getStatusCode", "()I", "getHeaders", "()Ljava/util/Map;", "zencoder"}
)
public final class Informational<T> extends ApiResponse<T> {
   @NotNull
   private final String statusText;
   private final int statusCode;
   @NotNull
   private final Map<String, List<String>> headers;

   public Informational(@NotNull String statusText, int statusCode, @NotNull Map<String, ? extends List<String>> headers) {
      Intrinsics.checkNotNullParameter(statusText, "");
      Intrinsics.checkNotNullParameter(headers, "");
      super(ResponseType.Informational);
      this.statusText = statusText;
      this.statusCode = statusCode;
      this.headers = headers;
   }

   @NotNull
   public final String a() {
      return this.statusText;
   }

   public int a() {
      return this.statusCode;
   }

   @NotNull
   public Map<String, List<String>> a() {
      return this.headers;
   }
}
