package ai.zencoder.generated.client.jira.infrastructure;

import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;

@Metadata(
   mv = {2, 1, 0},
   k = 2,
   xi = 48,
   d1 = {"\u0000\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\u0018\u0002\n\u0002\b\u0007\"\u0015\u0010\u0000\u001a\u00020\u0001*\u00020\u00028F\u00a2\u0006\u0006\u001a\u0004\b\u0000\u0010\u0003\"\u001b\u0010\u0004\u001a\u00020\u0001*\u00020\u00028F\u00a2\u0006\f\u0012\u0004\b\u0005\u0010\u0006\u001a\u0004\b\u0004\u0010\u0003\"\u0015\u0010\u0007\u001a\u00020\u0001*\u00020\u00028F\u00a2\u0006\u0006\u001a\u0004\b\u0007\u0010\u0003\"\u0015\u0010\b\u001a\u00020\u0001*\u00020\u00028F\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\u0003\u00a8\u0006\t"},
   d2 = {"isInformational", "", "Lokhttp3/Response;", "(Lokhttp3/Response;)Z", "isRedirect", "isRedirect$annotations", "(Lokhttp3/Response;)V", "isClientError", "isServerError", "zencoder"}
)
public final class ResponseExtensionsKt {
   public static final boolean a(@NotNull okhttp3.Response var0) {
      Intrinsics.checkNotNullParameter(var0, "");
      int var1 = var0.code();
      return 100 <= var1 ? var1 < 200 : false;
   }

   public static final boolean b(@NotNull okhttp3.Response var0) {
      Intrinsics.checkNotNullParameter(var0, "");
      int var1 = var0.code();
      return 300 <= var1 ? var1 < 400 : false;
   }

   public static final boolean c(@NotNull okhttp3.Response var0) {
      Intrinsics.checkNotNullParameter(var0, "");
      int var1 = var0.code();
      return 400 <= var1 ? var1 < 500 : false;
   }

   public static final boolean d(@NotNull okhttp3.Response var0) {
      Intrinsics.checkNotNullParameter(var0, "");
      int var1 = var0.code();
      return 500 <= var1 ? var1 < 1000 : false;
   }
}
