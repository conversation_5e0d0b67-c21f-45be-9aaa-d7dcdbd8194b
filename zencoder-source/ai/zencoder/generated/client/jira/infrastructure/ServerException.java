package ai.zencoder.generated.client.jira.infrastructure;

import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\b\u0016\u0018\u0000 \u000e2\u00020\u0001:\u0001\u000eB)\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u000f"},
   d2 = {"Lai/zencoder/generated/client/jira/infrastructure/ServerException;", "Ljava/lang/RuntimeException;", "message", "", "statusCode", "", "response", "Lai/zencoder/generated/client/jira/infrastructure/Response;", "<init>", "(Ljava/lang/String;ILai/zencoder/generated/client/jira/infrastructure/Response;)V", "getStatusCode", "()I", "getResponse", "()Lai/zencoder/generated/client/jira/infrastructure/Response;", "Companion", "zencoder"}
)
public class ServerException extends RuntimeException {
   @NotNull
   public static final Companion Companion = new Companion((DefaultConstructorMarker)null);
   private final int statusCode;
   @Nullable
   private final Response response;
   private static final long serialVersionUID = 456L;

   public ServerException(@Nullable String message, int statusCode, @Nullable Response response) {
      super(message);
      this.statusCode = statusCode;
      this.response = response;
   }

   public final int a() {
      return this.statusCode;
   }

   @Nullable
   public final Response a() {
      return this.response;
   }

   public ServerException() {
      this((String)null, 0, (Response)null, 7, (DefaultConstructorMarker)null);
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"},
      d2 = {"Lai/zencoder/generated/client/jira/infrastructure/ServerException$Companion;", "", "<init>", "()V", "serialVersionUID", "", "zencoder"}
   )
   public static final class Companion {
      private Companion() {
      }
   }
}
