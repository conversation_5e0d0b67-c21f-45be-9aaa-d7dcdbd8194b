package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u001fBC\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0004\b\n\u0010\u000bJ\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000eJ\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000eJ\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\tH\u00c6\u0003JJ\u0010\u0018\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\tH\u00c6\u0001\u00a2\u0006\u0002\u0010\u0019J\u0013\u0010\u001a\u001a\u00020\u00052\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\u000f\u001a\u0004\b\u0004\u0010\u000eR\u0017\u0010\u0006\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\u000f\u001a\u0004\b\u0006\u0010\u000eR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\rR\u0015\u0010\b\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006 "},
   d2 = {"Lai/zencoder/generated/client/jira/models/AnnouncementBannerConfiguration;", "", "hashId", "", "isDismissible", "", "isEnabled", "message", "visibility", "Lai/zencoder/generated/client/jira/models/AnnouncementBannerConfiguration$Visibility;", "<init>", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/AnnouncementBannerConfiguration$Visibility;)V", "getHashId", "()Ljava/lang/String;", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getMessage", "getVisibility", "()Lai/zencoder/generated/client/jira/models/AnnouncementBannerConfiguration$Visibility;", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/AnnouncementBannerConfiguration$Visibility;)Lai/zencoder/generated/client/jira/models/AnnouncementBannerConfiguration;", "equals", "other", "hashCode", "", "toString", "Visibility", "zencoder"}
)
public final class AnnouncementBannerConfiguration {
   @Nullable
   private final String hashId;
   @Nullable
   private final Boolean isDismissible;
   @Nullable
   private final Boolean isEnabled;
   @Nullable
   private final String message;
   @Nullable
   private final Visibility visibility;

   public AnnouncementBannerConfiguration(@Nullable String hashId, @Nullable Boolean isDismissible, @Nullable Boolean isEnabled, @Nullable String message, @Nullable Visibility visibility) {
      this.hashId = hashId;
      this.isDismissible = isDismissible;
      this.isEnabled = isEnabled;
      this.message = message;
      this.visibility = visibility;
   }

   @JsonProperty("hashId")
   @Nullable
   public final String getHashId() {
      return this.hashId;
   }

   @JsonProperty("isDismissible")
   @Nullable
   public final Boolean isDismissible() {
      return this.isDismissible;
   }

   @JsonProperty("isEnabled")
   @Nullable
   public final Boolean isEnabled() {
      return this.isEnabled;
   }

   @JsonProperty("message")
   @Nullable
   public final String getMessage() {
      return this.message;
   }

   @JsonProperty("visibility")
   @Nullable
   public final Visibility getVisibility() {
      return this.visibility;
   }

   @Nullable
   public final String component1() {
      return this.hashId;
   }

   @Nullable
   public final Boolean component2() {
      return this.isDismissible;
   }

   @Nullable
   public final Boolean component3() {
      return this.isEnabled;
   }

   @Nullable
   public final String component4() {
      return this.message;
   }

   @Nullable
   public final Visibility component5() {
      return this.visibility;
   }

   @NotNull
   public final AnnouncementBannerConfiguration copy(@Nullable String hashId, @Nullable Boolean isDismissible, @Nullable Boolean isEnabled, @Nullable String message, @Nullable Visibility visibility) {
      return new AnnouncementBannerConfiguration(hashId, isDismissible, isEnabled, message, visibility);
   }

   @NotNull
   public String toString() {
      return "AnnouncementBannerConfiguration(hashId=" + this.hashId + ", isDismissible=" + this.isDismissible + ", isEnabled=" + this.isEnabled + ", message=" + this.message + ", visibility=" + this.visibility + ")";
   }

   public int hashCode() {
      int var1 = this.hashId == null ? 0 : this.hashId.hashCode();
      var1 = var1 * 31 + (this.isDismissible == null ? 0 : this.isDismissible.hashCode());
      var1 = var1 * 31 + (this.isEnabled == null ? 0 : this.isEnabled.hashCode());
      var1 = var1 * 31 + (this.message == null ? 0 : this.message.hashCode());
      var1 = var1 * 31 + (this.visibility == null ? 0 : this.visibility.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof AnnouncementBannerConfiguration)) {
         return false;
      } else {
         AnnouncementBannerConfiguration var2 = (AnnouncementBannerConfiguration)other;
         if (!Intrinsics.areEqual(this.hashId, var2.hashId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.isDismissible, var2.isDismissible)) {
            return false;
         } else if (!Intrinsics.areEqual(this.isEnabled, var2.isEnabled)) {
            return false;
         } else if (!Intrinsics.areEqual(this.message, var2.message)) {
            return false;
         } else {
            return this.visibility == var2.visibility;
         }
      }
   }

   public AnnouncementBannerConfiguration() {
      this((String)null, (Boolean)null, (Boolean)null, (String)null, (Visibility)null, 31, (DefaultConstructorMarker)null);
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/AnnouncementBannerConfiguration$Visibility;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "PUBLIC", "PRIVATE", "zencoder"}
   )
   public static enum Visibility {
      @NotNull
      private final String value;
      @JsonProperty("PUBLIC")
      PUBLIC("PUBLIC"),
      @JsonProperty("PRIVATE")
      PRIVATE("PRIVATE");

      private Visibility(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Visibility> getEntries() {
         return $ENTRIES;
      }
   }
}
