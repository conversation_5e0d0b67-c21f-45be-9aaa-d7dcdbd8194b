package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0011\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\b\u0010\tJ\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\nJ\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\nJ\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J>\u0010\u0013\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0014J\u0013\u0010\u0015\u001a\u00020\u00032\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0006H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000b\u001a\u0004\b\u0002\u0010\nR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000b\u001a\u0004\b\u0004\u0010\nR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\r\u00a8\u0006\u001a"},
   d2 = {"Lai/zencoder/generated/client/jira/models/AnnouncementBannerConfigurationUpdate;", "", "isDismissible", "", "isEnabled", "message", "", "visibility", "<init>", "(Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;)V", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getMessage", "()Ljava/lang/String;", "getVisibility", "component1", "component2", "component3", "component4", "copy", "(Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/AnnouncementBannerConfigurationUpdate;", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class AnnouncementBannerConfigurationUpdate {
   @Nullable
   private final Boolean isDismissible;
   @Nullable
   private final Boolean isEnabled;
   @Nullable
   private final String message;
   @Nullable
   private final String visibility;

   public AnnouncementBannerConfigurationUpdate(@Nullable Boolean isDismissible, @Nullable Boolean isEnabled, @Nullable String message, @Nullable String visibility) {
      this.isDismissible = isDismissible;
      this.isEnabled = isEnabled;
      this.message = message;
      this.visibility = visibility;
   }

   @JsonProperty("isDismissible")
   @Nullable
   public final Boolean isDismissible() {
      return this.isDismissible;
   }

   @JsonProperty("isEnabled")
   @Nullable
   public final Boolean isEnabled() {
      return this.isEnabled;
   }

   @JsonProperty("message")
   @Nullable
   public final String getMessage() {
      return this.message;
   }

   @JsonProperty("visibility")
   @Nullable
   public final String getVisibility() {
      return this.visibility;
   }

   @Nullable
   public final Boolean component1() {
      return this.isDismissible;
   }

   @Nullable
   public final Boolean component2() {
      return this.isEnabled;
   }

   @Nullable
   public final String component3() {
      return this.message;
   }

   @Nullable
   public final String component4() {
      return this.visibility;
   }

   @NotNull
   public final AnnouncementBannerConfigurationUpdate copy(@Nullable Boolean isDismissible, @Nullable Boolean isEnabled, @Nullable String message, @Nullable String visibility) {
      return new AnnouncementBannerConfigurationUpdate(isDismissible, isEnabled, message, visibility);
   }

   @NotNull
   public String toString() {
      return "AnnouncementBannerConfigurationUpdate(isDismissible=" + this.isDismissible + ", isEnabled=" + this.isEnabled + ", message=" + this.message + ", visibility=" + this.visibility + ")";
   }

   public int hashCode() {
      int var1 = this.isDismissible == null ? 0 : this.isDismissible.hashCode();
      var1 = var1 * 31 + (this.isEnabled == null ? 0 : this.isEnabled.hashCode());
      var1 = var1 * 31 + (this.message == null ? 0 : this.message.hashCode());
      var1 = var1 * 31 + (this.visibility == null ? 0 : this.visibility.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof AnnouncementBannerConfigurationUpdate)) {
         return false;
      } else {
         AnnouncementBannerConfigurationUpdate var2 = (AnnouncementBannerConfigurationUpdate)other;
         if (!Intrinsics.areEqual(this.isDismissible, var2.isDismissible)) {
            return false;
         } else if (!Intrinsics.areEqual(this.isEnabled, var2.isEnabled)) {
            return false;
         } else if (!Intrinsics.areEqual(this.message, var2.message)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.visibility, var2.visibility);
         }
      }
   }

   public AnnouncementBannerConfigurationUpdate() {
      this((Boolean)null, (Boolean)null, (String)null, (String)null, 15, (DefaultConstructorMarker)null);
   }
}
