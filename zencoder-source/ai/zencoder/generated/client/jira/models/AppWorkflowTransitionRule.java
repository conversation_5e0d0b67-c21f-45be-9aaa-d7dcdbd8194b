package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\u0004\b\t\u0010\nJ\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\bH\u00c6\u0003J3\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\bH\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0006\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u0006\u001d"},
   d2 = {"Lai/zencoder/generated/client/jira/models/AppWorkflowTransitionRule;", "", "configuration", "Lai/zencoder/generated/client/jira/models/RuleConfiguration;", "id", "", "key", "transition", "Lai/zencoder/generated/client/jira/models/WorkflowTransition;", "<init>", "(Lai/zencoder/generated/client/jira/models/RuleConfiguration;Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/WorkflowTransition;)V", "getConfiguration", "()Lai/zencoder/generated/client/jira/models/RuleConfiguration;", "getId", "()Ljava/lang/String;", "getKey", "getTransition", "()Lai/zencoder/generated/client/jira/models/WorkflowTransition;", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class AppWorkflowTransitionRule {
   @NotNull
   private final RuleConfiguration configuration;
   @NotNull
   private final String id;
   @NotNull
   private final String key;
   @Nullable
   private final WorkflowTransition transition;

   public AppWorkflowTransitionRule(@NotNull RuleConfiguration configuration, @NotNull String id, @NotNull String key, @Nullable WorkflowTransition transition) {
      Intrinsics.checkNotNullParameter(configuration, "");
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(key, "");
      super();
      this.configuration = configuration;
      this.id = id;
      this.key = key;
      this.transition = transition;
   }

   @JsonProperty("configuration")
   @NotNull
   public final RuleConfiguration getConfiguration() {
      return this.configuration;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("key")
   @NotNull
   public final String getKey() {
      return this.key;
   }

   @JsonProperty("transition")
   @Nullable
   public final WorkflowTransition getTransition() {
      return this.transition;
   }

   @NotNull
   public final RuleConfiguration component1() {
      return this.configuration;
   }

   @NotNull
   public final String component2() {
      return this.id;
   }

   @NotNull
   public final String component3() {
      return this.key;
   }

   @Nullable
   public final WorkflowTransition component4() {
      return this.transition;
   }

   @NotNull
   public final AppWorkflowTransitionRule copy(@NotNull RuleConfiguration configuration, @NotNull String id, @NotNull String key, @Nullable WorkflowTransition transition) {
      Intrinsics.checkNotNullParameter(configuration, "");
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(key, "");
      return new AppWorkflowTransitionRule(configuration, id, key, transition);
   }

   @NotNull
   public String toString() {
      return "AppWorkflowTransitionRule(configuration=" + this.configuration + ", id=" + this.id + ", key=" + this.key + ", transition=" + this.transition + ")";
   }

   public int hashCode() {
      int var1 = this.configuration.hashCode();
      var1 = var1 * 31 + this.id.hashCode();
      var1 = var1 * 31 + this.key.hashCode();
      var1 = var1 * 31 + (this.transition == null ? 0 : this.transition.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof AppWorkflowTransitionRule)) {
         return false;
      } else {
         AppWorkflowTransitionRule var2 = (AppWorkflowTransitionRule)other;
         if (!Intrinsics.areEqual(this.configuration, var2.configuration)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.key, var2.key)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.transition, var2.transition);
         }
      }
   }
}
