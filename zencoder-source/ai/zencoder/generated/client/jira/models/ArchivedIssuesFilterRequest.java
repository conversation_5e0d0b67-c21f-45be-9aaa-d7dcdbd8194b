package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004B[\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0006\u0012\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0006\u0012\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0006\u00a2\u0006\u0004\b\f\u0010\rJ\u0011\u0010\u0015\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u0011\u0010\u0017\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0006H\u00c6\u0003J\u0011\u0010\u0018\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0006H\u00c6\u0003J\u0011\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0006H\u00c6\u0003J]\u0010\u001a\u001a\u00020\u00002\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u00062\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u00062\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u001b\u001a\u00020\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001J\t\u0010 \u001a\u00020\u0002H\u00d6\u0001R\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u001b\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000fR\u001b\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000fR\u001b\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u000f\u00a8\u0006!"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ArchivedIssuesFilterRequest;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "archivedBy", "", "archivedDateRange", "Lai/zencoder/generated/client/jira/models/DateRangeFilterRequest;", "issueTypes", "projects", "reporters", "<init>", "(Ljava/util/List;Lai/zencoder/generated/client/jira/models/DateRangeFilterRequest;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getArchivedBy", "()Ljava/util/List;", "getArchivedDateRange", "()Lai/zencoder/generated/client/jira/models/DateRangeFilterRequest;", "getIssueTypes", "getProjects", "getReporters", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class ArchivedIssuesFilterRequest extends HashMap<String, Object> {
   @Nullable
   private final List<String> archivedBy;
   @Nullable
   private final DateRangeFilterRequest archivedDateRange;
   @Nullable
   private final List<String> issueTypes;
   @Nullable
   private final List<String> projects;
   @Nullable
   private final List<String> reporters;

   public ArchivedIssuesFilterRequest(@Nullable List<String> archivedBy, @Nullable DateRangeFilterRequest archivedDateRange, @Nullable List<String> issueTypes, @Nullable List<String> projects, @Nullable List<String> reporters) {
      this.archivedBy = archivedBy;
      this.archivedDateRange = archivedDateRange;
      this.issueTypes = issueTypes;
      this.projects = projects;
      this.reporters = reporters;
   }

   @JsonProperty("archivedBy")
   @Nullable
   public final List<String> getArchivedBy() {
      return this.archivedBy;
   }

   @JsonProperty("archivedDateRange")
   @Nullable
   public final DateRangeFilterRequest getArchivedDateRange() {
      return this.archivedDateRange;
   }

   @JsonProperty("issueTypes")
   @Nullable
   public final List<String> getIssueTypes() {
      return this.issueTypes;
   }

   @JsonProperty("projects")
   @Nullable
   public final List<String> getProjects() {
      return this.projects;
   }

   @JsonProperty("reporters")
   @Nullable
   public final List<String> getReporters() {
      return this.reporters;
   }

   @Nullable
   public final List<String> component1() {
      return this.archivedBy;
   }

   @Nullable
   public final DateRangeFilterRequest component2() {
      return this.archivedDateRange;
   }

   @Nullable
   public final List<String> component3() {
      return this.issueTypes;
   }

   @Nullable
   public final List<String> component4() {
      return this.projects;
   }

   @Nullable
   public final List<String> component5() {
      return this.reporters;
   }

   @NotNull
   public final ArchivedIssuesFilterRequest copy(@Nullable List<String> archivedBy, @Nullable DateRangeFilterRequest archivedDateRange, @Nullable List<String> issueTypes, @Nullable List<String> projects, @Nullable List<String> reporters) {
      return new ArchivedIssuesFilterRequest(archivedBy, archivedDateRange, issueTypes, projects, reporters);
   }

   @NotNull
   public String toString() {
      return "ArchivedIssuesFilterRequest(archivedBy=" + this.archivedBy + ", archivedDateRange=" + this.archivedDateRange + ", issueTypes=" + this.issueTypes + ", projects=" + this.projects + ", reporters=" + this.reporters + ")";
   }

   public int hashCode() {
      int var1 = this.archivedBy == null ? 0 : this.archivedBy.hashCode();
      var1 = var1 * 31 + (this.archivedDateRange == null ? 0 : this.archivedDateRange.hashCode());
      var1 = var1 * 31 + (this.issueTypes == null ? 0 : this.issueTypes.hashCode());
      var1 = var1 * 31 + (this.projects == null ? 0 : this.projects.hashCode());
      var1 = var1 * 31 + (this.reporters == null ? 0 : this.reporters.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ArchivedIssuesFilterRequest)) {
         return false;
      } else {
         ArchivedIssuesFilterRequest var2 = (ArchivedIssuesFilterRequest)other;
         if (!Intrinsics.areEqual(this.archivedBy, var2.archivedBy)) {
            return false;
         } else if (!Intrinsics.areEqual(this.archivedDateRange, var2.archivedDateRange)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueTypes, var2.issueTypes)) {
            return false;
         } else if (!Intrinsics.areEqual(this.projects, var2.projects)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.reporters, var2.reporters);
         }
      }
   }

   public ArchivedIssuesFilterRequest() {
      this((List)null, (DateRangeFilterRequest)null, (List)null, (List)null, (List)null, 31, (DefaultConstructorMarker)null);
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
