package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\u0011\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006H\u00c6\u0003J/\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/AssociateSecuritySchemeWithProjectDetails;", "", "projectId", "", "schemeId", "oldToNewSecurityLevelMappings", "", "Lai/zencoder/generated/client/jira/models/OldToNewSecurityLevelMappingsBean;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V", "getProjectId", "()Ljava/lang/String;", "getSchemeId", "getOldToNewSecurityLevelMappings", "()Ljava/util/List;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class AssociateSecuritySchemeWithProjectDetails {
   @NotNull
   private final String projectId;
   @NotNull
   private final String schemeId;
   @Nullable
   private final List<OldToNewSecurityLevelMappingsBean> oldToNewSecurityLevelMappings;

   public AssociateSecuritySchemeWithProjectDetails(@NotNull String projectId, @NotNull String schemeId, @Nullable List<OldToNewSecurityLevelMappingsBean> oldToNewSecurityLevelMappings) {
      Intrinsics.checkNotNullParameter(projectId, "");
      Intrinsics.checkNotNullParameter(schemeId, "");
      super();
      this.projectId = projectId;
      this.schemeId = schemeId;
      this.oldToNewSecurityLevelMappings = oldToNewSecurityLevelMappings;
   }

   @JsonProperty("projectId")
   @NotNull
   public final String getProjectId() {
      return this.projectId;
   }

   @JsonProperty("schemeId")
   @NotNull
   public final String getSchemeId() {
      return this.schemeId;
   }

   @JsonProperty("oldToNewSecurityLevelMappings")
   @Nullable
   public final List<OldToNewSecurityLevelMappingsBean> getOldToNewSecurityLevelMappings() {
      return this.oldToNewSecurityLevelMappings;
   }

   @NotNull
   public final String component1() {
      return this.projectId;
   }

   @NotNull
   public final String component2() {
      return this.schemeId;
   }

   @Nullable
   public final List<OldToNewSecurityLevelMappingsBean> component3() {
      return this.oldToNewSecurityLevelMappings;
   }

   @NotNull
   public final AssociateSecuritySchemeWithProjectDetails copy(@NotNull String projectId, @NotNull String schemeId, @Nullable List<OldToNewSecurityLevelMappingsBean> oldToNewSecurityLevelMappings) {
      Intrinsics.checkNotNullParameter(projectId, "");
      Intrinsics.checkNotNullParameter(schemeId, "");
      return new AssociateSecuritySchemeWithProjectDetails(projectId, schemeId, oldToNewSecurityLevelMappings);
   }

   @NotNull
   public String toString() {
      return "AssociateSecuritySchemeWithProjectDetails(projectId=" + this.projectId + ", schemeId=" + this.schemeId + ", oldToNewSecurityLevelMappings=" + this.oldToNewSecurityLevelMappings + ")";
   }

   public int hashCode() {
      int var1 = this.projectId.hashCode();
      var1 = var1 * 31 + this.schemeId.hashCode();
      var1 = var1 * 31 + (this.oldToNewSecurityLevelMappings == null ? 0 : this.oldToNewSecurityLevelMappings.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof AssociateSecuritySchemeWithProjectDetails)) {
         return false;
      } else {
         AssociateSecuritySchemeWithProjectDetails var2 = (AssociateSecuritySchemeWithProjectDetails)other;
         if (!Intrinsics.areEqual(this.projectId, var2.projectId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.schemeId, var2.schemeId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.oldToNewSecurityLevelMappings, var2.oldToNewSecurityLevelMappings);
         }
      }
   }
}
