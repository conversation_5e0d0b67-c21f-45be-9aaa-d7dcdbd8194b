package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BC\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\t\u0010\nJ\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\fJ\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003JJ\u0010\u0018\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0019J\u0013\u0010\u001a\u001a\u00020\u001b2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001d\u001a\u00020\u001eH\u00d6\u0001J\t\u0010\u001f\u001a\u00020\u0005H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\r\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u0015\u0010\b\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000f\u00a8\u0006 "},
   d2 = {"Lai/zencoder/generated/client/jira/models/AttachmentArchiveItemReadable;", "", "index", "", "label", "", "mediaType", "path", "propertySize", "<init>", "(Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getIndex", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getLabel", "()Ljava/lang/String;", "getMediaType", "getPath", "getPropertySize", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/AttachmentArchiveItemReadable;", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class AttachmentArchiveItemReadable {
   @Nullable
   private final Long index;
   @Nullable
   private final String label;
   @Nullable
   private final String mediaType;
   @Nullable
   private final String path;
   @Nullable
   private final String propertySize;

   public AttachmentArchiveItemReadable(@Nullable Long index, @Nullable String label, @Nullable String mediaType, @Nullable String path, @Nullable String propertySize) {
      this.index = index;
      this.label = label;
      this.mediaType = mediaType;
      this.path = path;
      this.propertySize = propertySize;
   }

   @JsonProperty("index")
   @Nullable
   public final Long getIndex() {
      return this.index;
   }

   @JsonProperty("label")
   @Nullable
   public final String getLabel() {
      return this.label;
   }

   @JsonProperty("mediaType")
   @Nullable
   public final String getMediaType() {
      return this.mediaType;
   }

   @JsonProperty("path")
   @Nullable
   public final String getPath() {
      return this.path;
   }

   @JsonProperty("size")
   @Nullable
   public final String getPropertySize() {
      return this.propertySize;
   }

   @Nullable
   public final Long component1() {
      return this.index;
   }

   @Nullable
   public final String component2() {
      return this.label;
   }

   @Nullable
   public final String component3() {
      return this.mediaType;
   }

   @Nullable
   public final String component4() {
      return this.path;
   }

   @Nullable
   public final String component5() {
      return this.propertySize;
   }

   @NotNull
   public final AttachmentArchiveItemReadable copy(@Nullable Long index, @Nullable String label, @Nullable String mediaType, @Nullable String path, @Nullable String propertySize) {
      return new AttachmentArchiveItemReadable(index, label, mediaType, path, propertySize);
   }

   @NotNull
   public String toString() {
      return "AttachmentArchiveItemReadable(index=" + this.index + ", label=" + this.label + ", mediaType=" + this.mediaType + ", path=" + this.path + ", propertySize=" + this.propertySize + ")";
   }

   public int hashCode() {
      int var1 = this.index == null ? 0 : this.index.hashCode();
      var1 = var1 * 31 + (this.label == null ? 0 : this.label.hashCode());
      var1 = var1 * 31 + (this.mediaType == null ? 0 : this.mediaType.hashCode());
      var1 = var1 * 31 + (this.path == null ? 0 : this.path.hashCode());
      var1 = var1 * 31 + (this.propertySize == null ? 0 : this.propertySize.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof AttachmentArchiveItemReadable)) {
         return false;
      } else {
         AttachmentArchiveItemReadable var2 = (AttachmentArchiveItemReadable)other;
         if (!Intrinsics.areEqual(this.index, var2.index)) {
            return false;
         } else if (!Intrinsics.areEqual(this.label, var2.label)) {
            return false;
         } else if (!Intrinsics.areEqual(this.mediaType, var2.mediaType)) {
            return false;
         } else if (!Intrinsics.areEqual(this.path, var2.path)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.propertySize, var2.propertySize);
         }
      }
   }

   public AttachmentArchiveItemReadable() {
      this((Long)null, (String)null, (String)null, (String)null, (String)null, 31, (DefaultConstructorMarker)null);
   }
}
