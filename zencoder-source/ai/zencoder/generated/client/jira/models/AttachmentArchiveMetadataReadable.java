package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BI\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\u000b\u0010\fJ\u0011\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0010J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010\u0019\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0010JP\u0010\u001b\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001\u00a2\u0006\u0002\u0010\u001cJ\u0013\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020!H\u00d6\u0001J\t\u0010\"\u001a\u00020\bH\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0017\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\n\n\u0002\u0010\u0011\u001a\u0004\b\u000f\u0010\u0010R\u0015\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0015\u0010\t\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0013R\u0017\u0010\n\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\n\n\u0002\u0010\u0011\u001a\u0004\b\u0015\u0010\u0010\u00a8\u0006#"},
   d2 = {"Lai/zencoder/generated/client/jira/models/AttachmentArchiveMetadataReadable;", "", "propertyEntries", "", "Lai/zencoder/generated/client/jira/models/AttachmentArchiveItemReadable;", "id", "", "mediaType", "", "NAME", "totalEntryCount", "<init>", "(Ljava/util/List;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;)V", "getPropertyEntries", "()Ljava/util/List;", "getId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getMediaType", "()Ljava/lang/String;", "getNAME", "getTotalEntryCount", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/util/List;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/AttachmentArchiveMetadataReadable;", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class AttachmentArchiveMetadataReadable {
   @Nullable
   private final List<AttachmentArchiveItemReadable> propertyEntries;
   @Nullable
   private final Long id;
   @Nullable
   private final String mediaType;
   @Nullable
   private final String NAME;
   @Nullable
   private final Long totalEntryCount;

   public AttachmentArchiveMetadataReadable(@Nullable List<AttachmentArchiveItemReadable> propertyEntries, @Nullable Long id, @Nullable String mediaType, @Nullable String NAME, @Nullable Long totalEntryCount) {
      this.propertyEntries = propertyEntries;
      this.id = id;
      this.mediaType = mediaType;
      this.NAME = NAME;
      this.totalEntryCount = totalEntryCount;
   }

   @JsonProperty("entries")
   @Nullable
   public final List<AttachmentArchiveItemReadable> getPropertyEntries() {
      return this.propertyEntries;
   }

   @JsonProperty("id")
   @Nullable
   public final Long getId() {
      return this.id;
   }

   @JsonProperty("mediaType")
   @Nullable
   public final String getMediaType() {
      return this.mediaType;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("totalEntryCount")
   @Nullable
   public final Long getTotalEntryCount() {
      return this.totalEntryCount;
   }

   @Nullable
   public final List<AttachmentArchiveItemReadable> component1() {
      return this.propertyEntries;
   }

   @Nullable
   public final Long component2() {
      return this.id;
   }

   @Nullable
   public final String component3() {
      return this.mediaType;
   }

   @Nullable
   public final String component4() {
      return this.NAME;
   }

   @Nullable
   public final Long component5() {
      return this.totalEntryCount;
   }

   @NotNull
   public final AttachmentArchiveMetadataReadable copy(@Nullable List<AttachmentArchiveItemReadable> propertyEntries, @Nullable Long id, @Nullable String mediaType, @Nullable String NAME, @Nullable Long totalEntryCount) {
      return new AttachmentArchiveMetadataReadable(propertyEntries, id, mediaType, NAME, totalEntryCount);
   }

   @NotNull
   public String toString() {
      return "AttachmentArchiveMetadataReadable(propertyEntries=" + this.propertyEntries + ", id=" + this.id + ", mediaType=" + this.mediaType + ", NAME=" + this.NAME + ", totalEntryCount=" + this.totalEntryCount + ")";
   }

   public int hashCode() {
      int var1 = this.propertyEntries == null ? 0 : this.propertyEntries.hashCode();
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      var1 = var1 * 31 + (this.mediaType == null ? 0 : this.mediaType.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.totalEntryCount == null ? 0 : this.totalEntryCount.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof AttachmentArchiveMetadataReadable)) {
         return false;
      } else {
         AttachmentArchiveMetadataReadable var2 = (AttachmentArchiveMetadataReadable)other;
         if (!Intrinsics.areEqual(this.propertyEntries, var2.propertyEntries)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.mediaType, var2.mediaType)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.totalEntryCount, var2.totalEntryCount);
         }
      }
   }

   public AttachmentArchiveMetadataReadable() {
      this((List)null, (Long)null, (String)null, (String)null, (Long)null, 31, (DefaultConstructorMarker)null);
   }
}
