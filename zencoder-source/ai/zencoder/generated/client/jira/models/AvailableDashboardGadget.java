package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\r\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J+\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\t\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/AvailableDashboardGadget;", "", "title", "", "moduleKey", "uri", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getTitle", "()Ljava/lang/String;", "getModuleKey", "getUri", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class AvailableDashboardGadget {
   @NotNull
   private final String title;
   @Nullable
   private final String moduleKey;
   @Nullable
   private final String uri;

   public AvailableDashboardGadget(@NotNull String title, @Nullable String moduleKey, @Nullable String uri) {
      Intrinsics.checkNotNullParameter(title, "");
      super();
      this.title = title;
      this.moduleKey = moduleKey;
      this.uri = uri;
   }

   @JsonProperty("title")
   @NotNull
   public final String getTitle() {
      return this.title;
   }

   @JsonProperty("moduleKey")
   @Nullable
   public final String getModuleKey() {
      return this.moduleKey;
   }

   @JsonProperty("uri")
   @Nullable
   public final String getUri() {
      return this.uri;
   }

   @NotNull
   public final String component1() {
      return this.title;
   }

   @Nullable
   public final String component2() {
      return this.moduleKey;
   }

   @Nullable
   public final String component3() {
      return this.uri;
   }

   @NotNull
   public final AvailableDashboardGadget copy(@NotNull String title, @Nullable String moduleKey, @Nullable String uri) {
      Intrinsics.checkNotNullParameter(title, "");
      return new AvailableDashboardGadget(title, moduleKey, uri);
   }

   @NotNull
   public String toString() {
      return "AvailableDashboardGadget(title=" + this.title + ", moduleKey=" + this.moduleKey + ", uri=" + this.uri + ")";
   }

   public int hashCode() {
      int var1 = this.title.hashCode();
      var1 = var1 * 31 + (this.moduleKey == null ? 0 : this.moduleKey.hashCode());
      var1 = var1 * 31 + (this.uri == null ? 0 : this.uri.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof AvailableDashboardGadget)) {
         return false;
      } else {
         AvailableDashboardGadget var2 = (AvailableDashboardGadget)other;
         if (!Intrinsics.areEqual(this.title, var2.title)) {
            return false;
         } else if (!Intrinsics.areEqual(this.moduleKey, var2.moduleKey)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.uri, var2.uri);
         }
      }
   }
}
