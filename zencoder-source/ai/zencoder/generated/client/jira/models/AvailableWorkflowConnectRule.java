package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0019\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001*Bs\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\r\u0010\u000eJ\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003Ju\u0010#\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010$\u001a\u00020%2\b\u0010&\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010'\u001a\u00020(H\u00d6\u0001J\t\u0010)\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010R\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u0015\u0010\b\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0010R\u0015\u0010\t\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0010R\u0015\u0010\n\u001a\u0004\u0018\u00010\u000b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0015\u0010\f\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0010\u00a8\u0006+"},
   d2 = {"Lai/zencoder/generated/client/jira/models/AvailableWorkflowConnectRule;", "", "addonKey", "", "createUrl", "description", "editUrl", "moduleKey", "NAME", "ruleKey", "ruleType", "Lai/zencoder/generated/client/jira/models/AvailableWorkflowConnectRule$RuleType;", "viewUrl", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/AvailableWorkflowConnectRule$RuleType;Ljava/lang/String;)V", "getAddonKey", "()Ljava/lang/String;", "getCreateUrl", "getDescription", "getEditUrl", "getModuleKey", "getNAME", "getRuleKey", "getRuleType", "()Lai/zencoder/generated/client/jira/models/AvailableWorkflowConnectRule$RuleType;", "getViewUrl", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "RuleType", "zencoder"}
)
public final class AvailableWorkflowConnectRule {
   @Nullable
   private final String addonKey;
   @Nullable
   private final String createUrl;
   @Nullable
   private final String description;
   @Nullable
   private final String editUrl;
   @Nullable
   private final String moduleKey;
   @Nullable
   private final String NAME;
   @Nullable
   private final String ruleKey;
   @Nullable
   private final RuleType ruleType;
   @Nullable
   private final String viewUrl;

   public AvailableWorkflowConnectRule(@Nullable String addonKey, @Nullable String createUrl, @Nullable String description, @Nullable String editUrl, @Nullable String moduleKey, @Nullable String NAME, @Nullable String ruleKey, @Nullable RuleType ruleType, @Nullable String viewUrl) {
      this.addonKey = addonKey;
      this.createUrl = createUrl;
      this.description = description;
      this.editUrl = editUrl;
      this.moduleKey = moduleKey;
      this.NAME = NAME;
      this.ruleKey = ruleKey;
      this.ruleType = ruleType;
      this.viewUrl = viewUrl;
   }

   @JsonProperty("addonKey")
   @Nullable
   public final String getAddonKey() {
      return this.addonKey;
   }

   @JsonProperty("createUrl")
   @Nullable
   public final String getCreateUrl() {
      return this.createUrl;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("editUrl")
   @Nullable
   public final String getEditUrl() {
      return this.editUrl;
   }

   @JsonProperty("moduleKey")
   @Nullable
   public final String getModuleKey() {
      return this.moduleKey;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("ruleKey")
   @Nullable
   public final String getRuleKey() {
      return this.ruleKey;
   }

   @JsonProperty("ruleType")
   @Nullable
   public final RuleType getRuleType() {
      return this.ruleType;
   }

   @JsonProperty("viewUrl")
   @Nullable
   public final String getViewUrl() {
      return this.viewUrl;
   }

   @Nullable
   public final String component1() {
      return this.addonKey;
   }

   @Nullable
   public final String component2() {
      return this.createUrl;
   }

   @Nullable
   public final String component3() {
      return this.description;
   }

   @Nullable
   public final String component4() {
      return this.editUrl;
   }

   @Nullable
   public final String component5() {
      return this.moduleKey;
   }

   @Nullable
   public final String component6() {
      return this.NAME;
   }

   @Nullable
   public final String component7() {
      return this.ruleKey;
   }

   @Nullable
   public final RuleType component8() {
      return this.ruleType;
   }

   @Nullable
   public final String component9() {
      return this.viewUrl;
   }

   @NotNull
   public final AvailableWorkflowConnectRule copy(@Nullable String addonKey, @Nullable String createUrl, @Nullable String description, @Nullable String editUrl, @Nullable String moduleKey, @Nullable String NAME, @Nullable String ruleKey, @Nullable RuleType ruleType, @Nullable String viewUrl) {
      return new AvailableWorkflowConnectRule(addonKey, createUrl, description, editUrl, moduleKey, NAME, ruleKey, ruleType, viewUrl);
   }

   @NotNull
   public String toString() {
      return "AvailableWorkflowConnectRule(addonKey=" + this.addonKey + ", createUrl=" + this.createUrl + ", description=" + this.description + ", editUrl=" + this.editUrl + ", moduleKey=" + this.moduleKey + ", NAME=" + this.NAME + ", ruleKey=" + this.ruleKey + ", ruleType=" + this.ruleType + ", viewUrl=" + this.viewUrl + ")";
   }

   public int hashCode() {
      int var1 = this.addonKey == null ? 0 : this.addonKey.hashCode();
      var1 = var1 * 31 + (this.createUrl == null ? 0 : this.createUrl.hashCode());
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.editUrl == null ? 0 : this.editUrl.hashCode());
      var1 = var1 * 31 + (this.moduleKey == null ? 0 : this.moduleKey.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.ruleKey == null ? 0 : this.ruleKey.hashCode());
      var1 = var1 * 31 + (this.ruleType == null ? 0 : this.ruleType.hashCode());
      var1 = var1 * 31 + (this.viewUrl == null ? 0 : this.viewUrl.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof AvailableWorkflowConnectRule)) {
         return false;
      } else {
         AvailableWorkflowConnectRule var2 = (AvailableWorkflowConnectRule)other;
         if (!Intrinsics.areEqual(this.addonKey, var2.addonKey)) {
            return false;
         } else if (!Intrinsics.areEqual(this.createUrl, var2.createUrl)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.editUrl, var2.editUrl)) {
            return false;
         } else if (!Intrinsics.areEqual(this.moduleKey, var2.moduleKey)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else if (!Intrinsics.areEqual(this.ruleKey, var2.ruleKey)) {
            return false;
         } else if (this.ruleType != var2.ruleType) {
            return false;
         } else {
            return Intrinsics.areEqual(this.viewUrl, var2.viewUrl);
         }
      }
   }

   public AvailableWorkflowConnectRule() {
      this((String)null, (String)null, (String)null, (String)null, (String)null, (String)null, (String)null, (RuleType)null, (String)null, 511, (DefaultConstructorMarker)null);
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"},
      d2 = {"Lai/zencoder/generated/client/jira/models/AvailableWorkflowConnectRule$RuleType;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "Condition", "Validator", "Function", "Screen", "zencoder"}
   )
   public static enum RuleType {
      @NotNull
      private final String value;
      @JsonProperty("Condition")
      Condition("Condition"),
      @JsonProperty("Validator")
      Validator("Validator"),
      @JsonProperty("Function")
      Function("Function"),
      @JsonProperty("Screen")
      Screen("Screen");

      private RuleType(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<RuleType> getEntries() {
         return $ENTRIES;
      }
   }
}
