package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u001eBC\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\u0004\b\t\u0010\nJ\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\bH\u00c6\u0003JE\u0010\u0017\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\bH\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\fR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\fR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u0006\u001f"},
   d2 = {"Lai/zencoder/generated/client/jira/models/AvailableWorkflowForgeRule;", "", "description", "", "id", "NAME", "ruleKey", "ruleType", "Lai/zencoder/generated/client/jira/models/AvailableWorkflowForgeRule$RuleType;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/AvailableWorkflowForgeRule$RuleType;)V", "getDescription", "()Ljava/lang/String;", "getId", "getNAME", "getRuleKey", "getRuleType", "()Lai/zencoder/generated/client/jira/models/AvailableWorkflowForgeRule$RuleType;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "RuleType", "zencoder"}
)
public final class AvailableWorkflowForgeRule {
   @Nullable
   private final String description;
   @Nullable
   private final String id;
   @Nullable
   private final String NAME;
   @Nullable
   private final String ruleKey;
   @Nullable
   private final RuleType ruleType;

   public AvailableWorkflowForgeRule(@Nullable String description, @Nullable String id, @Nullable String NAME, @Nullable String ruleKey, @Nullable RuleType ruleType) {
      this.description = description;
      this.id = id;
      this.NAME = NAME;
      this.ruleKey = ruleKey;
      this.ruleType = ruleType;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("id")
   @Nullable
   public final String getId() {
      return this.id;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("ruleKey")
   @Nullable
   public final String getRuleKey() {
      return this.ruleKey;
   }

   @JsonProperty("ruleType")
   @Nullable
   public final RuleType getRuleType() {
      return this.ruleType;
   }

   @Nullable
   public final String component1() {
      return this.description;
   }

   @Nullable
   public final String component2() {
      return this.id;
   }

   @Nullable
   public final String component3() {
      return this.NAME;
   }

   @Nullable
   public final String component4() {
      return this.ruleKey;
   }

   @Nullable
   public final RuleType component5() {
      return this.ruleType;
   }

   @NotNull
   public final AvailableWorkflowForgeRule copy(@Nullable String description, @Nullable String id, @Nullable String NAME, @Nullable String ruleKey, @Nullable RuleType ruleType) {
      return new AvailableWorkflowForgeRule(description, id, NAME, ruleKey, ruleType);
   }

   @NotNull
   public String toString() {
      return "AvailableWorkflowForgeRule(description=" + this.description + ", id=" + this.id + ", NAME=" + this.NAME + ", ruleKey=" + this.ruleKey + ", ruleType=" + this.ruleType + ")";
   }

   public int hashCode() {
      int var1 = this.description == null ? 0 : this.description.hashCode();
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.ruleKey == null ? 0 : this.ruleKey.hashCode());
      var1 = var1 * 31 + (this.ruleType == null ? 0 : this.ruleType.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof AvailableWorkflowForgeRule)) {
         return false;
      } else {
         AvailableWorkflowForgeRule var2 = (AvailableWorkflowForgeRule)other;
         if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else if (!Intrinsics.areEqual(this.ruleKey, var2.ruleKey)) {
            return false;
         } else {
            return this.ruleType == var2.ruleType;
         }
      }
   }

   public AvailableWorkflowForgeRule() {
      this((String)null, (String)null, (String)null, (String)null, (RuleType)null, 31, (DefaultConstructorMarker)null);
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"},
      d2 = {"Lai/zencoder/generated/client/jira/models/AvailableWorkflowForgeRule$RuleType;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "Condition", "Validator", "Function", "Screen", "zencoder"}
   )
   public static enum RuleType {
      @NotNull
      private final String value;
      @JsonProperty("Condition")
      Condition("Condition"),
      @JsonProperty("Validator")
      Validator("Validator"),
      @JsonProperty("Function")
      Function("Function"),
      @JsonProperty("Screen")
      Screen("Screen");

      private RuleType(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<RuleType> getEntries() {
         return $ENTRIES;
      }
   }
}
