package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001%BI\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0003\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\r\u0010\u000eJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u000bH\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003JW\u0010\u001f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010 \u001a\u00020\u00072\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\"\u001a\u00020#H\u00d6\u0001J\t\u0010$\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0013\u0010\u0006\u001a\u00020\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0013R\u0013\u0010\b\u001a\u00020\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0013R\u0013\u0010\t\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u0013\u0010\n\u001a\u00020\u000b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0015\u0010\f\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0010\u00a8\u0006&"},
   d2 = {"Lai/zencoder/generated/client/jira/models/AvailableWorkflowSystemRule;", "", "description", "", "incompatibleRuleKeys", "", "isAvailableForInitialTransition", "", "isVisible", "ruleKey", "ruleType", "Lai/zencoder/generated/client/jira/models/AvailableWorkflowSystemRule$RuleType;", "NAME", "<init>", "(Ljava/lang/String;Ljava/util/List;ZZLjava/lang/String;Lai/zencoder/generated/client/jira/models/AvailableWorkflowSystemRule$RuleType;Ljava/lang/String;)V", "getDescription", "()Ljava/lang/String;", "getIncompatibleRuleKeys", "()Ljava/util/List;", "()Z", "getRuleKey", "getRuleType", "()Lai/zencoder/generated/client/jira/models/AvailableWorkflowSystemRule$RuleType;", "getNAME", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "RuleType", "zencoder"}
)
public final class AvailableWorkflowSystemRule {
   @NotNull
   private final String description;
   @NotNull
   private final List<String> incompatibleRuleKeys;
   private final boolean isAvailableForInitialTransition;
   private final boolean isVisible;
   @NotNull
   private final String ruleKey;
   @NotNull
   private final RuleType ruleType;
   @Nullable
   private final String NAME;

   public AvailableWorkflowSystemRule(@NotNull String description, @NotNull List<String> incompatibleRuleKeys, boolean isAvailableForInitialTransition, boolean isVisible, @NotNull String ruleKey, @NotNull RuleType ruleType, @Nullable String NAME) {
      Intrinsics.checkNotNullParameter(description, "");
      Intrinsics.checkNotNullParameter(incompatibleRuleKeys, "");
      Intrinsics.checkNotNullParameter(ruleKey, "");
      Intrinsics.checkNotNullParameter(ruleType, "");
      super();
      this.description = description;
      this.incompatibleRuleKeys = incompatibleRuleKeys;
      this.isAvailableForInitialTransition = isAvailableForInitialTransition;
      this.isVisible = isVisible;
      this.ruleKey = ruleKey;
      this.ruleType = ruleType;
      this.NAME = NAME;
   }

   @JsonProperty("description")
   @NotNull
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("incompatibleRuleKeys")
   @NotNull
   public final List<String> getIncompatibleRuleKeys() {
      return this.incompatibleRuleKeys;
   }

   @JsonProperty("isAvailableForInitialTransition")
   public final boolean isAvailableForInitialTransition() {
      return this.isAvailableForInitialTransition;
   }

   @JsonProperty("isVisible")
   public final boolean isVisible() {
      return this.isVisible;
   }

   @JsonProperty("ruleKey")
   @NotNull
   public final String getRuleKey() {
      return this.ruleKey;
   }

   @JsonProperty("ruleType")
   @NotNull
   public final RuleType getRuleType() {
      return this.ruleType;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @NotNull
   public final String component1() {
      return this.description;
   }

   @NotNull
   public final List<String> component2() {
      return this.incompatibleRuleKeys;
   }

   public final boolean component3() {
      return this.isAvailableForInitialTransition;
   }

   public final boolean component4() {
      return this.isVisible;
   }

   @NotNull
   public final String component5() {
      return this.ruleKey;
   }

   @NotNull
   public final RuleType component6() {
      return this.ruleType;
   }

   @Nullable
   public final String component7() {
      return this.NAME;
   }

   @NotNull
   public final AvailableWorkflowSystemRule copy(@NotNull String description, @NotNull List<String> incompatibleRuleKeys, boolean isAvailableForInitialTransition, boolean isVisible, @NotNull String ruleKey, @NotNull RuleType ruleType, @Nullable String NAME) {
      Intrinsics.checkNotNullParameter(description, "");
      Intrinsics.checkNotNullParameter(incompatibleRuleKeys, "");
      Intrinsics.checkNotNullParameter(ruleKey, "");
      Intrinsics.checkNotNullParameter(ruleType, "");
      return new AvailableWorkflowSystemRule(description, incompatibleRuleKeys, isAvailableForInitialTransition, isVisible, ruleKey, ruleType, NAME);
   }

   @NotNull
   public String toString() {
      return "AvailableWorkflowSystemRule(description=" + this.description + ", incompatibleRuleKeys=" + this.incompatibleRuleKeys + ", isAvailableForInitialTransition=" + this.isAvailableForInitialTransition + ", isVisible=" + this.isVisible + ", ruleKey=" + this.ruleKey + ", ruleType=" + this.ruleType + ", NAME=" + this.NAME + ")";
   }

   public int hashCode() {
      int var1 = this.description.hashCode();
      var1 = var1 * 31 + this.incompatibleRuleKeys.hashCode();
      var1 = var1 * 31 + Boolean.hashCode(this.isAvailableForInitialTransition);
      var1 = var1 * 31 + Boolean.hashCode(this.isVisible);
      var1 = var1 * 31 + this.ruleKey.hashCode();
      var1 = var1 * 31 + this.ruleType.hashCode();
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof AvailableWorkflowSystemRule)) {
         return false;
      } else {
         AvailableWorkflowSystemRule var2 = (AvailableWorkflowSystemRule)other;
         if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.incompatibleRuleKeys, var2.incompatibleRuleKeys)) {
            return false;
         } else if (this.isAvailableForInitialTransition != var2.isAvailableForInitialTransition) {
            return false;
         } else if (this.isVisible != var2.isVisible) {
            return false;
         } else if (!Intrinsics.areEqual(this.ruleKey, var2.ruleKey)) {
            return false;
         } else if (this.ruleType != var2.ruleType) {
            return false;
         } else {
            return Intrinsics.areEqual(this.NAME, var2.NAME);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"},
      d2 = {"Lai/zencoder/generated/client/jira/models/AvailableWorkflowSystemRule$RuleType;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "Condition", "Validator", "Function", "Screen", "zencoder"}
   )
   public static enum RuleType {
      @NotNull
      private final String value;
      @JsonProperty("Condition")
      Condition("Condition"),
      @JsonProperty("Validator")
      Validator("Validator"),
      @JsonProperty("Function")
      Function("Function"),
      @JsonProperty("Screen")
      Screen("Screen");

      private RuleType(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<RuleType> getEntries() {
         return $ENTRIES;
      }
   }
}
