package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\"\n\u0000\n\u0002\u0010\b\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B?\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\b\n\u0010\u000bJ\u000f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u0011\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0006H\u00c6\u0003J\u0010\u0010\u0017\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0011J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003JH\u0010\u0019\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0004H\u00c6\u0001\u00a2\u0006\u0002\u0010\u001aJ\u0013\u0010\u001b\u001a\u00020\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001e\u001a\u00020\bH\u00d6\u0001J\t\u0010\u001f\u001a\u00020\u0004H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0010\u0010\u0011R\u0015\u0010\t\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006 "},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkChangelogRequestBean;", "", "issueIdsOrKeys", "", "", "fieldIds", "", "maxResults", "", "nextPageToken", "<init>", "(Ljava/util/List;Ljava/util/Set;Ljava/lang/Integer;Ljava/lang/String;)V", "getIssueIdsOrKeys", "()Ljava/util/List;", "getFieldIds", "()Ljava/util/Set;", "getMaxResults", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getNextPageToken", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "copy", "(Ljava/util/List;Ljava/util/Set;Ljava/lang/Integer;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/BulkChangelogRequestBean;", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class BulkChangelogRequestBean {
   @NotNull
   private final List<String> issueIdsOrKeys;
   @Nullable
   private final Set<String> fieldIds;
   @Nullable
   private final Integer maxResults;
   @Nullable
   private final String nextPageToken;

   public BulkChangelogRequestBean(@NotNull List<String> issueIdsOrKeys, @Nullable Set<String> fieldIds, @Nullable Integer maxResults, @Nullable String nextPageToken) {
      Intrinsics.checkNotNullParameter(issueIdsOrKeys, "");
      super();
      this.issueIdsOrKeys = issueIdsOrKeys;
      this.fieldIds = fieldIds;
      this.maxResults = maxResults;
      this.nextPageToken = nextPageToken;
   }

   @JsonProperty("issueIdsOrKeys")
   @NotNull
   public final List<String> getIssueIdsOrKeys() {
      return this.issueIdsOrKeys;
   }

   @JsonProperty("fieldIds")
   @Nullable
   public final Set<String> getFieldIds() {
      return this.fieldIds;
   }

   @JsonProperty("maxResults")
   @Nullable
   public final Integer getMaxResults() {
      return this.maxResults;
   }

   @JsonProperty("nextPageToken")
   @Nullable
   public final String getNextPageToken() {
      return this.nextPageToken;
   }

   @NotNull
   public final List<String> component1() {
      return this.issueIdsOrKeys;
   }

   @Nullable
   public final Set<String> component2() {
      return this.fieldIds;
   }

   @Nullable
   public final Integer component3() {
      return this.maxResults;
   }

   @Nullable
   public final String component4() {
      return this.nextPageToken;
   }

   @NotNull
   public final BulkChangelogRequestBean copy(@NotNull List<String> issueIdsOrKeys, @Nullable Set<String> fieldIds, @Nullable Integer maxResults, @Nullable String nextPageToken) {
      Intrinsics.checkNotNullParameter(issueIdsOrKeys, "");
      return new BulkChangelogRequestBean(issueIdsOrKeys, fieldIds, maxResults, nextPageToken);
   }

   @NotNull
   public String toString() {
      return "BulkChangelogRequestBean(issueIdsOrKeys=" + this.issueIdsOrKeys + ", fieldIds=" + this.fieldIds + ", maxResults=" + this.maxResults + ", nextPageToken=" + this.nextPageToken + ")";
   }

   public int hashCode() {
      int var1 = this.issueIdsOrKeys.hashCode();
      var1 = var1 * 31 + (this.fieldIds == null ? 0 : this.fieldIds.hashCode());
      var1 = var1 * 31 + (this.maxResults == null ? 0 : this.maxResults.hashCode());
      var1 = var1 * 31 + (this.nextPageToken == null ? 0 : this.nextPageToken.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkChangelogRequestBean)) {
         return false;
      } else {
         BulkChangelogRequestBean var2 = (BulkChangelogRequestBean)other;
         if (!Intrinsics.areEqual(this.issueIdsOrKeys, var2.issueIdsOrKeys)) {
            return false;
         } else if (!Intrinsics.areEqual(this.fieldIds, var2.fieldIds)) {
            return false;
         } else if (!Intrinsics.areEqual(this.maxResults, var2.maxResults)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.nextPageToken, var2.nextPageToken);
         }
      }
   }
}
