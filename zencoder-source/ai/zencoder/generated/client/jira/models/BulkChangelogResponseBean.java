package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0011\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J'\u0010\u000f\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0006H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkChangelogResponseBean;", "", "issueChangeLogs", "", "Lai/zencoder/generated/client/jira/models/IssueChangeLog;", "nextPageToken", "", "<init>", "(Ljava/util/List;Ljava/lang/String;)V", "getIssueChangeLogs", "()Ljava/util/List;", "getNextPageToken", "()Ljava/lang/String;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class BulkChangelogResponseBean {
   @Nullable
   private final List<IssueChangeLog> issueChangeLogs;
   @Nullable
   private final String nextPageToken;

   public BulkChangelogResponseBean(@Nullable List<IssueChangeLog> issueChangeLogs, @Nullable String nextPageToken) {
      this.issueChangeLogs = issueChangeLogs;
      this.nextPageToken = nextPageToken;
   }

   @JsonProperty("issueChangeLogs")
   @Nullable
   public final List<IssueChangeLog> getIssueChangeLogs() {
      return this.issueChangeLogs;
   }

   @JsonProperty("nextPageToken")
   @Nullable
   public final String getNextPageToken() {
      return this.nextPageToken;
   }

   @Nullable
   public final List<IssueChangeLog> component1() {
      return this.issueChangeLogs;
   }

   @Nullable
   public final String component2() {
      return this.nextPageToken;
   }

   @NotNull
   public final BulkChangelogResponseBean copy(@Nullable List<IssueChangeLog> issueChangeLogs, @Nullable String nextPageToken) {
      return new BulkChangelogResponseBean(issueChangeLogs, nextPageToken);
   }

   @NotNull
   public String toString() {
      return "BulkChangelogResponseBean(issueChangeLogs=" + this.issueChangeLogs + ", nextPageToken=" + this.nextPageToken + ")";
   }

   public int hashCode() {
      int var1 = this.issueChangeLogs == null ? 0 : this.issueChangeLogs.hashCode();
      var1 = var1 * 31 + (this.nextPageToken == null ? 0 : this.nextPageToken.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkChangelogResponseBean)) {
         return false;
      } else {
         BulkChangelogResponseBean var2 = (BulkChangelogResponseBean)other;
         if (!Intrinsics.areEqual(this.issueChangeLogs, var2.issueChangeLogs)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.nextPageToken, var2.nextPageToken);
         }
      }
   }

   public BulkChangelogResponseBean() {
      this((List)null, (String)null, 3, (DefaultConstructorMarker)null);
   }
}
