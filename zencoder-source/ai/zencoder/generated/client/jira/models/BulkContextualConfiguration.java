package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0014\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0001\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0001\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J?\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0001H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0013\u0010\u0005\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000bR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000f\u00a8\u0006\u001d"},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkContextualConfiguration;", "", "customFieldId", "", "fieldContextId", "id", "configuration", "schema", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V", "getCustomFieldId", "()Ljava/lang/String;", "getFieldContextId", "getId", "getConfiguration", "()Ljava/lang/Object;", "getSchema", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class BulkContextualConfiguration {
   @NotNull
   private final String customFieldId;
   @NotNull
   private final String fieldContextId;
   @NotNull
   private final String id;
   @Nullable
   private final Object configuration;
   @Nullable
   private final Object schema;

   public BulkContextualConfiguration(@NotNull String customFieldId, @NotNull String fieldContextId, @NotNull String id, @Nullable Object configuration, @Nullable Object schema) {
      Intrinsics.checkNotNullParameter(customFieldId, "");
      Intrinsics.checkNotNullParameter(fieldContextId, "");
      Intrinsics.checkNotNullParameter(id, "");
      super();
      this.customFieldId = customFieldId;
      this.fieldContextId = fieldContextId;
      this.id = id;
      this.configuration = configuration;
      this.schema = schema;
   }

   @JsonProperty("customFieldId")
   @NotNull
   public final String getCustomFieldId() {
      return this.customFieldId;
   }

   @JsonProperty("fieldContextId")
   @NotNull
   public final String getFieldContextId() {
      return this.fieldContextId;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("configuration")
   @Nullable
   public final Object getConfiguration() {
      return this.configuration;
   }

   @JsonProperty("schema")
   @Nullable
   public final Object getSchema() {
      return this.schema;
   }

   @NotNull
   public final String component1() {
      return this.customFieldId;
   }

   @NotNull
   public final String component2() {
      return this.fieldContextId;
   }

   @NotNull
   public final String component3() {
      return this.id;
   }

   @Nullable
   public final Object component4() {
      return this.configuration;
   }

   @Nullable
   public final Object component5() {
      return this.schema;
   }

   @NotNull
   public final BulkContextualConfiguration copy(@NotNull String customFieldId, @NotNull String fieldContextId, @NotNull String id, @Nullable Object configuration, @Nullable Object schema) {
      Intrinsics.checkNotNullParameter(customFieldId, "");
      Intrinsics.checkNotNullParameter(fieldContextId, "");
      Intrinsics.checkNotNullParameter(id, "");
      return new BulkContextualConfiguration(customFieldId, fieldContextId, id, configuration, schema);
   }

   @NotNull
   public String toString() {
      return "BulkContextualConfiguration(customFieldId=" + this.customFieldId + ", fieldContextId=" + this.fieldContextId + ", id=" + this.id + ", configuration=" + this.configuration + ", schema=" + this.schema + ")";
   }

   public int hashCode() {
      int var1 = this.customFieldId.hashCode();
      var1 = var1 * 31 + this.fieldContextId.hashCode();
      var1 = var1 * 31 + this.id.hashCode();
      var1 = var1 * 31 + (this.configuration == null ? 0 : this.configuration.hashCode());
      var1 = var1 * 31 + (this.schema == null ? 0 : this.schema.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkContextualConfiguration)) {
         return false;
      } else {
         BulkContextualConfiguration var2 = (BulkContextualConfiguration)other;
         if (!Intrinsics.areEqual(this.customFieldId, var2.customFieldId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.fieldContextId, var2.fieldContextId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.configuration, var2.configuration)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.schema, var2.schema);
         }
      }
   }
}
