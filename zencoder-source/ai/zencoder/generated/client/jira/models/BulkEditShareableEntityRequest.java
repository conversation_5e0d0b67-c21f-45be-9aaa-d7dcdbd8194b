package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\"\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001:\u0001'BA\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\u0004\b\r\u0010\u000eJ\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u0010\u0010\u001d\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0016J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\fH\u00c6\u0003JL\u0010\u001f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00c6\u0001\u00a2\u0006\u0002\u0010 J\u0013\u0010!\u001a\u00020\n2\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020$H\u00d6\u0001J\t\u0010%\u001a\u00020&H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0015\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0017\u0010\t\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\n\n\u0002\u0010\u0017\u001a\u0004\b\u0015\u0010\u0016R\u0015\u0010\u000b\u001a\u0004\u0018\u00010\f8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019\u00a8\u0006("},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityRequest;", "", "action", "Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityRequest$Action;", "entityIds", "", "", "changeOwnerDetails", "Lai/zencoder/generated/client/jira/models/BulkChangeOwnerDetails;", "extendAdminPermissions", "", "permissionDetails", "Lai/zencoder/generated/client/jira/models/PermissionDetails;", "<init>", "(Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityRequest$Action;Ljava/util/Set;Lai/zencoder/generated/client/jira/models/BulkChangeOwnerDetails;Ljava/lang/Boolean;Lai/zencoder/generated/client/jira/models/PermissionDetails;)V", "getAction", "()Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityRequest$Action;", "getEntityIds", "()Ljava/util/Set;", "getChangeOwnerDetails", "()Lai/zencoder/generated/client/jira/models/BulkChangeOwnerDetails;", "getExtendAdminPermissions", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getPermissionDetails", "()Lai/zencoder/generated/client/jira/models/PermissionDetails;", "component1", "component2", "component3", "component4", "component5", "copy", "(Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityRequest$Action;Ljava/util/Set;Lai/zencoder/generated/client/jira/models/BulkChangeOwnerDetails;Ljava/lang/Boolean;Lai/zencoder/generated/client/jira/models/PermissionDetails;)Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityRequest;", "equals", "other", "hashCode", "", "toString", "", "Action", "zencoder"}
)
public final class BulkEditShareableEntityRequest {
   @NotNull
   private final Action action;
   @NotNull
   private final Set<Long> entityIds;
   @Nullable
   private final BulkChangeOwnerDetails changeOwnerDetails;
   @Nullable
   private final Boolean extendAdminPermissions;
   @Nullable
   private final PermissionDetails permissionDetails;

   public BulkEditShareableEntityRequest(@NotNull Action action, @NotNull Set<Long> entityIds, @Nullable BulkChangeOwnerDetails changeOwnerDetails, @Nullable Boolean extendAdminPermissions, @Nullable PermissionDetails permissionDetails) {
      Intrinsics.checkNotNullParameter(action, "");
      Intrinsics.checkNotNullParameter(entityIds, "");
      super();
      this.action = action;
      this.entityIds = entityIds;
      this.changeOwnerDetails = changeOwnerDetails;
      this.extendAdminPermissions = extendAdminPermissions;
      this.permissionDetails = permissionDetails;
   }

   @JsonProperty("action")
   @NotNull
   public final Action getAction() {
      return this.action;
   }

   @JsonProperty("entityIds")
   @NotNull
   public final Set<Long> getEntityIds() {
      return this.entityIds;
   }

   @JsonProperty("changeOwnerDetails")
   @Nullable
   public final BulkChangeOwnerDetails getChangeOwnerDetails() {
      return this.changeOwnerDetails;
   }

   @JsonProperty("extendAdminPermissions")
   @Nullable
   public final Boolean getExtendAdminPermissions() {
      return this.extendAdminPermissions;
   }

   @JsonProperty("permissionDetails")
   @Nullable
   public final PermissionDetails getPermissionDetails() {
      return this.permissionDetails;
   }

   @NotNull
   public final Action component1() {
      return this.action;
   }

   @NotNull
   public final Set<Long> component2() {
      return this.entityIds;
   }

   @Nullable
   public final BulkChangeOwnerDetails component3() {
      return this.changeOwnerDetails;
   }

   @Nullable
   public final Boolean component4() {
      return this.extendAdminPermissions;
   }

   @Nullable
   public final PermissionDetails component5() {
      return this.permissionDetails;
   }

   @NotNull
   public final BulkEditShareableEntityRequest copy(@NotNull Action action, @NotNull Set<Long> entityIds, @Nullable BulkChangeOwnerDetails changeOwnerDetails, @Nullable Boolean extendAdminPermissions, @Nullable PermissionDetails permissionDetails) {
      Intrinsics.checkNotNullParameter(action, "");
      Intrinsics.checkNotNullParameter(entityIds, "");
      return new BulkEditShareableEntityRequest(action, entityIds, changeOwnerDetails, extendAdminPermissions, permissionDetails);
   }

   @NotNull
   public String toString() {
      return "BulkEditShareableEntityRequest(action=" + this.action + ", entityIds=" + this.entityIds + ", changeOwnerDetails=" + this.changeOwnerDetails + ", extendAdminPermissions=" + this.extendAdminPermissions + ", permissionDetails=" + this.permissionDetails + ")";
   }

   public int hashCode() {
      int var1 = this.action.hashCode();
      var1 = var1 * 31 + this.entityIds.hashCode();
      var1 = var1 * 31 + (this.changeOwnerDetails == null ? 0 : this.changeOwnerDetails.hashCode());
      var1 = var1 * 31 + (this.extendAdminPermissions == null ? 0 : this.extendAdminPermissions.hashCode());
      var1 = var1 * 31 + (this.permissionDetails == null ? 0 : this.permissionDetails.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkEditShareableEntityRequest)) {
         return false;
      } else {
         BulkEditShareableEntityRequest var2 = (BulkEditShareableEntityRequest)other;
         if (this.action != var2.action) {
            return false;
         } else if (!Intrinsics.areEqual(this.entityIds, var2.entityIds)) {
            return false;
         } else if (!Intrinsics.areEqual(this.changeOwnerDetails, var2.changeOwnerDetails)) {
            return false;
         } else if (!Intrinsics.areEqual(this.extendAdminPermissions, var2.extendAdminPermissions)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.permissionDetails, var2.permissionDetails);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"},
      d2 = {"Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityRequest$Action;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "changeOwner", "changePermission", "addPermission", "removePermission", "zencoder"}
   )
   public static enum Action {
      @NotNull
      private final String value;
      @JsonProperty("changeOwner")
      changeOwner("changeOwner"),
      @JsonProperty("changePermission")
      changePermission("changePermission"),
      @JsonProperty("addPermission")
      addPermission("addPermission"),
      @JsonProperty("removePermission")
      removePermission("removePermission");

      private Action(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Action> getEntries() {
         return $ENTRIES;
      }
   }
}
