package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u0017B'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0016\b\u0002\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0005\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\u0017\u0010\u000f\u001a\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0005H\u00c6\u0003J+\u0010\u0010\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0016\b\u0002\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0006H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR!\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityResponse;", "", "action", "Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityResponse$Action;", "entityErrors", "", "", "Lai/zencoder/generated/client/jira/models/BulkEditActionError;", "<init>", "(Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityResponse$Action;Ljava/util/Map;)V", "getAction", "()Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityResponse$Action;", "getEntityErrors", "()Ljava/util/Map;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "Action", "zencoder"}
)
public final class BulkEditShareableEntityResponse {
   @NotNull
   private final Action action;
   @Nullable
   private final Map<String, BulkEditActionError> entityErrors;

   public BulkEditShareableEntityResponse(@NotNull Action action, @Nullable Map<String, BulkEditActionError> entityErrors) {
      Intrinsics.checkNotNullParameter(action, "");
      super();
      this.action = action;
      this.entityErrors = entityErrors;
   }

   @JsonProperty("action")
   @NotNull
   public final Action getAction() {
      return this.action;
   }

   @JsonProperty("entityErrors")
   @Nullable
   public final Map<String, BulkEditActionError> getEntityErrors() {
      return this.entityErrors;
   }

   @NotNull
   public final Action component1() {
      return this.action;
   }

   @Nullable
   public final Map<String, BulkEditActionError> component2() {
      return this.entityErrors;
   }

   @NotNull
   public final BulkEditShareableEntityResponse copy(@NotNull Action action, @Nullable Map<String, BulkEditActionError> entityErrors) {
      Intrinsics.checkNotNullParameter(action, "");
      return new BulkEditShareableEntityResponse(action, entityErrors);
   }

   @NotNull
   public String toString() {
      return "BulkEditShareableEntityResponse(action=" + this.action + ", entityErrors=" + this.entityErrors + ")";
   }

   public int hashCode() {
      int var1 = this.action.hashCode();
      var1 = var1 * 31 + (this.entityErrors == null ? 0 : this.entityErrors.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkEditShareableEntityResponse)) {
         return false;
      } else {
         BulkEditShareableEntityResponse var2 = (BulkEditShareableEntityResponse)other;
         if (this.action != var2.action) {
            return false;
         } else {
            return Intrinsics.areEqual(this.entityErrors, var2.entityErrors);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"},
      d2 = {"Lai/zencoder/generated/client/jira/models/BulkEditShareableEntityResponse$Action;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "changeOwner", "changePermission", "addPermission", "removePermission", "zencoder"}
   )
   public static enum Action {
      @NotNull
      private final String value;
      @JsonProperty("changeOwner")
      changeOwner("changeOwner"),
      @JsonProperty("changePermission")
      changePermission("changePermission"),
      @JsonProperty("addPermission")
      addPermission("addPermission"),
      @JsonProperty("removePermission")
      removePermission("removePermission");

      private Action(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Action> getEntries() {
         return $ENTRIES;
      }
   }
}
