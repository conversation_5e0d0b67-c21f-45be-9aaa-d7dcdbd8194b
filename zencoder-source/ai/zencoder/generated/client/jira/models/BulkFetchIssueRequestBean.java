package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0015\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BW\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\n\u0010\u000bJ\u000f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u0011\u0010\u0015\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0017\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0011J\u0011\u0010\u0018\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J`\u0010\u0019\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010\u001aJ\u0013\u0010\u001b\u001a\u00020\b2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001d\u001a\u00020\u001eH\u00d6\u0001J\t\u0010\u001f\u001a\u00020\u0004H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u001b\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\rR\u0017\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0010\u0010\u0011R\u001b\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\r\u00a8\u0006 "},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkFetchIssueRequestBean;", "", "issueIdsOrKeys", "", "", "expand", "fields", "fieldsByKeys", "", "properties", "<init>", "(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/lang/Boolean;Ljava/util/List;)V", "getIssueIdsOrKeys", "()Ljava/util/List;", "getExpand", "getFields", "getFieldsByKeys", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getProperties", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/lang/Boolean;Ljava/util/List;)Lai/zencoder/generated/client/jira/models/BulkFetchIssueRequestBean;", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class BulkFetchIssueRequestBean {
   @NotNull
   private final List<String> issueIdsOrKeys;
   @Nullable
   private final List<String> expand;
   @Nullable
   private final List<String> fields;
   @Nullable
   private final Boolean fieldsByKeys;
   @Nullable
   private final List<String> properties;

   public BulkFetchIssueRequestBean(@NotNull List<String> issueIdsOrKeys, @Nullable List<String> expand, @Nullable List<String> fields, @Nullable Boolean fieldsByKeys, @Nullable List<String> properties) {
      Intrinsics.checkNotNullParameter(issueIdsOrKeys, "");
      super();
      this.issueIdsOrKeys = issueIdsOrKeys;
      this.expand = expand;
      this.fields = fields;
      this.fieldsByKeys = fieldsByKeys;
      this.properties = properties;
   }

   @JsonProperty("issueIdsOrKeys")
   @NotNull
   public final List<String> getIssueIdsOrKeys() {
      return this.issueIdsOrKeys;
   }

   @JsonProperty("expand")
   @Nullable
   public final List<String> getExpand() {
      return this.expand;
   }

   @JsonProperty("fields")
   @Nullable
   public final List<String> getFields() {
      return this.fields;
   }

   @JsonProperty("fieldsByKeys")
   @Nullable
   public final Boolean getFieldsByKeys() {
      return this.fieldsByKeys;
   }

   @JsonProperty("properties")
   @Nullable
   public final List<String> getProperties() {
      return this.properties;
   }

   @NotNull
   public final List<String> component1() {
      return this.issueIdsOrKeys;
   }

   @Nullable
   public final List<String> component2() {
      return this.expand;
   }

   @Nullable
   public final List<String> component3() {
      return this.fields;
   }

   @Nullable
   public final Boolean component4() {
      return this.fieldsByKeys;
   }

   @Nullable
   public final List<String> component5() {
      return this.properties;
   }

   @NotNull
   public final BulkFetchIssueRequestBean copy(@NotNull List<String> issueIdsOrKeys, @Nullable List<String> expand, @Nullable List<String> fields, @Nullable Boolean fieldsByKeys, @Nullable List<String> properties) {
      Intrinsics.checkNotNullParameter(issueIdsOrKeys, "");
      return new BulkFetchIssueRequestBean(issueIdsOrKeys, expand, fields, fieldsByKeys, properties);
   }

   @NotNull
   public String toString() {
      return "BulkFetchIssueRequestBean(issueIdsOrKeys=" + this.issueIdsOrKeys + ", expand=" + this.expand + ", fields=" + this.fields + ", fieldsByKeys=" + this.fieldsByKeys + ", properties=" + this.properties + ")";
   }

   public int hashCode() {
      int var1 = this.issueIdsOrKeys.hashCode();
      var1 = var1 * 31 + (this.expand == null ? 0 : this.expand.hashCode());
      var1 = var1 * 31 + (this.fields == null ? 0 : this.fields.hashCode());
      var1 = var1 * 31 + (this.fieldsByKeys == null ? 0 : this.fieldsByKeys.hashCode());
      var1 = var1 * 31 + (this.properties == null ? 0 : this.properties.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkFetchIssueRequestBean)) {
         return false;
      } else {
         BulkFetchIssueRequestBean var2 = (BulkFetchIssueRequestBean)other;
         if (!Intrinsics.areEqual(this.issueIdsOrKeys, var2.issueIdsOrKeys)) {
            return false;
         } else if (!Intrinsics.areEqual(this.expand, var2.expand)) {
            return false;
         } else if (!Intrinsics.areEqual(this.fields, var2.fields)) {
            return false;
         } else if (!Intrinsics.areEqual(this.fieldsByKeys, var2.fieldsByKeys)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.properties, var2.properties);
         }
      }
   }
}
