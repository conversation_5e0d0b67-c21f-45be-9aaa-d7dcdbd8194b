package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0001\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J-\u0010\u0012\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0001H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkIssuePropertyUpdateRequest;", "", "expression", "", "filter", "Lai/zencoder/generated/client/jira/models/IssueFilterForBulkPropertySet;", "value", "<init>", "(Ljava/lang/String;Lai/zencoder/generated/client/jira/models/IssueFilterForBulkPropertySet;Ljava/lang/Object;)V", "getExpression", "()Ljava/lang/String;", "getFilter", "()Lai/zencoder/generated/client/jira/models/IssueFilterForBulkPropertySet;", "getValue", "()Ljava/lang/Object;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class BulkIssuePropertyUpdateRequest {
   @Nullable
   private final String expression;
   @Nullable
   private final IssueFilterForBulkPropertySet filter;
   @Nullable
   private final Object value;

   public BulkIssuePropertyUpdateRequest(@Nullable String expression, @Nullable IssueFilterForBulkPropertySet filter, @Nullable Object value) {
      this.expression = expression;
      this.filter = filter;
      this.value = value;
   }

   @JsonProperty("expression")
   @Nullable
   public final String getExpression() {
      return this.expression;
   }

   @JsonProperty("filter")
   @Nullable
   public final IssueFilterForBulkPropertySet getFilter() {
      return this.filter;
   }

   @JsonProperty("value")
   @Nullable
   public final Object getValue() {
      return this.value;
   }

   @Nullable
   public final String component1() {
      return this.expression;
   }

   @Nullable
   public final IssueFilterForBulkPropertySet component2() {
      return this.filter;
   }

   @Nullable
   public final Object component3() {
      return this.value;
   }

   @NotNull
   public final BulkIssuePropertyUpdateRequest copy(@Nullable String expression, @Nullable IssueFilterForBulkPropertySet filter, @Nullable Object value) {
      return new BulkIssuePropertyUpdateRequest(expression, filter, value);
   }

   @NotNull
   public String toString() {
      return "BulkIssuePropertyUpdateRequest(expression=" + this.expression + ", filter=" + this.filter + ", value=" + this.value + ")";
   }

   public int hashCode() {
      int var1 = this.expression == null ? 0 : this.expression.hashCode();
      var1 = var1 * 31 + (this.filter == null ? 0 : this.filter.hashCode());
      var1 = var1 * 31 + (this.value == null ? 0 : this.value.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkIssuePropertyUpdateRequest)) {
         return false;
      } else {
         BulkIssuePropertyUpdateRequest var2 = (BulkIssuePropertyUpdateRequest)other;
         if (!Intrinsics.areEqual(this.expression, var2.expression)) {
            return false;
         } else if (!Intrinsics.areEqual(this.filter, var2.filter)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.value, var2.value);
         }
      }
   }

   public BulkIssuePropertyUpdateRequest() {
      this((String)null, (IssueFilterForBulkPropertySet)null, (Object)null, 7, (DefaultConstructorMarker)null);
   }
}
