package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\fJ\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\fJ2\u0010\u0012\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0013J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\r\u001a\u0004\b\u000b\u0010\fR\u0017\u0010\u0006\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\r\u001a\u0004\b\u000e\u0010\f\u00a8\u0006\u001a"},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkOperationErrorResult;", "", "elementErrors", "Lai/zencoder/generated/client/jira/models/ErrorCollection;", "failedElementNumber", "", "status", "<init>", "(Lai/zencoder/generated/client/jira/models/ErrorCollection;Ljava/lang/Integer;Ljava/lang/Integer;)V", "getElementErrors", "()Lai/zencoder/generated/client/jira/models/ErrorCollection;", "getFailedElementNumber", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getStatus", "component1", "component2", "component3", "copy", "(Lai/zencoder/generated/client/jira/models/ErrorCollection;Ljava/lang/Integer;Ljava/lang/Integer;)Lai/zencoder/generated/client/jira/models/BulkOperationErrorResult;", "equals", "", "other", "hashCode", "toString", "", "zencoder"}
)
public final class BulkOperationErrorResult {
   @Nullable
   private final ErrorCollection elementErrors;
   @Nullable
   private final Integer failedElementNumber;
   @Nullable
   private final Integer status;

   public BulkOperationErrorResult(@Nullable ErrorCollection elementErrors, @Nullable Integer failedElementNumber, @Nullable Integer status) {
      this.elementErrors = elementErrors;
      this.failedElementNumber = failedElementNumber;
      this.status = status;
   }

   @JsonProperty("elementErrors")
   @Nullable
   public final ErrorCollection getElementErrors() {
      return this.elementErrors;
   }

   @JsonProperty("failedElementNumber")
   @Nullable
   public final Integer getFailedElementNumber() {
      return this.failedElementNumber;
   }

   @JsonProperty("status")
   @Nullable
   public final Integer getStatus() {
      return this.status;
   }

   @Nullable
   public final ErrorCollection component1() {
      return this.elementErrors;
   }

   @Nullable
   public final Integer component2() {
      return this.failedElementNumber;
   }

   @Nullable
   public final Integer component3() {
      return this.status;
   }

   @NotNull
   public final BulkOperationErrorResult copy(@Nullable ErrorCollection elementErrors, @Nullable Integer failedElementNumber, @Nullable Integer status) {
      return new BulkOperationErrorResult(elementErrors, failedElementNumber, status);
   }

   @NotNull
   public String toString() {
      return "BulkOperationErrorResult(elementErrors=" + this.elementErrors + ", failedElementNumber=" + this.failedElementNumber + ", status=" + this.status + ")";
   }

   public int hashCode() {
      int var1 = this.elementErrors == null ? 0 : this.elementErrors.hashCode();
      var1 = var1 * 31 + (this.failedElementNumber == null ? 0 : this.failedElementNumber.hashCode());
      var1 = var1 * 31 + (this.status == null ? 0 : this.status.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkOperationErrorResult)) {
         return false;
      } else {
         BulkOperationErrorResult var2 = (BulkOperationErrorResult)other;
         if (!Intrinsics.areEqual(this.elementErrors, var2.elementErrors)) {
            return false;
         } else if (!Intrinsics.areEqual(this.failedElementNumber, var2.failedElementNumber)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.status, var2.status);
         }
      }
   }

   public BulkOperationErrorResult() {
      this((ErrorCollection)null, (Integer)null, (Integer)null, 7, (DefaultConstructorMarker)null);
   }
}
