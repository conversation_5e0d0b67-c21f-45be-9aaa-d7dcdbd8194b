package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\"\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0005\u00a2\u0006\u0004\b\b\u0010\tJ\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0005H\u00c6\u0003J9\u0010\u0012\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00052\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001b\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\r\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkPermissionsRequestBean;", "", "accountId", "", "globalPermissions", "", "projectPermissions", "Lai/zencoder/generated/client/jira/models/BulkProjectPermissions;", "<init>", "(Ljava/lang/String;Ljava/util/Set;Ljava/util/Set;)V", "getAccountId", "()Ljava/lang/String;", "getGlobalPermissions", "()Ljava/util/Set;", "getProjectPermissions", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class BulkPermissionsRequestBean {
   @Nullable
   private final String accountId;
   @Nullable
   private final Set<String> globalPermissions;
   @Nullable
   private final Set<BulkProjectPermissions> projectPermissions;

   public BulkPermissionsRequestBean(@Nullable String accountId, @Nullable Set<String> globalPermissions, @Nullable Set<BulkProjectPermissions> projectPermissions) {
      this.accountId = accountId;
      this.globalPermissions = globalPermissions;
      this.projectPermissions = projectPermissions;
   }

   @JsonProperty("accountId")
   @Nullable
   public final String getAccountId() {
      return this.accountId;
   }

   @JsonProperty("globalPermissions")
   @Nullable
   public final Set<String> getGlobalPermissions() {
      return this.globalPermissions;
   }

   @JsonProperty("projectPermissions")
   @Nullable
   public final Set<BulkProjectPermissions> getProjectPermissions() {
      return this.projectPermissions;
   }

   @Nullable
   public final String component1() {
      return this.accountId;
   }

   @Nullable
   public final Set<String> component2() {
      return this.globalPermissions;
   }

   @Nullable
   public final Set<BulkProjectPermissions> component3() {
      return this.projectPermissions;
   }

   @NotNull
   public final BulkPermissionsRequestBean copy(@Nullable String accountId, @Nullable Set<String> globalPermissions, @Nullable Set<BulkProjectPermissions> projectPermissions) {
      return new BulkPermissionsRequestBean(accountId, globalPermissions, projectPermissions);
   }

   @NotNull
   public String toString() {
      return "BulkPermissionsRequestBean(accountId=" + this.accountId + ", globalPermissions=" + this.globalPermissions + ", projectPermissions=" + this.projectPermissions + ")";
   }

   public int hashCode() {
      int var1 = this.accountId == null ? 0 : this.accountId.hashCode();
      var1 = var1 * 31 + (this.globalPermissions == null ? 0 : this.globalPermissions.hashCode());
      var1 = var1 * 31 + (this.projectPermissions == null ? 0 : this.projectPermissions.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkPermissionsRequestBean)) {
         return false;
      } else {
         BulkPermissionsRequestBean var2 = (BulkPermissionsRequestBean)other;
         if (!Intrinsics.areEqual(this.accountId, var2.accountId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.globalPermissions, var2.globalPermissions)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.projectPermissions, var2.projectPermissions);
         }
      }
   }

   public BulkPermissionsRequestBean() {
      this((String)null, (Set)null, (Set)null, 7, (DefaultConstructorMarker)null);
   }
}
