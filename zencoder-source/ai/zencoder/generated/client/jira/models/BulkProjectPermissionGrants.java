package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\"\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0004\b\b\u0010\tJ\u000f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0006H\u00c6\u0003J\u000f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J3\u0010\u0012\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0006H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0005\u001a\u00020\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0019\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000b\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkProjectPermissionGrants;", "", "issues", "", "", "permission", "", "projects", "<init>", "(Ljava/util/Set;Ljava/lang/String;Ljava/util/Set;)V", "getIssues", "()Ljava/util/Set;", "getPermission", "()Ljava/lang/String;", "getProjects", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class BulkProjectPermissionGrants {
   @NotNull
   private final Set<Long> issues;
   @NotNull
   private final String permission;
   @NotNull
   private final Set<Long> projects;

   public BulkProjectPermissionGrants(@NotNull Set<Long> issues, @NotNull String permission, @NotNull Set<Long> projects) {
      Intrinsics.checkNotNullParameter(issues, "");
      Intrinsics.checkNotNullParameter(permission, "");
      Intrinsics.checkNotNullParameter(projects, "");
      super();
      this.issues = issues;
      this.permission = permission;
      this.projects = projects;
   }

   @JsonProperty("issues")
   @NotNull
   public final Set<Long> getIssues() {
      return this.issues;
   }

   @JsonProperty("permission")
   @NotNull
   public final String getPermission() {
      return this.permission;
   }

   @JsonProperty("projects")
   @NotNull
   public final Set<Long> getProjects() {
      return this.projects;
   }

   @NotNull
   public final Set<Long> component1() {
      return this.issues;
   }

   @NotNull
   public final String component2() {
      return this.permission;
   }

   @NotNull
   public final Set<Long> component3() {
      return this.projects;
   }

   @NotNull
   public final BulkProjectPermissionGrants copy(@NotNull Set<Long> issues, @NotNull String permission, @NotNull Set<Long> projects) {
      Intrinsics.checkNotNullParameter(issues, "");
      Intrinsics.checkNotNullParameter(permission, "");
      Intrinsics.checkNotNullParameter(projects, "");
      return new BulkProjectPermissionGrants(issues, permission, projects);
   }

   @NotNull
   public String toString() {
      return "BulkProjectPermissionGrants(issues=" + this.issues + ", permission=" + this.permission + ", projects=" + this.projects + ")";
   }

   public int hashCode() {
      int var1 = this.issues.hashCode();
      var1 = var1 * 31 + this.permission.hashCode();
      var1 = var1 * 31 + this.projects.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkProjectPermissionGrants)) {
         return false;
      } else {
         BulkProjectPermissionGrants var2 = (BulkProjectPermissionGrants)other;
         if (!Intrinsics.areEqual(this.issues, var2.issues)) {
            return false;
         } else if (!Intrinsics.areEqual(this.permission, var2.permission)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.projects, var2.projects);
         }
      }
   }
}
