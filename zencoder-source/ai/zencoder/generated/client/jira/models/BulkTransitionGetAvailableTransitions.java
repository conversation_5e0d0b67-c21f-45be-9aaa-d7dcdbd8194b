package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\b\u0010\tJ\u0011\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J3\u0010\u0012\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0006H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\r\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkTransitionGetAvailableTransitions;", "", "availableTransitions", "", "Lai/zencoder/generated/client/jira/models/IssueBulkTransitionForWorkflow;", "endingBefore", "", "startingAfter", "<init>", "(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;)V", "getAvailableTransitions", "()Ljava/util/List;", "getEndingBefore", "()Ljava/lang/String;", "getStartingAfter", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class BulkTransitionGetAvailableTransitions {
   @Nullable
   private final List<IssueBulkTransitionForWorkflow> availableTransitions;
   @Nullable
   private final String endingBefore;
   @Nullable
   private final String startingAfter;

   public BulkTransitionGetAvailableTransitions(@Nullable List<IssueBulkTransitionForWorkflow> availableTransitions, @Nullable String endingBefore, @Nullable String startingAfter) {
      this.availableTransitions = availableTransitions;
      this.endingBefore = endingBefore;
      this.startingAfter = startingAfter;
   }

   @JsonProperty("availableTransitions")
   @Nullable
   public final List<IssueBulkTransitionForWorkflow> getAvailableTransitions() {
      return this.availableTransitions;
   }

   @JsonProperty("endingBefore")
   @Nullable
   public final String getEndingBefore() {
      return this.endingBefore;
   }

   @JsonProperty("startingAfter")
   @Nullable
   public final String getStartingAfter() {
      return this.startingAfter;
   }

   @Nullable
   public final List<IssueBulkTransitionForWorkflow> component1() {
      return this.availableTransitions;
   }

   @Nullable
   public final String component2() {
      return this.endingBefore;
   }

   @Nullable
   public final String component3() {
      return this.startingAfter;
   }

   @NotNull
   public final BulkTransitionGetAvailableTransitions copy(@Nullable List<IssueBulkTransitionForWorkflow> availableTransitions, @Nullable String endingBefore, @Nullable String startingAfter) {
      return new BulkTransitionGetAvailableTransitions(availableTransitions, endingBefore, startingAfter);
   }

   @NotNull
   public String toString() {
      return "BulkTransitionGetAvailableTransitions(availableTransitions=" + this.availableTransitions + ", endingBefore=" + this.endingBefore + ", startingAfter=" + this.startingAfter + ")";
   }

   public int hashCode() {
      int var1 = this.availableTransitions == null ? 0 : this.availableTransitions.hashCode();
      var1 = var1 * 31 + (this.endingBefore == null ? 0 : this.endingBefore.hashCode());
      var1 = var1 * 31 + (this.startingAfter == null ? 0 : this.startingAfter.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkTransitionGetAvailableTransitions)) {
         return false;
      } else {
         BulkTransitionGetAvailableTransitions var2 = (BulkTransitionGetAvailableTransitions)other;
         if (!Intrinsics.areEqual(this.availableTransitions, var2.availableTransitions)) {
            return false;
         } else if (!Intrinsics.areEqual(this.endingBefore, var2.endingBefore)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.startingAfter, var2.startingAfter);
         }
      }
   }

   public BulkTransitionGetAvailableTransitions() {
      this((List)null, (String)null, (String)null, 7, (DefaultConstructorMarker)null);
   }
}
