package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0004H\u00c6\u0003J#\u0010\u000e\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0004H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0004H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0005\u001a\u00020\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"},
   d2 = {"Lai/zencoder/generated/client/jira/models/BulkTransitionSubmitInput;", "", "selectedIssueIdsOrKeys", "", "", "transitionId", "<init>", "(Ljava/util/List;Ljava/lang/String;)V", "getSelectedIssueIdsOrKeys", "()Ljava/util/List;", "getTransitionId", "()Ljava/lang/String;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class BulkTransitionSubmitInput {
   @NotNull
   private final List<String> selectedIssueIdsOrKeys;
   @NotNull
   private final String transitionId;

   public BulkTransitionSubmitInput(@NotNull List<String> selectedIssueIdsOrKeys, @NotNull String transitionId) {
      Intrinsics.checkNotNullParameter(selectedIssueIdsOrKeys, "");
      Intrinsics.checkNotNullParameter(transitionId, "");
      super();
      this.selectedIssueIdsOrKeys = selectedIssueIdsOrKeys;
      this.transitionId = transitionId;
   }

   @JsonProperty("selectedIssueIdsOrKeys")
   @NotNull
   public final List<String> getSelectedIssueIdsOrKeys() {
      return this.selectedIssueIdsOrKeys;
   }

   @JsonProperty("transitionId")
   @NotNull
   public final String getTransitionId() {
      return this.transitionId;
   }

   @NotNull
   public final List<String> component1() {
      return this.selectedIssueIdsOrKeys;
   }

   @NotNull
   public final String component2() {
      return this.transitionId;
   }

   @NotNull
   public final BulkTransitionSubmitInput copy(@NotNull List<String> selectedIssueIdsOrKeys, @NotNull String transitionId) {
      Intrinsics.checkNotNullParameter(selectedIssueIdsOrKeys, "");
      Intrinsics.checkNotNullParameter(transitionId, "");
      return new BulkTransitionSubmitInput(selectedIssueIdsOrKeys, transitionId);
   }

   @NotNull
   public String toString() {
      return "BulkTransitionSubmitInput(selectedIssueIdsOrKeys=" + this.selectedIssueIdsOrKeys + ", transitionId=" + this.transitionId + ")";
   }

   public int hashCode() {
      int var1 = this.selectedIssueIdsOrKeys.hashCode();
      var1 = var1 * 31 + this.transitionId.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof BulkTransitionSubmitInput)) {
         return false;
      } else {
         BulkTransitionSubmitInput var2 = (BulkTransitionSubmitInput)other;
         if (!Intrinsics.areEqual(this.selectedIssueIdsOrKeys, var2.selectedIssueIdsOrKeys)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.transitionId, var2.transitionId);
         }
      }
   }
}
