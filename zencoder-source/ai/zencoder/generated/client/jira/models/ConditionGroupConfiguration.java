package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u001aB7\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0000\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u0011\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\u0000\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J9\u0010\u0012\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0000\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0000\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u001b"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ConditionGroupConfiguration;", "", "conditionGroups", "", "conditions", "Lai/zencoder/generated/client/jira/models/WorkflowRuleConfiguration;", "operation", "Lai/zencoder/generated/client/jira/models/ConditionGroupConfiguration$Operation;", "<init>", "(Ljava/util/List;Ljava/util/List;Lai/zencoder/generated/client/jira/models/ConditionGroupConfiguration$Operation;)V", "getConditionGroups", "()Ljava/util/List;", "getConditions", "getOperation", "()Lai/zencoder/generated/client/jira/models/ConditionGroupConfiguration$Operation;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "Operation", "zencoder"}
)
public final class ConditionGroupConfiguration {
   @Nullable
   private final List<ConditionGroupConfiguration> conditionGroups;
   @Nullable
   private final List<WorkflowRuleConfiguration> conditions;
   @Nullable
   private final Operation operation;

   public ConditionGroupConfiguration(@Nullable List<ConditionGroupConfiguration> conditionGroups, @Nullable List<WorkflowRuleConfiguration> conditions, @Nullable Operation operation) {
      this.conditionGroups = conditionGroups;
      this.conditions = conditions;
      this.operation = operation;
   }

   @JsonProperty("conditionGroups")
   @Nullable
   public final List<ConditionGroupConfiguration> getConditionGroups() {
      return this.conditionGroups;
   }

   @JsonProperty("conditions")
   @Nullable
   public final List<WorkflowRuleConfiguration> getConditions() {
      return this.conditions;
   }

   @JsonProperty("operation")
   @Nullable
   public final Operation getOperation() {
      return this.operation;
   }

   @Nullable
   public final List<ConditionGroupConfiguration> component1() {
      return this.conditionGroups;
   }

   @Nullable
   public final List<WorkflowRuleConfiguration> component2() {
      return this.conditions;
   }

   @Nullable
   public final Operation component3() {
      return this.operation;
   }

   @NotNull
   public final ConditionGroupConfiguration copy(@Nullable List<ConditionGroupConfiguration> conditionGroups, @Nullable List<WorkflowRuleConfiguration> conditions, @Nullable Operation operation) {
      return new ConditionGroupConfiguration(conditionGroups, conditions, operation);
   }

   @NotNull
   public String toString() {
      return "ConditionGroupConfiguration(conditionGroups=" + this.conditionGroups + ", conditions=" + this.conditions + ", operation=" + this.operation + ")";
   }

   public int hashCode() {
      int var1 = this.conditionGroups == null ? 0 : this.conditionGroups.hashCode();
      var1 = var1 * 31 + (this.conditions == null ? 0 : this.conditions.hashCode());
      var1 = var1 * 31 + (this.operation == null ? 0 : this.operation.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ConditionGroupConfiguration)) {
         return false;
      } else {
         ConditionGroupConfiguration var2 = (ConditionGroupConfiguration)other;
         if (!Intrinsics.areEqual(this.conditionGroups, var2.conditionGroups)) {
            return false;
         } else if (!Intrinsics.areEqual(this.conditions, var2.conditions)) {
            return false;
         } else {
            return this.operation == var2.operation;
         }
      }
   }

   public ConditionGroupConfiguration() {
      this((List)null, (List)null, (Operation)null, 7, (DefaultConstructorMarker)null);
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/ConditionGroupConfiguration$Operation;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "ANY", "ALL", "zencoder"}
   )
   public static enum Operation {
      @NotNull
      private final String value;
      @JsonProperty("ANY")
      ANY("ANY"),
      @JsonProperty("ALL")
      ALL("ALL");

      private Operation(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Operation> getEntries() {
         return $ENTRIES;
      }
   }
}
