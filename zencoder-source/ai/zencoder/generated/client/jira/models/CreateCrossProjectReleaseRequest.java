package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\"\n\u0002\u0010\t\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000b\u0010\r\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J'\u0010\u000f\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CreateCrossProjectReleaseRequest;", "", "NAME", "", "releaseIds", "", "", "<init>", "(Ljava/lang/String;Ljava/util/Set;)V", "getNAME", "()Ljava/lang/String;", "getReleaseIds", "()Ljava/util/Set;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CreateCrossProjectReleaseRequest {
   @Nullable
   private final String NAME;
   @Nullable
   private final Set<Long> releaseIds;

   public CreateCrossProjectReleaseRequest(@Nullable String NAME, @Nullable Set<Long> releaseIds) {
      this.NAME = NAME;
      this.releaseIds = releaseIds;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("releaseIds")
   @Nullable
   public final Set<Long> getReleaseIds() {
      return this.releaseIds;
   }

   @Nullable
   public final String component1() {
      return this.NAME;
   }

   @Nullable
   public final Set<Long> component2() {
      return this.releaseIds;
   }

   @NotNull
   public final CreateCrossProjectReleaseRequest copy(@Nullable String NAME, @Nullable Set<Long> releaseIds) {
      return new CreateCrossProjectReleaseRequest(NAME, releaseIds);
   }

   @NotNull
   public String toString() {
      return "CreateCrossProjectReleaseRequest(NAME=" + this.NAME + ", releaseIds=" + this.releaseIds + ")";
   }

   public int hashCode() {
      int var1 = this.NAME == null ? 0 : this.NAME.hashCode();
      var1 = var1 * 31 + (this.releaseIds == null ? 0 : this.releaseIds.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CreateCrossProjectReleaseRequest)) {
         return false;
      } else {
         CreateCrossProjectReleaseRequest var2 = (CreateCrossProjectReleaseRequest)other;
         if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.releaseIds, var2.releaseIds);
         }
      }
   }

   public CreateCrossProjectReleaseRequest() {
      this((String)null, (Set)null, 3, (DefaultConstructorMarker)null);
   }
}
