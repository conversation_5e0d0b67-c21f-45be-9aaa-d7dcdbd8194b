package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BO\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0006\u00a2\u0006\u0004\b\t\u0010\nJ\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0014\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0006H\u00c6\u0003JQ\u0010\u0017\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\fR\u001b\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000f\u00a8\u0006\u001e"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CreateCustomFieldContext;", "", "description", "", "id", "issueTypeIds", "", "NAME", "projectIds", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/util/List;)V", "getDescription", "()Ljava/lang/String;", "getId", "getIssueTypeIds", "()Ljava/util/List;", "getNAME", "getProjectIds", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CreateCustomFieldContext {
   @Nullable
   private final String description;
   @Nullable
   private final String id;
   @Nullable
   private final List<String> issueTypeIds;
   @Nullable
   private final String NAME;
   @Nullable
   private final List<String> projectIds;

   public CreateCustomFieldContext(@Nullable String description, @Nullable String id, @Nullable List<String> issueTypeIds, @Nullable String NAME, @Nullable List<String> projectIds) {
      this.description = description;
      this.id = id;
      this.issueTypeIds = issueTypeIds;
      this.NAME = NAME;
      this.projectIds = projectIds;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("id")
   @Nullable
   public final String getId() {
      return this.id;
   }

   @JsonProperty("issueTypeIds")
   @Nullable
   public final List<String> getIssueTypeIds() {
      return this.issueTypeIds;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("projectIds")
   @Nullable
   public final List<String> getProjectIds() {
      return this.projectIds;
   }

   @Nullable
   public final String component1() {
      return this.description;
   }

   @Nullable
   public final String component2() {
      return this.id;
   }

   @Nullable
   public final List<String> component3() {
      return this.issueTypeIds;
   }

   @Nullable
   public final String component4() {
      return this.NAME;
   }

   @Nullable
   public final List<String> component5() {
      return this.projectIds;
   }

   @NotNull
   public final CreateCustomFieldContext copy(@Nullable String description, @Nullable String id, @Nullable List<String> issueTypeIds, @Nullable String NAME, @Nullable List<String> projectIds) {
      return new CreateCustomFieldContext(description, id, issueTypeIds, NAME, projectIds);
   }

   @NotNull
   public String toString() {
      return "CreateCustomFieldContext(description=" + this.description + ", id=" + this.id + ", issueTypeIds=" + this.issueTypeIds + ", NAME=" + this.NAME + ", projectIds=" + this.projectIds + ")";
   }

   public int hashCode() {
      int var1 = this.description == null ? 0 : this.description.hashCode();
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      var1 = var1 * 31 + (this.issueTypeIds == null ? 0 : this.issueTypeIds.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.projectIds == null ? 0 : this.projectIds.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CreateCustomFieldContext)) {
         return false;
      } else {
         CreateCustomFieldContext var2 = (CreateCustomFieldContext)other;
         if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueTypeIds, var2.issueTypeIds)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.projectIds, var2.projectIds);
         }
      }
   }

   public CreateCustomFieldContext() {
      this((String)null, (String)null, (List)null, (String)null, (List)null, 31, (DefaultConstructorMarker)null);
   }
}
