package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u000e\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001b\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000bJ$\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0010J\u0013\u0010\u0011\u001a\u00020\u00052\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\f\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0017"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CreateCustomFieldRequest;", "", "customFieldId", "", "filter", "", "<init>", "(JLjava/lang/Boolean;)V", "getCustomFieldId", "()J", "getFilter", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "component1", "component2", "copy", "(JLjava/lang/Boolean;)Lai/zencoder/generated/client/jira/models/CreateCustomFieldRequest;", "equals", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class CreateCustomFieldRequest {
   private final long customFieldId;
   @Nullable
   private final Boolean filter;

   public CreateCustomFieldRequest(long customFieldId, @Nullable Boolean filter) {
      this.customFieldId = customFieldId;
      this.filter = filter;
   }

   @JsonProperty("customFieldId")
   public final long getCustomFieldId() {
      return this.customFieldId;
   }

   @JsonProperty("filter")
   @Nullable
   public final Boolean getFilter() {
      return this.filter;
   }

   public final long component1() {
      return this.customFieldId;
   }

   @Nullable
   public final Boolean component2() {
      return this.filter;
   }

   @NotNull
   public final CreateCustomFieldRequest copy(long customFieldId, @Nullable Boolean filter) {
      return new CreateCustomFieldRequest(customFieldId, filter);
   }

   @NotNull
   public String toString() {
      return "CreateCustomFieldRequest(customFieldId=" + this.customFieldId + ", filter=" + this.filter + ")";
   }

   public int hashCode() {
      int var1 = Long.hashCode(this.customFieldId);
      var1 = var1 * 31 + (this.filter == null ? 0 : this.filter.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CreateCustomFieldRequest)) {
         return false;
      } else {
         CreateCustomFieldRequest var2 = (CreateCustomFieldRequest)other;
         if (this.customFieldId != var2.customFieldId) {
            return false;
         } else {
            return Intrinsics.areEqual(this.filter, var2.filter);
         }
      }
   }
}
