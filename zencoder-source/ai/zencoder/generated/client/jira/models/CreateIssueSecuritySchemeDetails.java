package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004B1\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0002\u0012\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0002\u00a2\u0006\u0004\b\n\u0010\u000bJ\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u0011\u0010\u0012\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J3\u0010\u0014\u001a\u00020\u00002\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00022\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00072\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0002H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0002H\u00d6\u0001R\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001b\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\t\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\r\u00a8\u0006\u001b"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CreateIssueSecuritySchemeDetails;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "description", "levels", "", "Lai/zencoder/generated/client/jira/models/SecuritySchemeLevelBean;", "NAME", "<init>", "(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;)V", "getDescription", "()Ljava/lang/String;", "getLevels", "()Ljava/util/List;", "getNAME", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CreateIssueSecuritySchemeDetails extends HashMap<String, Object> {
   @Nullable
   private final String description;
   @Nullable
   private final List<SecuritySchemeLevelBean> levels;
   @Nullable
   private final String NAME;

   public CreateIssueSecuritySchemeDetails(@Nullable String description, @Nullable List<SecuritySchemeLevelBean> levels, @Nullable String NAME) {
      this.description = description;
      this.levels = levels;
      this.NAME = NAME;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("levels")
   @Nullable
   public final List<SecuritySchemeLevelBean> getLevels() {
      return this.levels;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @Nullable
   public final String component1() {
      return this.description;
   }

   @Nullable
   public final List<SecuritySchemeLevelBean> component2() {
      return this.levels;
   }

   @Nullable
   public final String component3() {
      return this.NAME;
   }

   @NotNull
   public final CreateIssueSecuritySchemeDetails copy(@Nullable String description, @Nullable List<SecuritySchemeLevelBean> levels, @Nullable String NAME) {
      return new CreateIssueSecuritySchemeDetails(description, levels, NAME);
   }

   @NotNull
   public String toString() {
      return "CreateIssueSecuritySchemeDetails(description=" + this.description + ", levels=" + this.levels + ", NAME=" + this.NAME + ")";
   }

   public int hashCode() {
      int var1 = this.description == null ? 0 : this.description.hashCode();
      var1 = var1 * 31 + (this.levels == null ? 0 : this.levels.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CreateIssueSecuritySchemeDetails)) {
         return false;
      } else {
         CreateIssueSecuritySchemeDetails var2 = (CreateIssueSecuritySchemeDetails)other;
         if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.levels, var2.levels)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.NAME, var2.NAME);
         }
      }
   }

   public CreateIssueSecuritySchemeDetails() {
      this((String)null, (List)null, (String)null, 7, (DefaultConstructorMarker)null);
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
