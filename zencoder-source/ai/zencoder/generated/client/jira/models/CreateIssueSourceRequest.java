package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u0016B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0017"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CreateIssueSourceRequest;", "", "type", "Lai/zencoder/generated/client/jira/models/CreateIssueSourceRequest$Type;", "value", "", "<init>", "(Lai/zencoder/generated/client/jira/models/CreateIssueSourceRequest$Type;J)V", "getType", "()Lai/zencoder/generated/client/jira/models/CreateIssueSourceRequest$Type;", "getValue", "()J", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "Type", "zencoder"}
)
public final class CreateIssueSourceRequest {
   @NotNull
   private final Type type;
   private final long value;

   public CreateIssueSourceRequest(@NotNull Type type, long value) {
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.type = type;
      this.value = value;
   }

   @JsonProperty("type")
   @NotNull
   public final Type getType() {
      return this.type;
   }

   @JsonProperty("value")
   public final long getValue() {
      return this.value;
   }

   @NotNull
   public final Type component1() {
      return this.type;
   }

   public final long component2() {
      return this.value;
   }

   @NotNull
   public final CreateIssueSourceRequest copy(@NotNull Type type, long value) {
      Intrinsics.checkNotNullParameter(type, "");
      return new CreateIssueSourceRequest(type, value);
   }

   @NotNull
   public String toString() {
      return "CreateIssueSourceRequest(type=" + this.type + ", value=" + this.value + ")";
   }

   public int hashCode() {
      int var1 = this.type.hashCode();
      var1 = var1 * 31 + Long.hashCode(this.value);
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CreateIssueSourceRequest)) {
         return false;
      } else {
         CreateIssueSourceRequest var2 = (CreateIssueSourceRequest)other;
         if (this.type != var2.type) {
            return false;
         } else {
            return this.value == var2.value;
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"},
      d2 = {"Lai/zencoder/generated/client/jira/models/CreateIssueSourceRequest$Type;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "Board", "Project", "Filter", "zencoder"}
   )
   public static enum Type {
      @NotNull
      private final String value;
      @JsonProperty("Board")
      Board("Board"),
      @JsonProperty("Project")
      Project("Project"),
      @JsonProperty("Filter")
      Filter("Filter");

      private Type(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Type> getEntries() {
         return $ENTRIES;
      }
   }
}
