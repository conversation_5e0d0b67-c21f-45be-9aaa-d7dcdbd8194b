package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u001a\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001*BQ\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\r\u0010\u000eJ\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010\u001d\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0012J\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0015J\u0011\u0010\u001f\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\tH\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u0010\u0010!\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0015JZ\u0010\"\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010#J\u0013\u0010$\u001a\u00020%2\b\u0010&\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010'\u001a\u00020(H\u00d6\u0001J\t\u0010)\u001a\u00020\nH\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\u0013\u001a\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u0016\u001a\u0004\b\u0014\u0010\u0015R\u001b\u0010\b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0015\u0010\u000b\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\f\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u0016\u001a\u0004\b\u001b\u0010\u0015\u00a8\u0006+"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CreatePlanOnlyTeamRequest;", "", "planningStyle", "Lai/zencoder/generated/client/jira/models/CreatePlanOnlyTeamRequest$PlanningStyle;", "capacity", "", "issueSourceId", "", "memberAccountIds", "", "", "NAME", "sprintLength", "<init>", "(Lai/zencoder/generated/client/jira/models/CreatePlanOnlyTeamRequest$PlanningStyle;Ljava/lang/Double;Ljava/lang/Long;Ljava/util/Set;Ljava/lang/String;Ljava/lang/Long;)V", "getPlanningStyle", "()Lai/zencoder/generated/client/jira/models/CreatePlanOnlyTeamRequest$PlanningStyle;", "getCapacity", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getIssueSourceId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getMemberAccountIds", "()Ljava/util/Set;", "getNAME", "()Ljava/lang/String;", "getSprintLength", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "(Lai/zencoder/generated/client/jira/models/CreatePlanOnlyTeamRequest$PlanningStyle;Ljava/lang/Double;Ljava/lang/Long;Ljava/util/Set;Ljava/lang/String;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/CreatePlanOnlyTeamRequest;", "equals", "", "other", "hashCode", "", "toString", "PlanningStyle", "zencoder"}
)
public final class CreatePlanOnlyTeamRequest {
   @NotNull
   private final PlanningStyle planningStyle;
   @Nullable
   private final Double capacity;
   @Nullable
   private final Long issueSourceId;
   @Nullable
   private final Set<String> memberAccountIds;
   @Nullable
   private final String NAME;
   @Nullable
   private final Long sprintLength;

   public CreatePlanOnlyTeamRequest(@NotNull PlanningStyle planningStyle, @Nullable Double capacity, @Nullable Long issueSourceId, @Nullable Set<String> memberAccountIds, @Nullable String NAME, @Nullable Long sprintLength) {
      Intrinsics.checkNotNullParameter(planningStyle, "");
      super();
      this.planningStyle = planningStyle;
      this.capacity = capacity;
      this.issueSourceId = issueSourceId;
      this.memberAccountIds = memberAccountIds;
      this.NAME = NAME;
      this.sprintLength = sprintLength;
   }

   @JsonProperty("planningStyle")
   @NotNull
   public final PlanningStyle getPlanningStyle() {
      return this.planningStyle;
   }

   @JsonProperty("capacity")
   @Nullable
   public final Double getCapacity() {
      return this.capacity;
   }

   @JsonProperty("issueSourceId")
   @Nullable
   public final Long getIssueSourceId() {
      return this.issueSourceId;
   }

   @JsonProperty("memberAccountIds")
   @Nullable
   public final Set<String> getMemberAccountIds() {
      return this.memberAccountIds;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("sprintLength")
   @Nullable
   public final Long getSprintLength() {
      return this.sprintLength;
   }

   @NotNull
   public final PlanningStyle component1() {
      return this.planningStyle;
   }

   @Nullable
   public final Double component2() {
      return this.capacity;
   }

   @Nullable
   public final Long component3() {
      return this.issueSourceId;
   }

   @Nullable
   public final Set<String> component4() {
      return this.memberAccountIds;
   }

   @Nullable
   public final String component5() {
      return this.NAME;
   }

   @Nullable
   public final Long component6() {
      return this.sprintLength;
   }

   @NotNull
   public final CreatePlanOnlyTeamRequest copy(@NotNull PlanningStyle planningStyle, @Nullable Double capacity, @Nullable Long issueSourceId, @Nullable Set<String> memberAccountIds, @Nullable String NAME, @Nullable Long sprintLength) {
      Intrinsics.checkNotNullParameter(planningStyle, "");
      return new CreatePlanOnlyTeamRequest(planningStyle, capacity, issueSourceId, memberAccountIds, NAME, sprintLength);
   }

   @NotNull
   public String toString() {
      return "CreatePlanOnlyTeamRequest(planningStyle=" + this.planningStyle + ", capacity=" + this.capacity + ", issueSourceId=" + this.issueSourceId + ", memberAccountIds=" + this.memberAccountIds + ", NAME=" + this.NAME + ", sprintLength=" + this.sprintLength + ")";
   }

   public int hashCode() {
      int var1 = this.planningStyle.hashCode();
      var1 = var1 * 31 + (this.capacity == null ? 0 : this.capacity.hashCode());
      var1 = var1 * 31 + (this.issueSourceId == null ? 0 : this.issueSourceId.hashCode());
      var1 = var1 * 31 + (this.memberAccountIds == null ? 0 : this.memberAccountIds.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.sprintLength == null ? 0 : this.sprintLength.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CreatePlanOnlyTeamRequest)) {
         return false;
      } else {
         CreatePlanOnlyTeamRequest var2 = (CreatePlanOnlyTeamRequest)other;
         if (this.planningStyle != var2.planningStyle) {
            return false;
         } else if (!Intrinsics.areEqual(this.capacity, var2.capacity)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueSourceId, var2.issueSourceId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.memberAccountIds, var2.memberAccountIds)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.sprintLength, var2.sprintLength);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/CreatePlanOnlyTeamRequest$PlanningStyle;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "Scrum", "Kanban", "zencoder"}
   )
   public static enum PlanningStyle {
      @NotNull
      private final String value;
      @JsonProperty("Scrum")
      Scrum("Scrum"),
      @JsonProperty("Kanban")
      Kanban("Kanban");

      private PlanningStyle(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<PlanningStyle> getEntries() {
         return $ENTRIES;
      }
   }
}
