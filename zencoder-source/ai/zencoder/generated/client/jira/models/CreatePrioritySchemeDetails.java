package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BS\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0007\u0012\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\f\u00a2\u0006\u0004\b\r\u0010\u000eJ\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u0011\u0010\u001f\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\fH\u00c6\u0003JY\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00072\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\fH\u00c6\u0001J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010$\u001a\u00020%H\u00d6\u0001J\t\u0010&\u001a\u00020\u0007H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0015\u0010\b\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0015\u0010\n\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0014R\u001b\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\f8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019\u00a8\u0006'"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CreatePrioritySchemeDetails;", "", "defaultPriorityId", "", "priorityIds", "", "description", "", "mappings", "Lai/zencoder/generated/client/jira/models/PriorityMapping;", "NAME", "projectIds", "", "<init>", "(JLjava/util/List;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/PriorityMapping;Ljava/lang/String;Ljava/util/Set;)V", "getDefaultPriorityId", "()J", "getPriorityIds", "()Ljava/util/List;", "getDescription", "()Ljava/lang/String;", "getMappings", "()Lai/zencoder/generated/client/jira/models/PriorityMapping;", "getNAME", "getProjectIds", "()Ljava/util/Set;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CreatePrioritySchemeDetails {
   private final long defaultPriorityId;
   @NotNull
   private final List<Long> priorityIds;
   @Nullable
   private final String description;
   @Nullable
   private final PriorityMapping mappings;
   @Nullable
   private final String NAME;
   @Nullable
   private final Set<Long> projectIds;

   public CreatePrioritySchemeDetails(long defaultPriorityId, @NotNull List<Long> priorityIds, @Nullable String description, @Nullable PriorityMapping mappings, @Nullable String NAME, @Nullable Set<Long> projectIds) {
      Intrinsics.checkNotNullParameter(priorityIds, "");
      super();
      this.defaultPriorityId = defaultPriorityId;
      this.priorityIds = priorityIds;
      this.description = description;
      this.mappings = mappings;
      this.NAME = NAME;
      this.projectIds = projectIds;
   }

   @JsonProperty("defaultPriorityId")
   public final long getDefaultPriorityId() {
      return this.defaultPriorityId;
   }

   @JsonProperty("priorityIds")
   @NotNull
   public final List<Long> getPriorityIds() {
      return this.priorityIds;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("mappings")
   @Nullable
   public final PriorityMapping getMappings() {
      return this.mappings;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("projectIds")
   @Nullable
   public final Set<Long> getProjectIds() {
      return this.projectIds;
   }

   public final long component1() {
      return this.defaultPriorityId;
   }

   @NotNull
   public final List<Long> component2() {
      return this.priorityIds;
   }

   @Nullable
   public final String component3() {
      return this.description;
   }

   @Nullable
   public final PriorityMapping component4() {
      return this.mappings;
   }

   @Nullable
   public final String component5() {
      return this.NAME;
   }

   @Nullable
   public final Set<Long> component6() {
      return this.projectIds;
   }

   @NotNull
   public final CreatePrioritySchemeDetails copy(long defaultPriorityId, @NotNull List<Long> priorityIds, @Nullable String description, @Nullable PriorityMapping mappings, @Nullable String NAME, @Nullable Set<Long> projectIds) {
      Intrinsics.checkNotNullParameter(priorityIds, "");
      return new CreatePrioritySchemeDetails(defaultPriorityId, priorityIds, description, mappings, NAME, projectIds);
   }

   @NotNull
   public String toString() {
      return "CreatePrioritySchemeDetails(defaultPriorityId=" + this.defaultPriorityId + ", priorityIds=" + this.priorityIds + ", description=" + this.description + ", mappings=" + this.mappings + ", NAME=" + this.NAME + ", projectIds=" + this.projectIds + ")";
   }

   public int hashCode() {
      int var1 = Long.hashCode(this.defaultPriorityId);
      var1 = var1 * 31 + this.priorityIds.hashCode();
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.mappings == null ? 0 : this.mappings.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.projectIds == null ? 0 : this.projectIds.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CreatePrioritySchemeDetails)) {
         return false;
      } else {
         CreatePrioritySchemeDetails var2 = (CreatePrioritySchemeDetails)other;
         if (this.defaultPriorityId != var2.defaultPriorityId) {
            return false;
         } else if (!Intrinsics.areEqual(this.priorityIds, var2.priorityIds)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.mappings, var2.mappings)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.projectIds, var2.projectIds);
         }
      }
   }
}
