package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010$\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001a\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001/Bq\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\u0016\b\u0002\u0010\n\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\u0004\b\u0010\u0010\u0011J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010#\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\bH\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0017\u0010%\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u000bH\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\u000b\u0010'\u001a\u0004\u0018\u00010\u000fH\u00c6\u0003Jw\u0010(\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\u0016\b\u0002\u0010\n\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u00c6\u0001J\u0013\u0010)\u001a\u00020*2\b\u0010+\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010,\u001a\u00020-H\u00d6\u0001J\t\u0010.\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013R\u001b\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0015\u0010\t\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013R!\u0010\n\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u000b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0015\u0010\f\u001a\u0004\u0018\u00010\r8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0015\u0010\u000e\u001a\u0004\u0018\u00010\u000f8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001f\u00a8\u00060"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionDetails;", "", "to", "", "type", "Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionDetails$Type;", "description", "from", "", "NAME", "properties", "", "rules", "Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionRulesDetails;", "screen", "Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionScreenDetails;", "<init>", "(Ljava/lang/String;Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionDetails$Type;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/util/Map;Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionRulesDetails;Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionScreenDetails;)V", "getTo", "()Ljava/lang/String;", "getType", "()Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionDetails$Type;", "getDescription", "getFrom", "()Ljava/util/List;", "getNAME", "getProperties", "()Ljava/util/Map;", "getRules", "()Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionRulesDetails;", "getScreen", "()Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionScreenDetails;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "", "other", "hashCode", "", "toString", "Type", "zencoder"}
)
public final class CreateWorkflowTransitionDetails {
   @NotNull
   private final String to;
   @NotNull
   private final Type type;
   @Nullable
   private final String description;
   @Nullable
   private final List<String> from;
   @Nullable
   private final String NAME;
   @Nullable
   private final Map<String, String> properties;
   @Nullable
   private final CreateWorkflowTransitionRulesDetails rules;
   @Nullable
   private final CreateWorkflowTransitionScreenDetails screen;

   public CreateWorkflowTransitionDetails(@NotNull String to, @NotNull Type type, @Nullable String description, @Nullable List<String> from, @Nullable String NAME, @Nullable Map<String, String> properties, @Nullable CreateWorkflowTransitionRulesDetails rules, @Nullable CreateWorkflowTransitionScreenDetails screen) {
      Intrinsics.checkNotNullParameter(to, "");
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.to = to;
      this.type = type;
      this.description = description;
      this.from = from;
      this.NAME = NAME;
      this.properties = properties;
      this.rules = rules;
      this.screen = screen;
   }

   @JsonProperty("to")
   @NotNull
   public final String getTo() {
      return this.to;
   }

   @JsonProperty("type")
   @NotNull
   public final Type getType() {
      return this.type;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("from")
   @Nullable
   public final List<String> getFrom() {
      return this.from;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("properties")
   @Nullable
   public final Map<String, String> getProperties() {
      return this.properties;
   }

   @JsonProperty("rules")
   @Nullable
   public final CreateWorkflowTransitionRulesDetails getRules() {
      return this.rules;
   }

   @JsonProperty("screen")
   @Nullable
   public final CreateWorkflowTransitionScreenDetails getScreen() {
      return this.screen;
   }

   @NotNull
   public final String component1() {
      return this.to;
   }

   @NotNull
   public final Type component2() {
      return this.type;
   }

   @Nullable
   public final String component3() {
      return this.description;
   }

   @Nullable
   public final List<String> component4() {
      return this.from;
   }

   @Nullable
   public final String component5() {
      return this.NAME;
   }

   @Nullable
   public final Map<String, String> component6() {
      return this.properties;
   }

   @Nullable
   public final CreateWorkflowTransitionRulesDetails component7() {
      return this.rules;
   }

   @Nullable
   public final CreateWorkflowTransitionScreenDetails component8() {
      return this.screen;
   }

   @NotNull
   public final CreateWorkflowTransitionDetails copy(@NotNull String to, @NotNull Type type, @Nullable String description, @Nullable List<String> from, @Nullable String NAME, @Nullable Map<String, String> properties, @Nullable CreateWorkflowTransitionRulesDetails rules, @Nullable CreateWorkflowTransitionScreenDetails screen) {
      Intrinsics.checkNotNullParameter(to, "");
      Intrinsics.checkNotNullParameter(type, "");
      return new CreateWorkflowTransitionDetails(to, type, description, from, NAME, properties, rules, screen);
   }

   @NotNull
   public String toString() {
      return "CreateWorkflowTransitionDetails(to=" + this.to + ", type=" + this.type + ", description=" + this.description + ", from=" + this.from + ", NAME=" + this.NAME + ", properties=" + this.properties + ", rules=" + this.rules + ", screen=" + this.screen + ")";
   }

   public int hashCode() {
      int var1 = this.to.hashCode();
      var1 = var1 * 31 + this.type.hashCode();
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.from == null ? 0 : this.from.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.properties == null ? 0 : this.properties.hashCode());
      var1 = var1 * 31 + (this.rules == null ? 0 : this.rules.hashCode());
      var1 = var1 * 31 + (this.screen == null ? 0 : this.screen.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CreateWorkflowTransitionDetails)) {
         return false;
      } else {
         CreateWorkflowTransitionDetails var2 = (CreateWorkflowTransitionDetails)other;
         if (!Intrinsics.areEqual(this.to, var2.to)) {
            return false;
         } else if (this.type != var2.type) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.from, var2.from)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else if (!Intrinsics.areEqual(this.properties, var2.properties)) {
            return false;
         } else if (!Intrinsics.areEqual(this.rules, var2.rules)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.screen, var2.screen);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"},
      d2 = {"Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionDetails$Type;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "global", "initial", "directed", "zencoder"}
   )
   public static enum Type {
      @NotNull
      private final String value;
      @JsonProperty("global")
      global("global"),
      @JsonProperty("initial")
      initial("initial"),
      @JsonProperty("directed")
      directed("directed");

      private Type(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Type> getEntries() {
         return $ENTRIES;
      }
   }
}
