package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010$\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0016\b\u0002\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\u0017\u0010\r\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0005H\u00c6\u0003J+\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0016\b\u0002\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR!\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionRule;", "", "type", "", "configuration", "", "<init>", "(Ljava/lang/String;Ljava/util/Map;)V", "getType", "()Ljava/lang/String;", "getConfiguration", "()Ljava/util/Map;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CreateWorkflowTransitionRule {
   @NotNull
   private final String type;
   @Nullable
   private final Map<String, Object> configuration;

   public CreateWorkflowTransitionRule(@NotNull String type, @Nullable Map<String, ? extends Object> configuration) {
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.type = type;
      this.configuration = configuration;
   }

   @JsonProperty("type")
   @NotNull
   public final String getType() {
      return this.type;
   }

   @JsonProperty("configuration")
   @Nullable
   public final Map<String, Object> getConfiguration() {
      return this.configuration;
   }

   @NotNull
   public final String component1() {
      return this.type;
   }

   @Nullable
   public final Map<String, Object> component2() {
      return this.configuration;
   }

   @NotNull
   public final CreateWorkflowTransitionRule copy(@NotNull String type, @Nullable Map<String, ? extends Object> configuration) {
      Intrinsics.checkNotNullParameter(type, "");
      return new CreateWorkflowTransitionRule(type, configuration);
   }

   @NotNull
   public String toString() {
      return "CreateWorkflowTransitionRule(type=" + this.type + ", configuration=" + this.configuration + ")";
   }

   public int hashCode() {
      int var1 = this.type.hashCode();
      var1 = var1 * 31 + (this.configuration == null ? 0 : this.configuration.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CreateWorkflowTransitionRule)) {
         return false;
      } else {
         CreateWorkflowTransitionRule var2 = (CreateWorkflowTransitionRule)other;
         if (!Intrinsics.areEqual(this.type, var2.type)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.configuration, var2.configuration);
         }
      }
   }
}
