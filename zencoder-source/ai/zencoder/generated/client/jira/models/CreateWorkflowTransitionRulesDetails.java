package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u00a2\u0006\u0004\b\b\u0010\tJ\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J9\u0010\u0012\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001b\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\r\u00a8\u0006\u001a"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionRulesDetails;", "", "conditions", "Lai/zencoder/generated/client/jira/models/CreateWorkflowCondition;", "postFunctions", "", "Lai/zencoder/generated/client/jira/models/CreateWorkflowTransitionRule;", "validators", "<init>", "(Lai/zencoder/generated/client/jira/models/CreateWorkflowCondition;Ljava/util/List;Ljava/util/List;)V", "getConditions", "()Lai/zencoder/generated/client/jira/models/CreateWorkflowCondition;", "getPostFunctions", "()Ljava/util/List;", "getValidators", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class CreateWorkflowTransitionRulesDetails {
   @Nullable
   private final CreateWorkflowCondition conditions;
   @Nullable
   private final List<CreateWorkflowTransitionRule> postFunctions;
   @Nullable
   private final List<CreateWorkflowTransitionRule> validators;

   public CreateWorkflowTransitionRulesDetails(@Nullable CreateWorkflowCondition conditions, @Nullable List<CreateWorkflowTransitionRule> postFunctions, @Nullable List<CreateWorkflowTransitionRule> validators) {
      this.conditions = conditions;
      this.postFunctions = postFunctions;
      this.validators = validators;
   }

   @JsonProperty("conditions")
   @Nullable
   public final CreateWorkflowCondition getConditions() {
      return this.conditions;
   }

   @JsonProperty("postFunctions")
   @Nullable
   public final List<CreateWorkflowTransitionRule> getPostFunctions() {
      return this.postFunctions;
   }

   @JsonProperty("validators")
   @Nullable
   public final List<CreateWorkflowTransitionRule> getValidators() {
      return this.validators;
   }

   @Nullable
   public final CreateWorkflowCondition component1() {
      return this.conditions;
   }

   @Nullable
   public final List<CreateWorkflowTransitionRule> component2() {
      return this.postFunctions;
   }

   @Nullable
   public final List<CreateWorkflowTransitionRule> component3() {
      return this.validators;
   }

   @NotNull
   public final CreateWorkflowTransitionRulesDetails copy(@Nullable CreateWorkflowCondition conditions, @Nullable List<CreateWorkflowTransitionRule> postFunctions, @Nullable List<CreateWorkflowTransitionRule> validators) {
      return new CreateWorkflowTransitionRulesDetails(conditions, postFunctions, validators);
   }

   @NotNull
   public String toString() {
      return "CreateWorkflowTransitionRulesDetails(conditions=" + this.conditions + ", postFunctions=" + this.postFunctions + ", validators=" + this.validators + ")";
   }

   public int hashCode() {
      int var1 = this.conditions == null ? 0 : this.conditions.hashCode();
      var1 = var1 * 31 + (this.postFunctions == null ? 0 : this.postFunctions.hashCode());
      var1 = var1 * 31 + (this.validators == null ? 0 : this.validators.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CreateWorkflowTransitionRulesDetails)) {
         return false;
      } else {
         CreateWorkflowTransitionRulesDetails var2 = (CreateWorkflowTransitionRulesDetails)other;
         if (!Intrinsics.areEqual(this.conditions, var2.conditions)) {
            return false;
         } else if (!Intrinsics.areEqual(this.postFunctions, var2.postFunctions)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.validators, var2.validators);
         }
      }
   }

   public CreateWorkflowTransitionRulesDetails() {
      this((CreateWorkflowCondition)null, (List)null, (List)null, 7, (DefaultConstructorMarker)null);
   }
}
