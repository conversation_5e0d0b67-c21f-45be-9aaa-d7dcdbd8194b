package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import java.util.List;
import kotlin.Metadata;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@JsonTypeInfo(
   use = Id.NAME,
   include = As.PROPERTY,
   property = "type",
   visible = true
)
@JsonSubTypes({@Type(
   value = CustomFieldContextDefaultValueDate.class,
   name = "datepicker"
), @Type(
   value = CustomFieldContextDefaultValueDateTime.class,
   name = "datetimepicker"
), @Type(
   value = CustomFieldContextDefaultValueFloat.class,
   name = "float"
), @Type(
   value = CustomFieldContextDefaultValueForgeDateTimeField.class,
   name = "forge.datetime"
), @Type(
   value = CustomFieldContextDefaultValueForgeGroupField.class,
   name = "forge.group"
), @Type(
   value = CustomFieldContextDefaultValueForgeMultiGroupField.class,
   name = "forge.group.list"
), @Type(
   value = CustomFieldContextDefaultValueForgeNumberField.class,
   name = "forge.number"
), @Type(
   value = CustomFieldContextDefaultValueForgeObjectField.class,
   name = "forge.object"
), @Type(
   value = CustomFieldContextDefaultValueForgeStringField.class,
   name = "forge.string"
), @Type(
   value = CustomFieldContextDefaultValueForgeMultiStringField.class,
   name = "forge.string.list"
), @Type(
   value = CustomFieldContextDefaultValueForgeUserField.class,
   name = "forge.user"
), @Type(
   value = CustomFieldContextDefaultValueForgeMultiUserField.class,
   name = "forge.user.list"
), @Type(
   value = CustomFieldContextDefaultValueMultipleGroupPicker.class,
   name = "grouppicker.multiple"
), @Type(
   value = CustomFieldContextDefaultValueSingleGroupPicker.class,
   name = "grouppicker.single"
), @Type(
   value = CustomFieldContextDefaultValueLabels.class,
   name = "labels"
), @Type(
   value = CustomFieldContextDefaultValueMultiUserPicker.class,
   name = "multi.user.select"
), @Type(
   value = CustomFieldContextDefaultValueCascadingOption.class,
   name = "option.cascading"
), @Type(
   value = CustomFieldContextDefaultValueMultipleOption.class,
   name = "option.multiple"
), @Type(
   value = CustomFieldContextDefaultValueSingleOption.class,
   name = "option.single"
), @Type(
   value = CustomFieldContextDefaultValueProject.class,
   name = "project"
), @Type(
   value = CustomFieldContextDefaultValueReadOnly.class,
   name = "readonly"
), @Type(
   value = CustomFieldContextSingleUserPickerDefaults.class,
   name = "single.user.select"
), @Type(
   value = CustomFieldContextDefaultValueTextArea.class,
   name = "textarea"
), @Type(
   value = CustomFieldContextDefaultValueTextField.class,
   name = "textfield"
), @Type(
   value = CustomFieldContextDefaultValueURL.class,
   name = "url"
), @Type(
   value = CustomFieldContextDefaultValueMultipleVersionPicker.class,
   name = "version.multiple"
), @Type(
   value = CustomFieldContextDefaultValueSingleVersionPicker.class,
   name = "version.single"
)})
@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u0006\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u000e\bg\u0018\u00002\u00020\u0001R\u0014\u0010\u0002\u001a\u00020\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0004\u0010\u0005R\u0014\u0010\u0006\u001a\u00020\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0007\u0010\u0005R\u0014\u0010\b\u001a\u00020\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\t\u0010\u0005R\u001a\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00030\u000b8gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\f\u0010\rR\u0014\u0010\u000e\u001a\u00020\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u000f\u0010\u0005R\u0014\u0010\u0010\u001a\u00020\u00118gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0012\u0010\u0013R\u001a\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00030\u000b8gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0015\u0010\rR\u0014\u0010\u0016\u001a\u00020\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0017\u0010\u0005R\u001a\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00030\u000b8gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0019\u0010\rR\u0014\u0010\u001a\u001a\u00020\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u001b\u0010\u0005R\u0014\u0010\u001c\u001a\u00020\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u001d\u0010\u0005R\u0014\u0010\u001e\u001a\u00020\u001f8gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b \u0010!R\u001a\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00030\u000b8gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b#\u0010\rR\u0014\u0010$\u001a\u00020\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b%\u0010\u0005R\u001a\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00030\u000b8gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b'\u0010\rR\u0016\u0010(\u001a\u0004\u0018\u00010\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b)\u0010\u0005R\u0016\u0010*\u001a\u0004\u0018\u00010\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b+\u0010\u0005R\u0016\u0010,\u001a\u0004\u0018\u00010-8gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b.\u0010/R\u0016\u00100\u001a\u0004\u0018\u00010\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b1\u0010\u0005R\u0016\u00102\u001a\u0004\u0018\u00010\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b3\u0010\u0005R\u0016\u00104\u001a\u0004\u0018\u00010\u00038gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b5\u0010\u0005R\u001c\u00106\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u000b8gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b7\u0010\rR\u0016\u00108\u001a\u0004\u0018\u00010\u00018gX\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b9\u0010:\u00a8\u0006;"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldContextDefaultValue;", "", "contextId", "", "getContextId", "()Ljava/lang/String;", "optionId", "getOptionId", "type", "getType", "optionIds", "", "getOptionIds", "()Ljava/util/List;", "accountId", "getAccountId", "userFilter", "Lai/zencoder/generated/client/jira/models/UserFilter;", "getUserFilter", "()Lai/zencoder/generated/client/jira/models/UserFilter;", "accountIds", "getAccountIds", "groupId", "getGroupId", "groupIds", "getGroupIds", "url", "getUrl", "projectId", "getProjectId", "number", "", "getNumber", "()D", "labels", "getLabels", "versionId", "getVersionId", "versionIds", "getVersionIds", "cascadingOptionId", "getCascadingOptionId", "date", "getDate", "useCurrent", "", "getUseCurrent", "()Ljava/lang/Boolean;", "dateTime", "getDateTime", "text", "getText", "versionOrder", "getVersionOrder", "propertyValues", "getPropertyValues", "object", "getObject", "()Ljava/lang/Object;", "zencoder"}
)
public interface CustomFieldContextDefaultValue {
   @JsonProperty("contextId")
   @NotNull
   String getContextId();

   @JsonProperty("optionId")
   @NotNull
   String getOptionId();

   @JsonProperty("type")
   @NotNull
   String getType();

   @JsonProperty("optionIds")
   @NotNull
   List<String> getOptionIds();

   @JsonProperty("accountId")
   @NotNull
   String getAccountId();

   @JsonProperty("userFilter")
   @NotNull
   UserFilter getUserFilter();

   @JsonProperty("accountIds")
   @NotNull
   List<String> getAccountIds();

   @JsonProperty("groupId")
   @NotNull
   String getGroupId();

   @JsonProperty("groupIds")
   @NotNull
   List<String> getGroupIds();

   @JsonProperty("url")
   @NotNull
   String getUrl();

   @JsonProperty("projectId")
   @NotNull
   String getProjectId();

   @JsonProperty("number")
   double getNumber();

   @JsonProperty("labels")
   @NotNull
   List<String> getLabels();

   @JsonProperty("versionId")
   @NotNull
   String getVersionId();

   @JsonProperty("versionIds")
   @NotNull
   List<String> getVersionIds();

   @JsonProperty("cascadingOptionId")
   @Nullable
   String getCascadingOptionId();

   @JsonProperty("date")
   @Nullable
   String getDate();

   @JsonProperty("useCurrent")
   @Nullable
   Boolean getUseCurrent();

   @JsonProperty("dateTime")
   @Nullable
   String getDateTime();

   @JsonProperty("text")
   @Nullable
   String getText();

   @JsonProperty("versionOrder")
   @Nullable
   String getVersionOrder();

   @JsonProperty("values")
   @Nullable
   List<String> getPropertyValues();

   @JsonProperty("object")
   @Nullable
   Object getObject();
}
