package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J3\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0013\u0010\u0005\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\nR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\n\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldContextDefaultValueCascadingOption;", "", "contextId", "", "optionId", "type", "cascadingOptionId", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getContextId", "()Ljava/lang/String;", "getOptionId", "getType", "getCascadingOptionId", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CustomFieldContextDefaultValueCascadingOption {
   @NotNull
   private final String contextId;
   @NotNull
   private final String optionId;
   @NotNull
   private final String type;
   @Nullable
   private final String cascadingOptionId;

   public CustomFieldContextDefaultValueCascadingOption(@NotNull String contextId, @NotNull String optionId, @NotNull String type, @Nullable String cascadingOptionId) {
      Intrinsics.checkNotNullParameter(contextId, "");
      Intrinsics.checkNotNullParameter(optionId, "");
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.contextId = contextId;
      this.optionId = optionId;
      this.type = type;
      this.cascadingOptionId = cascadingOptionId;
   }

   @JsonProperty("contextId")
   @NotNull
   public final String getContextId() {
      return this.contextId;
   }

   @JsonProperty("optionId")
   @NotNull
   public final String getOptionId() {
      return this.optionId;
   }

   @JsonProperty("type")
   @NotNull
   public final String getType() {
      return this.type;
   }

   @JsonProperty("cascadingOptionId")
   @Nullable
   public final String getCascadingOptionId() {
      return this.cascadingOptionId;
   }

   @NotNull
   public final String component1() {
      return this.contextId;
   }

   @NotNull
   public final String component2() {
      return this.optionId;
   }

   @NotNull
   public final String component3() {
      return this.type;
   }

   @Nullable
   public final String component4() {
      return this.cascadingOptionId;
   }

   @NotNull
   public final CustomFieldContextDefaultValueCascadingOption copy(@NotNull String contextId, @NotNull String optionId, @NotNull String type, @Nullable String cascadingOptionId) {
      Intrinsics.checkNotNullParameter(contextId, "");
      Intrinsics.checkNotNullParameter(optionId, "");
      Intrinsics.checkNotNullParameter(type, "");
      return new CustomFieldContextDefaultValueCascadingOption(contextId, optionId, type, cascadingOptionId);
   }

   @NotNull
   public String toString() {
      return "CustomFieldContextDefaultValueCascadingOption(contextId=" + this.contextId + ", optionId=" + this.optionId + ", type=" + this.type + ", cascadingOptionId=" + this.cascadingOptionId + ")";
   }

   public int hashCode() {
      int var1 = this.contextId.hashCode();
      var1 = var1 * 31 + this.optionId.hashCode();
      var1 = var1 * 31 + this.type.hashCode();
      var1 = var1 * 31 + (this.cascadingOptionId == null ? 0 : this.cascadingOptionId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CustomFieldContextDefaultValueCascadingOption)) {
         return false;
      } else {
         CustomFieldContextDefaultValueCascadingOption var2 = (CustomFieldContextDefaultValueCascadingOption)other;
         if (!Intrinsics.areEqual(this.contextId, var2.contextId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.optionId, var2.optionId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.type, var2.type)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.cascadingOptionId, var2.cascadingOptionId);
         }
      }
   }
}
