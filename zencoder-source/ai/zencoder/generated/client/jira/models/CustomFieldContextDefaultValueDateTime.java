package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0010\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ0\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0013J\u0013\u0010\u0014\u001a\u00020\u00062\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0017\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldContextDefaultValueDateTime;", "", "type", "", "dateTime", "useCurrent", "", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;)V", "getType", "()Ljava/lang/String;", "getDateTime", "getUseCurrent", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "component1", "component2", "component3", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;)Lai/zencoder/generated/client/jira/models/CustomFieldContextDefaultValueDateTime;", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CustomFieldContextDefaultValueDateTime {
   @NotNull
   private final String type;
   @Nullable
   private final String dateTime;
   @Nullable
   private final Boolean useCurrent;

   public CustomFieldContextDefaultValueDateTime(@NotNull String type, @Nullable String dateTime, @Nullable Boolean useCurrent) {
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.type = type;
      this.dateTime = dateTime;
      this.useCurrent = useCurrent;
   }

   @JsonProperty("type")
   @NotNull
   public final String getType() {
      return this.type;
   }

   @JsonProperty("dateTime")
   @Nullable
   public final String getDateTime() {
      return this.dateTime;
   }

   @JsonProperty("useCurrent")
   @Nullable
   public final Boolean getUseCurrent() {
      return this.useCurrent;
   }

   @NotNull
   public final String component1() {
      return this.type;
   }

   @Nullable
   public final String component2() {
      return this.dateTime;
   }

   @Nullable
   public final Boolean component3() {
      return this.useCurrent;
   }

   @NotNull
   public final CustomFieldContextDefaultValueDateTime copy(@NotNull String type, @Nullable String dateTime, @Nullable Boolean useCurrent) {
      Intrinsics.checkNotNullParameter(type, "");
      return new CustomFieldContextDefaultValueDateTime(type, dateTime, useCurrent);
   }

   @NotNull
   public String toString() {
      return "CustomFieldContextDefaultValueDateTime(type=" + this.type + ", dateTime=" + this.dateTime + ", useCurrent=" + this.useCurrent + ")";
   }

   public int hashCode() {
      int var1 = this.type.hashCode();
      var1 = var1 * 31 + (this.dateTime == null ? 0 : this.dateTime.hashCode());
      var1 = var1 * 31 + (this.useCurrent == null ? 0 : this.useCurrent.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CustomFieldContextDefaultValueDateTime)) {
         return false;
      } else {
         CustomFieldContextDefaultValueDateTime var2 = (CustomFieldContextDefaultValueDateTime)other;
         if (!Intrinsics.areEqual(this.type, var2.type)) {
            return false;
         } else if (!Intrinsics.areEqual(this.dateTime, var2.dateTime)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.useCurrent, var2.useCurrent);
         }
      }
   }
}
