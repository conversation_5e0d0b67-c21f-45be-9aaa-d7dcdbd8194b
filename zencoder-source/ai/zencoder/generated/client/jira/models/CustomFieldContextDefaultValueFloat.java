package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldContextDefaultValueFloat;", "", "number", "", "type", "", "<init>", "(DLjava/lang/String;)V", "getNumber", "()D", "getType", "()Ljava/lang/String;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CustomFieldContextDefaultValueFloat {
   private final double number;
   @NotNull
   private final String type;

   public CustomFieldContextDefaultValueFloat(double number, @NotNull String type) {
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.number = number;
      this.type = type;
   }

   @JsonProperty("number")
   public final double getNumber() {
      return this.number;
   }

   @JsonProperty("type")
   @NotNull
   public final String getType() {
      return this.type;
   }

   public final double component1() {
      return this.number;
   }

   @NotNull
   public final String component2() {
      return this.type;
   }

   @NotNull
   public final CustomFieldContextDefaultValueFloat copy(double number, @NotNull String type) {
      Intrinsics.checkNotNullParameter(type, "");
      return new CustomFieldContextDefaultValueFloat(number, type);
   }

   @NotNull
   public String toString() {
      return "CustomFieldContextDefaultValueFloat(number=" + this.number + ", type=" + this.type + ")";
   }

   public int hashCode() {
      int var1 = Double.hashCode(this.number);
      var1 = var1 * 31 + this.type.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CustomFieldContextDefaultValueFloat)) {
         return false;
      } else {
         CustomFieldContextDefaultValueFloat var2 = (CustomFieldContextDefaultValueFloat)other;
         if (Double.compare(this.number, var2.number) != 0) {
            return false;
         } else {
            return Intrinsics.areEqual(this.type, var2.type);
         }
      }
   }
}
