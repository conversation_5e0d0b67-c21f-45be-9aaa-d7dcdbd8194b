package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\"\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J-\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0006\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\n\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldContextDefaultValueForgeMultiGroupField;", "", "contextId", "", "groupIds", "", "type", "<init>", "(Ljava/lang/String;Ljava/util/Set;Ljava/lang/String;)V", "getContextId", "()Ljava/lang/String;", "getGroupIds", "()Ljava/util/Set;", "getType", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CustomFieldContextDefaultValueForgeMultiGroupField {
   @NotNull
   private final String contextId;
   @NotNull
   private final Set<String> groupIds;
   @NotNull
   private final String type;

   public CustomFieldContextDefaultValueForgeMultiGroupField(@NotNull String contextId, @NotNull Set<String> groupIds, @NotNull String type) {
      Intrinsics.checkNotNullParameter(contextId, "");
      Intrinsics.checkNotNullParameter(groupIds, "");
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.contextId = contextId;
      this.groupIds = groupIds;
      this.type = type;
   }

   @JsonProperty("contextId")
   @NotNull
   public final String getContextId() {
      return this.contextId;
   }

   @JsonProperty("groupIds")
   @NotNull
   public final Set<String> getGroupIds() {
      return this.groupIds;
   }

   @JsonProperty("type")
   @NotNull
   public final String getType() {
      return this.type;
   }

   @NotNull
   public final String component1() {
      return this.contextId;
   }

   @NotNull
   public final Set<String> component2() {
      return this.groupIds;
   }

   @NotNull
   public final String component3() {
      return this.type;
   }

   @NotNull
   public final CustomFieldContextDefaultValueForgeMultiGroupField copy(@NotNull String contextId, @NotNull Set<String> groupIds, @NotNull String type) {
      Intrinsics.checkNotNullParameter(contextId, "");
      Intrinsics.checkNotNullParameter(groupIds, "");
      Intrinsics.checkNotNullParameter(type, "");
      return new CustomFieldContextDefaultValueForgeMultiGroupField(contextId, groupIds, type);
   }

   @NotNull
   public String toString() {
      return "CustomFieldContextDefaultValueForgeMultiGroupField(contextId=" + this.contextId + ", groupIds=" + this.groupIds + ", type=" + this.type + ")";
   }

   public int hashCode() {
      int var1 = this.contextId.hashCode();
      var1 = var1 * 31 + this.groupIds.hashCode();
      var1 = var1 * 31 + this.type.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CustomFieldContextDefaultValueForgeMultiGroupField)) {
         return false;
      } else {
         CustomFieldContextDefaultValueForgeMultiGroupField var2 = (CustomFieldContextDefaultValueForgeMultiGroupField)other;
         if (!Intrinsics.areEqual(this.contextId, var2.contextId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.groupIds, var2.groupIds)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.type, var2.type);
         }
      }
   }
}
