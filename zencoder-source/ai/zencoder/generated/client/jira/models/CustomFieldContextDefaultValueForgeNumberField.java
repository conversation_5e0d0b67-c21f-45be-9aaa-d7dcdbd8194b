package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J'\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0006\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\n\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldContextDefaultValueForgeNumberField;", "", "contextId", "", "number", "", "type", "<init>", "(Ljava/lang/String;DLjava/lang/String;)V", "getContextId", "()Ljava/lang/String;", "getNumber", "()D", "getType", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CustomFieldContextDefaultValueForgeNumberField {
   @NotNull
   private final String contextId;
   private final double number;
   @NotNull
   private final String type;

   public CustomFieldContextDefaultValueForgeNumberField(@NotNull String contextId, double number, @NotNull String type) {
      Intrinsics.checkNotNullParameter(contextId, "");
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.contextId = contextId;
      this.number = number;
      this.type = type;
   }

   @JsonProperty("contextId")
   @NotNull
   public final String getContextId() {
      return this.contextId;
   }

   @JsonProperty("number")
   public final double getNumber() {
      return this.number;
   }

   @JsonProperty("type")
   @NotNull
   public final String getType() {
      return this.type;
   }

   @NotNull
   public final String component1() {
      return this.contextId;
   }

   public final double component2() {
      return this.number;
   }

   @NotNull
   public final String component3() {
      return this.type;
   }

   @NotNull
   public final CustomFieldContextDefaultValueForgeNumberField copy(@NotNull String contextId, double number, @NotNull String type) {
      Intrinsics.checkNotNullParameter(contextId, "");
      Intrinsics.checkNotNullParameter(type, "");
      return new CustomFieldContextDefaultValueForgeNumberField(contextId, number, type);
   }

   @NotNull
   public String toString() {
      return "CustomFieldContextDefaultValueForgeNumberField(contextId=" + this.contextId + ", number=" + this.number + ", type=" + this.type + ")";
   }

   public int hashCode() {
      int var1 = this.contextId.hashCode();
      var1 = var1 * 31 + Double.hashCode(this.number);
      var1 = var1 * 31 + this.type.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CustomFieldContextDefaultValueForgeNumberField)) {
         return false;
      } else {
         CustomFieldContextDefaultValueForgeNumberField var2 = (CustomFieldContextDefaultValueForgeNumberField)other;
         if (!Intrinsics.areEqual(this.contextId, var2.contextId)) {
            return false;
         } else if (Double.compare(this.number, var2.number) != 0) {
            return false;
         } else {
            return Intrinsics.areEqual(this.type, var2.type);
         }
      }
   }
}
