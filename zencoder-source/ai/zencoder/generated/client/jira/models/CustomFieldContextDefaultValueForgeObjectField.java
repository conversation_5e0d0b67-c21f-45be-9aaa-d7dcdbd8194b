package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001b\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0001\u00a2\u0006\u0004\b\u0005\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J\u001f\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0001H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0014"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldContextDefaultValueForgeObjectField;", "", "type", "", "object", "<init>", "(Ljava/lang/String;Ljava/lang/Object;)V", "getType", "()Ljava/lang/String;", "getObject", "()Ljava/lang/Object;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CustomFieldContextDefaultValueForgeObjectField {
   @NotNull
   private final String type;
   @Nullable
   private final Object object;

   public CustomFieldContextDefaultValueForgeObjectField(@NotNull String type, @Nullable Object object) {
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.type = type;
      this.object = object;
   }

   @JsonProperty("type")
   @NotNull
   public final String getType() {
      return this.type;
   }

   @JsonProperty("object")
   @Nullable
   public final Object getObject() {
      return this.object;
   }

   @NotNull
   public final String component1() {
      return this.type;
   }

   @Nullable
   public final Object component2() {
      return this.object;
   }

   @NotNull
   public final CustomFieldContextDefaultValueForgeObjectField copy(@NotNull String type, @Nullable Object object) {
      Intrinsics.checkNotNullParameter(type, "");
      return new CustomFieldContextDefaultValueForgeObjectField(type, object);
   }

   @NotNull
   public String toString() {
      return "CustomFieldContextDefaultValueForgeObjectField(type=" + this.type + ", object=" + this.object + ")";
   }

   public int hashCode() {
      int var1 = this.type.hashCode();
      var1 = var1 * 31 + (this.object == null ? 0 : this.object.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CustomFieldContextDefaultValueForgeObjectField)) {
         return false;
      } else {
         CustomFieldContextDefaultValueForgeObjectField var2 = (CustomFieldContextDefaultValueForgeObjectField)other;
         if (!Intrinsics.areEqual(this.type, var2.type)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.object, var2.object);
         }
      }
   }
}
