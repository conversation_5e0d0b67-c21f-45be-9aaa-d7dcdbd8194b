package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J'\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0013\u0010\u0005\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\t\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldContextDefaultValueURL;", "", "contextId", "", "type", "url", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getContextId", "()Ljava/lang/String;", "getType", "getUrl", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CustomFieldContextDefaultValueURL {
   @NotNull
   private final String contextId;
   @NotNull
   private final String type;
   @NotNull
   private final String url;

   public CustomFieldContextDefaultValueURL(@NotNull String contextId, @NotNull String type, @NotNull String url) {
      Intrinsics.checkNotNullParameter(contextId, "");
      Intrinsics.checkNotNullParameter(type, "");
      Intrinsics.checkNotNullParameter(url, "");
      super();
      this.contextId = contextId;
      this.type = type;
      this.url = url;
   }

   @JsonProperty("contextId")
   @NotNull
   public final String getContextId() {
      return this.contextId;
   }

   @JsonProperty("type")
   @NotNull
   public final String getType() {
      return this.type;
   }

   @JsonProperty("url")
   @NotNull
   public final String getUrl() {
      return this.url;
   }

   @NotNull
   public final String component1() {
      return this.contextId;
   }

   @NotNull
   public final String component2() {
      return this.type;
   }

   @NotNull
   public final String component3() {
      return this.url;
   }

   @NotNull
   public final CustomFieldContextDefaultValueURL copy(@NotNull String contextId, @NotNull String type, @NotNull String url) {
      Intrinsics.checkNotNullParameter(contextId, "");
      Intrinsics.checkNotNullParameter(type, "");
      Intrinsics.checkNotNullParameter(url, "");
      return new CustomFieldContextDefaultValueURL(contextId, type, url);
   }

   @NotNull
   public String toString() {
      return "CustomFieldContextDefaultValueURL(contextId=" + this.contextId + ", type=" + this.type + ", url=" + this.url + ")";
   }

   public int hashCode() {
      int var1 = this.contextId.hashCode();
      var1 = var1 * 31 + this.type.hashCode();
      var1 = var1 * 31 + this.url.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CustomFieldContextDefaultValueURL)) {
         return false;
      } else {
         CustomFieldContextDefaultValueURL var2 = (CustomFieldContextDefaultValueURL)other;
         if (!Intrinsics.areEqual(this.contextId, var2.contextId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.type, var2.type)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.url, var2.url);
         }
      }
   }
}
