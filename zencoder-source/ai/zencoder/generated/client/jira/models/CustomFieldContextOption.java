package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0012\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J3\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00032\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0013\u0010\u0006\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\r\u00a8\u0006\u001a"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldContextOption;", "", "disabled", "", "id", "", "value", "optionId", "<init>", "(ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getDisabled", "()Z", "getId", "()Ljava/lang/String;", "getValue", "getOptionId", "component1", "component2", "component3", "component4", "copy", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CustomFieldContextOption {
   private final boolean disabled;
   @NotNull
   private final String id;
   @NotNull
   private final String value;
   @Nullable
   private final String optionId;

   public CustomFieldContextOption(boolean disabled, @NotNull String id, @NotNull String value, @Nullable String optionId) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(value, "");
      super();
      this.disabled = disabled;
      this.id = id;
      this.value = value;
      this.optionId = optionId;
   }

   @JsonProperty("disabled")
   public final boolean getDisabled() {
      return this.disabled;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("value")
   @NotNull
   public final String getValue() {
      return this.value;
   }

   @JsonProperty("optionId")
   @Nullable
   public final String getOptionId() {
      return this.optionId;
   }

   public final boolean component1() {
      return this.disabled;
   }

   @NotNull
   public final String component2() {
      return this.id;
   }

   @NotNull
   public final String component3() {
      return this.value;
   }

   @Nullable
   public final String component4() {
      return this.optionId;
   }

   @NotNull
   public final CustomFieldContextOption copy(boolean disabled, @NotNull String id, @NotNull String value, @Nullable String optionId) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(value, "");
      return new CustomFieldContextOption(disabled, id, value, optionId);
   }

   @NotNull
   public String toString() {
      return "CustomFieldContextOption(disabled=" + this.disabled + ", id=" + this.id + ", value=" + this.value + ", optionId=" + this.optionId + ")";
   }

   public int hashCode() {
      int var1 = Boolean.hashCode(this.disabled);
      var1 = var1 * 31 + this.id.hashCode();
      var1 = var1 * 31 + this.value.hashCode();
      var1 = var1 * 31 + (this.optionId == null ? 0 : this.optionId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CustomFieldContextOption)) {
         return false;
      } else {
         CustomFieldContextOption var2 = (CustomFieldContextOption)other;
         if (this.disabled != var2.disabled) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.value, var2.value)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.optionId, var2.optionId);
         }
      }
   }
}
