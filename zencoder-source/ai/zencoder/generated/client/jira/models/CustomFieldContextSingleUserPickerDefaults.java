package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0007H\u00c6\u0003J1\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0013\u0010\u0005\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000bR\u0013\u0010\u0006\u001a\u00020\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001b"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldContextSingleUserPickerDefaults;", "", "accountId", "", "contextId", "type", "userFilter", "Lai/zencoder/generated/client/jira/models/UserFilter;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/UserFilter;)V", "getAccountId", "()Ljava/lang/String;", "getContextId", "getType", "getUserFilter", "()Lai/zencoder/generated/client/jira/models/UserFilter;", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CustomFieldContextSingleUserPickerDefaults {
   @NotNull
   private final String accountId;
   @NotNull
   private final String contextId;
   @NotNull
   private final String type;
   @NotNull
   private final UserFilter userFilter;

   public CustomFieldContextSingleUserPickerDefaults(@NotNull String accountId, @NotNull String contextId, @NotNull String type, @NotNull UserFilter userFilter) {
      Intrinsics.checkNotNullParameter(accountId, "");
      Intrinsics.checkNotNullParameter(contextId, "");
      Intrinsics.checkNotNullParameter(type, "");
      Intrinsics.checkNotNullParameter(userFilter, "");
      super();
      this.accountId = accountId;
      this.contextId = contextId;
      this.type = type;
      this.userFilter = userFilter;
   }

   @JsonProperty("accountId")
   @NotNull
   public final String getAccountId() {
      return this.accountId;
   }

   @JsonProperty("contextId")
   @NotNull
   public final String getContextId() {
      return this.contextId;
   }

   @JsonProperty("type")
   @NotNull
   public final String getType() {
      return this.type;
   }

   @JsonProperty("userFilter")
   @NotNull
   public final UserFilter getUserFilter() {
      return this.userFilter;
   }

   @NotNull
   public final String component1() {
      return this.accountId;
   }

   @NotNull
   public final String component2() {
      return this.contextId;
   }

   @NotNull
   public final String component3() {
      return this.type;
   }

   @NotNull
   public final UserFilter component4() {
      return this.userFilter;
   }

   @NotNull
   public final CustomFieldContextSingleUserPickerDefaults copy(@NotNull String accountId, @NotNull String contextId, @NotNull String type, @NotNull UserFilter userFilter) {
      Intrinsics.checkNotNullParameter(accountId, "");
      Intrinsics.checkNotNullParameter(contextId, "");
      Intrinsics.checkNotNullParameter(type, "");
      Intrinsics.checkNotNullParameter(userFilter, "");
      return new CustomFieldContextSingleUserPickerDefaults(accountId, contextId, type, userFilter);
   }

   @NotNull
   public String toString() {
      return "CustomFieldContextSingleUserPickerDefaults(accountId=" + this.accountId + ", contextId=" + this.contextId + ", type=" + this.type + ", userFilter=" + this.userFilter + ")";
   }

   public int hashCode() {
      int var1 = this.accountId.hashCode();
      var1 = var1 * 31 + this.contextId.hashCode();
      var1 = var1 * 31 + this.type.hashCode();
      var1 = var1 * 31 + this.userFilter.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CustomFieldContextSingleUserPickerDefaults)) {
         return false;
      } else {
         CustomFieldContextSingleUserPickerDefaults var2 = (CustomFieldContextSingleUserPickerDefaults)other;
         if (!Intrinsics.areEqual(this.accountId, var2.accountId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.contextId, var2.contextId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.type, var2.type)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.userFilter, var2.userFilter);
         }
      }
   }
}
