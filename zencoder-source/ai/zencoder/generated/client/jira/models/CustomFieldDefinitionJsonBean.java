package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u001bB3\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J7\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000bR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldDefinitionJsonBean;", "", "type", "", "description", "NAME", "searcherKey", "Lai/zencoder/generated/client/jira/models/CustomFieldDefinitionJsonBean$SearcherKey;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/CustomFieldDefinitionJsonBean$SearcherKey;)V", "getType", "()Ljava/lang/String;", "getDescription", "getNAME", "getSearcherKey", "()Lai/zencoder/generated/client/jira/models/CustomFieldDefinitionJsonBean$SearcherKey;", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "SearcherKey", "zencoder"}
)
public final class CustomFieldDefinitionJsonBean {
   @NotNull
   private final String type;
   @Nullable
   private final String description;
   @Nullable
   private final String NAME;
   @Nullable
   private final SearcherKey searcherKey;

   public CustomFieldDefinitionJsonBean(@NotNull String type, @Nullable String description, @Nullable String NAME, @Nullable SearcherKey searcherKey) {
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.type = type;
      this.description = description;
      this.NAME = NAME;
      this.searcherKey = searcherKey;
   }

   @JsonProperty("type")
   @NotNull
   public final String getType() {
      return this.type;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("searcherKey")
   @Nullable
   public final SearcherKey getSearcherKey() {
      return this.searcherKey;
   }

   @NotNull
   public final String component1() {
      return this.type;
   }

   @Nullable
   public final String component2() {
      return this.description;
   }

   @Nullable
   public final String component3() {
      return this.NAME;
   }

   @Nullable
   public final SearcherKey component4() {
      return this.searcherKey;
   }

   @NotNull
   public final CustomFieldDefinitionJsonBean copy(@NotNull String type, @Nullable String description, @Nullable String NAME, @Nullable SearcherKey searcherKey) {
      Intrinsics.checkNotNullParameter(type, "");
      return new CustomFieldDefinitionJsonBean(type, description, NAME, searcherKey);
   }

   @NotNull
   public String toString() {
      return "CustomFieldDefinitionJsonBean(type=" + this.type + ", description=" + this.description + ", NAME=" + this.NAME + ", searcherKey=" + this.searcherKey + ")";
   }

   public int hashCode() {
      int var1 = this.type.hashCode();
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.searcherKey == null ? 0 : this.searcherKey.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CustomFieldDefinitionJsonBean)) {
         return false;
      } else {
         CustomFieldDefinitionJsonBean var2 = (CustomFieldDefinitionJsonBean)other;
         if (!Intrinsics.areEqual(this.type, var2.type)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return this.searcherKey == var2.searcherKey;
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0012\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014\u00a8\u0006\u0015"},
      d2 = {"Lai/zencoder/generated/client/jira/models/CustomFieldDefinitionJsonBean$SearcherKey;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonCascadingselectsearcher", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonDaterange", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonDatetimerange", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonExactnumber", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonExacttextsearcher", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonGrouppickersearcher", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonLabelsearcher", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonMultiselectsearcher", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonNumberrange", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonProjectsearcher", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonTextsearcher", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonUserpickergroupsearcher", "comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonVersionsearcher", "zencoder"}
   )
   public static enum SearcherKey {
      @NotNull
      private final String value;
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:cascadingselectsearcher")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonCascadingselectsearcher("com.atlassian.jira.plugin.system.customfieldtypes:cascadingselectsearcher"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:daterange")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonDaterange("com.atlassian.jira.plugin.system.customfieldtypes:daterange"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:datetimerange")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonDatetimerange("com.atlassian.jira.plugin.system.customfieldtypes:datetimerange"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:exactnumber")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonExactnumber("com.atlassian.jira.plugin.system.customfieldtypes:exactnumber"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:exacttextsearcher")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonExacttextsearcher("com.atlassian.jira.plugin.system.customfieldtypes:exacttextsearcher"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:grouppickersearcher")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonGrouppickersearcher("com.atlassian.jira.plugin.system.customfieldtypes:grouppickersearcher"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:labelsearcher")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonLabelsearcher("com.atlassian.jira.plugin.system.customfieldtypes:labelsearcher"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:multiselectsearcher")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonMultiselectsearcher("com.atlassian.jira.plugin.system.customfieldtypes:multiselectsearcher"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:numberrange")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonNumberrange("com.atlassian.jira.plugin.system.customfieldtypes:numberrange"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:projectsearcher")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonProjectsearcher("com.atlassian.jira.plugin.system.customfieldtypes:projectsearcher"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:textsearcher")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonTextsearcher("com.atlassian.jira.plugin.system.customfieldtypes:textsearcher"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:userpickergroupsearcher")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonUserpickergroupsearcher("com.atlassian.jira.plugin.system.customfieldtypes:userpickergroupsearcher"),
      @JsonProperty("com.atlassian.jira.plugin.system.customfieldtypes:versionsearcher")
      comPeriodAtlassianPeriodJiraPeriodPluginPeriodSystemPeriodCustomfieldtypesColonVersionsearcher("com.atlassian.jira.plugin.system.customfieldtypes:versionsearcher");

      private SearcherKey(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<SearcherKey> getEntries() {
         return $ENTRIES;
      }
   }
}
