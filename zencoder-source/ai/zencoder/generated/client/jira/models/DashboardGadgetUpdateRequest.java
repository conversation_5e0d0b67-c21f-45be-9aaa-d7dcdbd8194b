package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J-\u0010\u0011\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\n\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/DashboardGadgetUpdateRequest;", "", "color", "", "position", "Lai/zencoder/generated/client/jira/models/DashboardGadgetPosition;", "title", "<init>", "(Ljava/lang/String;Lai/zencoder/generated/client/jira/models/DashboardGadgetPosition;Ljava/lang/String;)V", "getColor", "()Ljava/lang/String;", "getPosition", "()Lai/zencoder/generated/client/jira/models/DashboardGadgetPosition;", "getTitle", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class DashboardGadgetUpdateRequest {
   @Nullable
   private final String color;
   @Nullable
   private final DashboardGadgetPosition position;
   @Nullable
   private final String title;

   public DashboardGadgetUpdateRequest(@Nullable String color, @Nullable DashboardGadgetPosition position, @Nullable String title) {
      this.color = color;
      this.position = position;
      this.title = title;
   }

   @JsonProperty("color")
   @Nullable
   public final String getColor() {
      return this.color;
   }

   @JsonProperty("position")
   @Nullable
   public final DashboardGadgetPosition getPosition() {
      return this.position;
   }

   @JsonProperty("title")
   @Nullable
   public final String getTitle() {
      return this.title;
   }

   @Nullable
   public final String component1() {
      return this.color;
   }

   @Nullable
   public final DashboardGadgetPosition component2() {
      return this.position;
   }

   @Nullable
   public final String component3() {
      return this.title;
   }

   @NotNull
   public final DashboardGadgetUpdateRequest copy(@Nullable String color, @Nullable DashboardGadgetPosition position, @Nullable String title) {
      return new DashboardGadgetUpdateRequest(color, position, title);
   }

   @NotNull
   public String toString() {
      return "DashboardGadgetUpdateRequest(color=" + this.color + ", position=" + this.position + ", title=" + this.title + ")";
   }

   public int hashCode() {
      int var1 = this.color == null ? 0 : this.color.hashCode();
      var1 = var1 * 31 + (this.position == null ? 0 : this.position.hashCode());
      var1 = var1 * 31 + (this.title == null ? 0 : this.title.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof DashboardGadgetUpdateRequest)) {
         return false;
      } else {
         DashboardGadgetUpdateRequest var2 = (DashboardGadgetUpdateRequest)other;
         if (!Intrinsics.areEqual(this.color, var2.color)) {
            return false;
         } else if (!Intrinsics.areEqual(this.position, var2.position)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.title, var2.title);
         }
      }
   }

   public DashboardGadgetUpdateRequest() {
      this((String)null, (DashboardGadgetPosition)null, (String)null, 7, (DefaultConstructorMarker)null);
   }
}
