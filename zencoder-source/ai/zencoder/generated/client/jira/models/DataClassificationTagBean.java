package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0016\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BS\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0004\b\u000b\u0010\fJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u001d\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0015J^\u0010\u001e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\nH\u00c6\u0001\u00a2\u0006\u0002\u0010\u001fJ\u0013\u0010 \u001a\u00020!2\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020\nH\u00d6\u0001J\t\u0010$\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000eR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000eR\u0015\u0010\b\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000eR\u0017\u0010\t\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\n\n\u0002\u0010\u0016\u001a\u0004\b\u0014\u0010\u0015\u00a8\u0006%"},
   d2 = {"Lai/zencoder/generated/client/jira/models/DataClassificationTagBean;", "", "id", "", "status", "color", "description", "guideline", "NAME", "rank", "", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)V", "getId", "()Ljava/lang/String;", "getStatus", "getColor", "getDescription", "getGuideline", "getNAME", "getRank", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Lai/zencoder/generated/client/jira/models/DataClassificationTagBean;", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class DataClassificationTagBean {
   @NotNull
   private final String id;
   @NotNull
   private final String status;
   @Nullable
   private final String color;
   @Nullable
   private final String description;
   @Nullable
   private final String guideline;
   @Nullable
   private final String NAME;
   @Nullable
   private final Integer rank;

   public DataClassificationTagBean(@NotNull String id, @NotNull String status, @Nullable String color, @Nullable String description, @Nullable String guideline, @Nullable String NAME, @Nullable Integer rank) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(status, "");
      super();
      this.id = id;
      this.status = status;
      this.color = color;
      this.description = description;
      this.guideline = guideline;
      this.NAME = NAME;
      this.rank = rank;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("status")
   @NotNull
   public final String getStatus() {
      return this.status;
   }

   @JsonProperty("color")
   @Nullable
   public final String getColor() {
      return this.color;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("guideline")
   @Nullable
   public final String getGuideline() {
      return this.guideline;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("rank")
   @Nullable
   public final Integer getRank() {
      return this.rank;
   }

   @NotNull
   public final String component1() {
      return this.id;
   }

   @NotNull
   public final String component2() {
      return this.status;
   }

   @Nullable
   public final String component3() {
      return this.color;
   }

   @Nullable
   public final String component4() {
      return this.description;
   }

   @Nullable
   public final String component5() {
      return this.guideline;
   }

   @Nullable
   public final String component6() {
      return this.NAME;
   }

   @Nullable
   public final Integer component7() {
      return this.rank;
   }

   @NotNull
   public final DataClassificationTagBean copy(@NotNull String id, @NotNull String status, @Nullable String color, @Nullable String description, @Nullable String guideline, @Nullable String NAME, @Nullable Integer rank) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(status, "");
      return new DataClassificationTagBean(id, status, color, description, guideline, NAME, rank);
   }

   @NotNull
   public String toString() {
      return "DataClassificationTagBean(id=" + this.id + ", status=" + this.status + ", color=" + this.color + ", description=" + this.description + ", guideline=" + this.guideline + ", NAME=" + this.NAME + ", rank=" + this.rank + ")";
   }

   public int hashCode() {
      int var1 = this.id.hashCode();
      var1 = var1 * 31 + this.status.hashCode();
      var1 = var1 * 31 + (this.color == null ? 0 : this.color.hashCode());
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.guideline == null ? 0 : this.guideline.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.rank == null ? 0 : this.rank.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof DataClassificationTagBean)) {
         return false;
      } else {
         DataClassificationTagBean var2 = (DataClassificationTagBean)other;
         if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.status, var2.status)) {
            return false;
         } else if (!Intrinsics.areEqual(this.color, var2.color)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.guideline, var2.guideline)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.rank, var2.rank);
         }
      }
   }
}
