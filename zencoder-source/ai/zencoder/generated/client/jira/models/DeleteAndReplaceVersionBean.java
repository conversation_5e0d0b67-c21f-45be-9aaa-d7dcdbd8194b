package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\b\u0010\tJ\u0011\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ8\u0010\u0013\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0014J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0017\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\f\u0010\rR\u0017\u0010\u0007\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\u000f\u0010\r\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/jira/models/DeleteAndReplaceVersionBean;", "", "customFieldReplacementList", "", "Lai/zencoder/generated/client/jira/models/CustomFieldReplacement;", "moveAffectedIssuesTo", "", "moveFixIssuesTo", "<init>", "(Ljava/util/List;Ljava/lang/Long;Ljava/lang/Long;)V", "getCustomFieldReplacementList", "()Ljava/util/List;", "getMoveAffectedIssuesTo", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getMoveFixIssuesTo", "component1", "component2", "component3", "copy", "(Ljava/util/List;Ljava/lang/Long;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/DeleteAndReplaceVersionBean;", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class DeleteAndReplaceVersionBean {
   @Nullable
   private final List<CustomFieldReplacement> customFieldReplacementList;
   @Nullable
   private final Long moveAffectedIssuesTo;
   @Nullable
   private final Long moveFixIssuesTo;

   public DeleteAndReplaceVersionBean(@Nullable List<CustomFieldReplacement> customFieldReplacementList, @Nullable Long moveAffectedIssuesTo, @Nullable Long moveFixIssuesTo) {
      this.customFieldReplacementList = customFieldReplacementList;
      this.moveAffectedIssuesTo = moveAffectedIssuesTo;
      this.moveFixIssuesTo = moveFixIssuesTo;
   }

   @JsonProperty("customFieldReplacementList")
   @Nullable
   public final List<CustomFieldReplacement> getCustomFieldReplacementList() {
      return this.customFieldReplacementList;
   }

   @JsonProperty("moveAffectedIssuesTo")
   @Nullable
   public final Long getMoveAffectedIssuesTo() {
      return this.moveAffectedIssuesTo;
   }

   @JsonProperty("moveFixIssuesTo")
   @Nullable
   public final Long getMoveFixIssuesTo() {
      return this.moveFixIssuesTo;
   }

   @Nullable
   public final List<CustomFieldReplacement> component1() {
      return this.customFieldReplacementList;
   }

   @Nullable
   public final Long component2() {
      return this.moveAffectedIssuesTo;
   }

   @Nullable
   public final Long component3() {
      return this.moveFixIssuesTo;
   }

   @NotNull
   public final DeleteAndReplaceVersionBean copy(@Nullable List<CustomFieldReplacement> customFieldReplacementList, @Nullable Long moveAffectedIssuesTo, @Nullable Long moveFixIssuesTo) {
      return new DeleteAndReplaceVersionBean(customFieldReplacementList, moveAffectedIssuesTo, moveFixIssuesTo);
   }

   @NotNull
   public String toString() {
      return "DeleteAndReplaceVersionBean(customFieldReplacementList=" + this.customFieldReplacementList + ", moveAffectedIssuesTo=" + this.moveAffectedIssuesTo + ", moveFixIssuesTo=" + this.moveFixIssuesTo + ")";
   }

   public int hashCode() {
      int var1 = this.customFieldReplacementList == null ? 0 : this.customFieldReplacementList.hashCode();
      var1 = var1 * 31 + (this.moveAffectedIssuesTo == null ? 0 : this.moveAffectedIssuesTo.hashCode());
      var1 = var1 * 31 + (this.moveFixIssuesTo == null ? 0 : this.moveFixIssuesTo.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof DeleteAndReplaceVersionBean)) {
         return false;
      } else {
         DeleteAndReplaceVersionBean var2 = (DeleteAndReplaceVersionBean)other;
         if (!Intrinsics.areEqual(this.customFieldReplacementList, var2.customFieldReplacementList)) {
            return false;
         } else if (!Intrinsics.areEqual(this.moveAffectedIssuesTo, var2.moveAffectedIssuesTo)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.moveFixIssuesTo, var2.moveFixIssuesTo);
         }
      }
   }

   public DeleteAndReplaceVersionBean() {
      this((List)null, (Long)null, (Long)null, 7, (DefaultConstructorMarker)null);
   }
}
