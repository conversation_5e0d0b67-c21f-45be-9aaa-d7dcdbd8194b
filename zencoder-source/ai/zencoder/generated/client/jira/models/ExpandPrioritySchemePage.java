package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004B+\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b\u00a2\u0006\u0004\b\n\u0010\u000bJ\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ\u0010\u0010\u0014\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0010J\u0010\u0010\u0015\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0010J2\u0010\u0016\u001a\u00020\u00002\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\bH\u00c6\u0001\u00a2\u0006\u0002\u0010\u0017J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u0006H\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u0002H\u00d6\u0001R\u0017\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\f\u0010\rR\u0017\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\n\n\u0002\u0010\u0011\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\t\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\n\n\u0002\u0010\u0011\u001a\u0004\b\u0012\u0010\u0010\u00a8\u0006\u001d"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ExpandPrioritySchemePage;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "maxResults", "", "startAt", "", "total", "<init>", "(Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;)V", "getMaxResults", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getStartAt", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getTotal", "component1", "component2", "component3", "copy", "(Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/ExpandPrioritySchemePage;", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class ExpandPrioritySchemePage extends HashMap<String, Object> {
   @Nullable
   private final Integer maxResults;
   @Nullable
   private final Long startAt;
   @Nullable
   private final Long total;

   public ExpandPrioritySchemePage(@Nullable Integer maxResults, @Nullable Long startAt, @Nullable Long total) {
      this.maxResults = maxResults;
      this.startAt = startAt;
      this.total = total;
   }

   @JsonProperty("maxResults")
   @Nullable
   public final Integer getMaxResults() {
      return this.maxResults;
   }

   @JsonProperty("startAt")
   @Nullable
   public final Long getStartAt() {
      return this.startAt;
   }

   @JsonProperty("total")
   @Nullable
   public final Long getTotal() {
      return this.total;
   }

   @Nullable
   public final Integer component1() {
      return this.maxResults;
   }

   @Nullable
   public final Long component2() {
      return this.startAt;
   }

   @Nullable
   public final Long component3() {
      return this.total;
   }

   @NotNull
   public final ExpandPrioritySchemePage copy(@Nullable Integer maxResults, @Nullable Long startAt, @Nullable Long total) {
      return new ExpandPrioritySchemePage(maxResults, startAt, total);
   }

   @NotNull
   public String toString() {
      return "ExpandPrioritySchemePage(maxResults=" + this.maxResults + ", startAt=" + this.startAt + ", total=" + this.total + ")";
   }

   public int hashCode() {
      int var1 = this.maxResults == null ? 0 : this.maxResults.hashCode();
      var1 = var1 * 31 + (this.startAt == null ? 0 : this.startAt.hashCode());
      var1 = var1 * 31 + (this.total == null ? 0 : this.total.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ExpandPrioritySchemePage)) {
         return false;
      } else {
         ExpandPrioritySchemePage var2 = (ExpandPrioritySchemePage)other;
         if (!Intrinsics.areEqual(this.maxResults, var2.maxResults)) {
            return false;
         } else if (!Intrinsics.areEqual(this.startAt, var2.startAt)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.total, var2.total);
         }
      }
   }

   public ExpandPrioritySchemePage() {
      this((Integer)null, (Long)null, (Long)null, 7, (DefaultConstructorMarker)null);
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
