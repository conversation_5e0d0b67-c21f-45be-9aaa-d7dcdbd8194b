package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.OffsetDateTime;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0016\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BO\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u000b\u0010\fJ\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0011J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003JV\u0010\u001d\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010\u001eJ\u0013\u0010\u001f\u001a\u00020 2\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\"\u001a\u00020#H\u00d6\u0001J\t\u0010$\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0017\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0010\u0010\u0011R\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000eR\u0015\u0010\b\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0015\u0010\n\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u000e\u00a8\u0006%"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ExportArchivedIssuesTaskProgressResponse;", "", "fileUrl", "", "payload", "progress", "", "status", "submittedTime", "Ljava/time/OffsetDateTime;", "taskId", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Ljava/time/OffsetDateTime;Ljava/lang/String;)V", "getFileUrl", "()Ljava/lang/String;", "getPayload", "getProgress", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getStatus", "getSubmittedTime", "()Ljava/time/OffsetDateTime;", "getTaskId", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Ljava/time/OffsetDateTime;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/ExportArchivedIssuesTaskProgressResponse;", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class ExportArchivedIssuesTaskProgressResponse {
   @Nullable
   private final String fileUrl;
   @Nullable
   private final String payload;
   @Nullable
   private final Long progress;
   @Nullable
   private final String status;
   @Nullable
   private final OffsetDateTime submittedTime;
   @Nullable
   private final String taskId;

   public ExportArchivedIssuesTaskProgressResponse(@Nullable String fileUrl, @Nullable String payload, @Nullable Long progress, @Nullable String status, @Nullable OffsetDateTime submittedTime, @Nullable String taskId) {
      this.fileUrl = fileUrl;
      this.payload = payload;
      this.progress = progress;
      this.status = status;
      this.submittedTime = submittedTime;
      this.taskId = taskId;
   }

   @JsonProperty("fileUrl")
   @Nullable
   public final String getFileUrl() {
      return this.fileUrl;
   }

   @JsonProperty("payload")
   @Nullable
   public final String getPayload() {
      return this.payload;
   }

   @JsonProperty("progress")
   @Nullable
   public final Long getProgress() {
      return this.progress;
   }

   @JsonProperty("status")
   @Nullable
   public final String getStatus() {
      return this.status;
   }

   @JsonProperty("submittedTime")
   @Nullable
   public final OffsetDateTime getSubmittedTime() {
      return this.submittedTime;
   }

   @JsonProperty("taskId")
   @Nullable
   public final String getTaskId() {
      return this.taskId;
   }

   @Nullable
   public final String component1() {
      return this.fileUrl;
   }

   @Nullable
   public final String component2() {
      return this.payload;
   }

   @Nullable
   public final Long component3() {
      return this.progress;
   }

   @Nullable
   public final String component4() {
      return this.status;
   }

   @Nullable
   public final OffsetDateTime component5() {
      return this.submittedTime;
   }

   @Nullable
   public final String component6() {
      return this.taskId;
   }

   @NotNull
   public final ExportArchivedIssuesTaskProgressResponse copy(@Nullable String fileUrl, @Nullable String payload, @Nullable Long progress, @Nullable String status, @Nullable OffsetDateTime submittedTime, @Nullable String taskId) {
      return new ExportArchivedIssuesTaskProgressResponse(fileUrl, payload, progress, status, submittedTime, taskId);
   }

   @NotNull
   public String toString() {
      return "ExportArchivedIssuesTaskProgressResponse(fileUrl=" + this.fileUrl + ", payload=" + this.payload + ", progress=" + this.progress + ", status=" + this.status + ", submittedTime=" + this.submittedTime + ", taskId=" + this.taskId + ")";
   }

   public int hashCode() {
      int var1 = this.fileUrl == null ? 0 : this.fileUrl.hashCode();
      var1 = var1 * 31 + (this.payload == null ? 0 : this.payload.hashCode());
      var1 = var1 * 31 + (this.progress == null ? 0 : this.progress.hashCode());
      var1 = var1 * 31 + (this.status == null ? 0 : this.status.hashCode());
      var1 = var1 * 31 + (this.submittedTime == null ? 0 : this.submittedTime.hashCode());
      var1 = var1 * 31 + (this.taskId == null ? 0 : this.taskId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ExportArchivedIssuesTaskProgressResponse)) {
         return false;
      } else {
         ExportArchivedIssuesTaskProgressResponse var2 = (ExportArchivedIssuesTaskProgressResponse)other;
         if (!Intrinsics.areEqual(this.fileUrl, var2.fileUrl)) {
            return false;
         } else if (!Intrinsics.areEqual(this.payload, var2.payload)) {
            return false;
         } else if (!Intrinsics.areEqual(this.progress, var2.progress)) {
            return false;
         } else if (!Intrinsics.areEqual(this.status, var2.status)) {
            return false;
         } else if (!Intrinsics.areEqual(this.submittedTime, var2.submittedTime)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.taskId, var2.taskId);
         }
      }
   }

   public ExportArchivedIssuesTaskProgressResponse() {
      this((String)null, (String)null, (Long)null, (String)null, (OffsetDateTime)null, (String)null, 63, (DefaultConstructorMarker)null);
   }
}
