package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J'\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0013\u0010\u0005\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\t\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/FieldConfigurationIssueTypeItem;", "", "fieldConfigurationId", "", "fieldConfigurationSchemeId", "issueTypeId", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getFieldConfigurationId", "()Ljava/lang/String;", "getFieldConfigurationSchemeId", "getIssueTypeId", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class FieldConfigurationIssueTypeItem {
   @NotNull
   private final String fieldConfigurationId;
   @NotNull
   private final String fieldConfigurationSchemeId;
   @NotNull
   private final String issueTypeId;

   public FieldConfigurationIssueTypeItem(@NotNull String fieldConfigurationId, @NotNull String fieldConfigurationSchemeId, @NotNull String issueTypeId) {
      Intrinsics.checkNotNullParameter(fieldConfigurationId, "");
      Intrinsics.checkNotNullParameter(fieldConfigurationSchemeId, "");
      Intrinsics.checkNotNullParameter(issueTypeId, "");
      super();
      this.fieldConfigurationId = fieldConfigurationId;
      this.fieldConfigurationSchemeId = fieldConfigurationSchemeId;
      this.issueTypeId = issueTypeId;
   }

   @JsonProperty("fieldConfigurationId")
   @NotNull
   public final String getFieldConfigurationId() {
      return this.fieldConfigurationId;
   }

   @JsonProperty("fieldConfigurationSchemeId")
   @NotNull
   public final String getFieldConfigurationSchemeId() {
      return this.fieldConfigurationSchemeId;
   }

   @JsonProperty("issueTypeId")
   @NotNull
   public final String getIssueTypeId() {
      return this.issueTypeId;
   }

   @NotNull
   public final String component1() {
      return this.fieldConfigurationId;
   }

   @NotNull
   public final String component2() {
      return this.fieldConfigurationSchemeId;
   }

   @NotNull
   public final String component3() {
      return this.issueTypeId;
   }

   @NotNull
   public final FieldConfigurationIssueTypeItem copy(@NotNull String fieldConfigurationId, @NotNull String fieldConfigurationSchemeId, @NotNull String issueTypeId) {
      Intrinsics.checkNotNullParameter(fieldConfigurationId, "");
      Intrinsics.checkNotNullParameter(fieldConfigurationSchemeId, "");
      Intrinsics.checkNotNullParameter(issueTypeId, "");
      return new FieldConfigurationIssueTypeItem(fieldConfigurationId, fieldConfigurationSchemeId, issueTypeId);
   }

   @NotNull
   public String toString() {
      return "FieldConfigurationIssueTypeItem(fieldConfigurationId=" + this.fieldConfigurationId + ", fieldConfigurationSchemeId=" + this.fieldConfigurationSchemeId + ", issueTypeId=" + this.issueTypeId + ")";
   }

   public int hashCode() {
      int var1 = this.fieldConfigurationId.hashCode();
      var1 = var1 * 31 + this.fieldConfigurationSchemeId.hashCode();
      var1 = var1 * 31 + this.issueTypeId.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof FieldConfigurationIssueTypeItem)) {
         return false;
      } else {
         FieldConfigurationIssueTypeItem var2 = (FieldConfigurationIssueTypeItem)other;
         if (!Intrinsics.areEqual(this.fieldConfigurationId, var2.fieldConfigurationId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.fieldConfigurationSchemeId, var2.fieldConfigurationSchemeId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.issueTypeId, var2.issueTypeId);
         }
      }
   }
}
