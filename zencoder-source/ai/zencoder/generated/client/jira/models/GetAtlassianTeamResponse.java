package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0002\b\u0016\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001%B;\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0004\b\u000b\u0010\fJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00c6\u0003J\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0012J\u0010\u0010\u001b\u001a\u0004\u0018\u00010\tH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0015J\u0010\u0010\u001c\u001a\u0004\u0018\u00010\tH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0015JF\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\tH\u00c6\u0001\u00a2\u0006\u0002\u0010\u001eJ\u0013\u0010\u001f\u001a\u00020 2\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\"\u001a\u00020#H\u00d6\u0001J\t\u0010$\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u0013\u001a\u0004\b\u0011\u0010\u0012R\u0017\u0010\b\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\n\n\u0002\u0010\u0016\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\n\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\n\n\u0002\u0010\u0016\u001a\u0004\b\u0017\u0010\u0015\u00a8\u0006&"},
   d2 = {"Lai/zencoder/generated/client/jira/models/GetAtlassianTeamResponse;", "", "id", "", "planningStyle", "Lai/zencoder/generated/client/jira/models/GetAtlassianTeamResponse$PlanningStyle;", "capacity", "", "issueSourceId", "", "sprintLength", "<init>", "(Ljava/lang/String;Lai/zencoder/generated/client/jira/models/GetAtlassianTeamResponse$PlanningStyle;Ljava/lang/Double;Ljava/lang/Long;Ljava/lang/Long;)V", "getId", "()Ljava/lang/String;", "getPlanningStyle", "()Lai/zencoder/generated/client/jira/models/GetAtlassianTeamResponse$PlanningStyle;", "getCapacity", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getIssueSourceId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getSprintLength", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/lang/String;Lai/zencoder/generated/client/jira/models/GetAtlassianTeamResponse$PlanningStyle;Ljava/lang/Double;Ljava/lang/Long;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/GetAtlassianTeamResponse;", "equals", "", "other", "hashCode", "", "toString", "PlanningStyle", "zencoder"}
)
public final class GetAtlassianTeamResponse {
   @NotNull
   private final String id;
   @NotNull
   private final PlanningStyle planningStyle;
   @Nullable
   private final Double capacity;
   @Nullable
   private final Long issueSourceId;
   @Nullable
   private final Long sprintLength;

   public GetAtlassianTeamResponse(@NotNull String id, @NotNull PlanningStyle planningStyle, @Nullable Double capacity, @Nullable Long issueSourceId, @Nullable Long sprintLength) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(planningStyle, "");
      super();
      this.id = id;
      this.planningStyle = planningStyle;
      this.capacity = capacity;
      this.issueSourceId = issueSourceId;
      this.sprintLength = sprintLength;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("planningStyle")
   @NotNull
   public final PlanningStyle getPlanningStyle() {
      return this.planningStyle;
   }

   @JsonProperty("capacity")
   @Nullable
   public final Double getCapacity() {
      return this.capacity;
   }

   @JsonProperty("issueSourceId")
   @Nullable
   public final Long getIssueSourceId() {
      return this.issueSourceId;
   }

   @JsonProperty("sprintLength")
   @Nullable
   public final Long getSprintLength() {
      return this.sprintLength;
   }

   @NotNull
   public final String component1() {
      return this.id;
   }

   @NotNull
   public final PlanningStyle component2() {
      return this.planningStyle;
   }

   @Nullable
   public final Double component3() {
      return this.capacity;
   }

   @Nullable
   public final Long component4() {
      return this.issueSourceId;
   }

   @Nullable
   public final Long component5() {
      return this.sprintLength;
   }

   @NotNull
   public final GetAtlassianTeamResponse copy(@NotNull String id, @NotNull PlanningStyle planningStyle, @Nullable Double capacity, @Nullable Long issueSourceId, @Nullable Long sprintLength) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(planningStyle, "");
      return new GetAtlassianTeamResponse(id, planningStyle, capacity, issueSourceId, sprintLength);
   }

   @NotNull
   public String toString() {
      return "GetAtlassianTeamResponse(id=" + this.id + ", planningStyle=" + this.planningStyle + ", capacity=" + this.capacity + ", issueSourceId=" + this.issueSourceId + ", sprintLength=" + this.sprintLength + ")";
   }

   public int hashCode() {
      int var1 = this.id.hashCode();
      var1 = var1 * 31 + this.planningStyle.hashCode();
      var1 = var1 * 31 + (this.capacity == null ? 0 : this.capacity.hashCode());
      var1 = var1 * 31 + (this.issueSourceId == null ? 0 : this.issueSourceId.hashCode());
      var1 = var1 * 31 + (this.sprintLength == null ? 0 : this.sprintLength.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof GetAtlassianTeamResponse)) {
         return false;
      } else {
         GetAtlassianTeamResponse var2 = (GetAtlassianTeamResponse)other;
         if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (this.planningStyle != var2.planningStyle) {
            return false;
         } else if (!Intrinsics.areEqual(this.capacity, var2.capacity)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueSourceId, var2.issueSourceId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.sprintLength, var2.sprintLength);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/GetAtlassianTeamResponse$PlanningStyle;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "Scrum", "Kanban", "zencoder"}
   )
   public static enum PlanningStyle {
      @NotNull
      private final String value;
      @JsonProperty("Scrum")
      Scrum("Scrum"),
      @JsonProperty("Kanban")
      Kanban("Kanban");

      private PlanningStyle(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<PlanningStyle> getEntries() {
         return $ENTRIES;
      }
   }
}
