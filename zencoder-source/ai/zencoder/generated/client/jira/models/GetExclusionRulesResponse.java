package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\"\n\u0002\u0010\t\n\u0002\b\u0016\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001Bi\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u00a2\u0006\u0004\b\u000b\u0010\fJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\u0011\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u0017\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u0018\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u001a\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003Jm\u0010\u001b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00d6\u0001J\t\u0010 \u001a\u00020!H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u001b\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u001b\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u001b\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010R\u001b\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010\u00a8\u0006\""},
   d2 = {"Lai/zencoder/generated/client/jira/models/GetExclusionRulesResponse;", "", "numberOfDaysToShowCompletedIssues", "", "issueIds", "", "", "issueTypeIds", "releaseIds", "workStatusCategoryIds", "workStatusIds", "<init>", "(ILjava/util/Set;Ljava/util/Set;Ljava/util/Set;Ljava/util/Set;Ljava/util/Set;)V", "getNumberOfDaysToShowCompletedIssues", "()I", "getIssueIds", "()Ljava/util/Set;", "getIssueTypeIds", "getReleaseIds", "getWorkStatusCategoryIds", "getWorkStatusIds", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "toString", "", "zencoder"}
)
public final class GetExclusionRulesResponse {
   private final int numberOfDaysToShowCompletedIssues;
   @Nullable
   private final Set<Long> issueIds;
   @Nullable
   private final Set<Long> issueTypeIds;
   @Nullable
   private final Set<Long> releaseIds;
   @Nullable
   private final Set<Long> workStatusCategoryIds;
   @Nullable
   private final Set<Long> workStatusIds;

   public GetExclusionRulesResponse(int numberOfDaysToShowCompletedIssues, @Nullable Set<Long> issueIds, @Nullable Set<Long> issueTypeIds, @Nullable Set<Long> releaseIds, @Nullable Set<Long> workStatusCategoryIds, @Nullable Set<Long> workStatusIds) {
      this.numberOfDaysToShowCompletedIssues = numberOfDaysToShowCompletedIssues;
      this.issueIds = issueIds;
      this.issueTypeIds = issueTypeIds;
      this.releaseIds = releaseIds;
      this.workStatusCategoryIds = workStatusCategoryIds;
      this.workStatusIds = workStatusIds;
   }

   @JsonProperty("numberOfDaysToShowCompletedIssues")
   public final int getNumberOfDaysToShowCompletedIssues() {
      return this.numberOfDaysToShowCompletedIssues;
   }

   @JsonProperty("issueIds")
   @Nullable
   public final Set<Long> getIssueIds() {
      return this.issueIds;
   }

   @JsonProperty("issueTypeIds")
   @Nullable
   public final Set<Long> getIssueTypeIds() {
      return this.issueTypeIds;
   }

   @JsonProperty("releaseIds")
   @Nullable
   public final Set<Long> getReleaseIds() {
      return this.releaseIds;
   }

   @JsonProperty("workStatusCategoryIds")
   @Nullable
   public final Set<Long> getWorkStatusCategoryIds() {
      return this.workStatusCategoryIds;
   }

   @JsonProperty("workStatusIds")
   @Nullable
   public final Set<Long> getWorkStatusIds() {
      return this.workStatusIds;
   }

   public final int component1() {
      return this.numberOfDaysToShowCompletedIssues;
   }

   @Nullable
   public final Set<Long> component2() {
      return this.issueIds;
   }

   @Nullable
   public final Set<Long> component3() {
      return this.issueTypeIds;
   }

   @Nullable
   public final Set<Long> component4() {
      return this.releaseIds;
   }

   @Nullable
   public final Set<Long> component5() {
      return this.workStatusCategoryIds;
   }

   @Nullable
   public final Set<Long> component6() {
      return this.workStatusIds;
   }

   @NotNull
   public final GetExclusionRulesResponse copy(int numberOfDaysToShowCompletedIssues, @Nullable Set<Long> issueIds, @Nullable Set<Long> issueTypeIds, @Nullable Set<Long> releaseIds, @Nullable Set<Long> workStatusCategoryIds, @Nullable Set<Long> workStatusIds) {
      return new GetExclusionRulesResponse(numberOfDaysToShowCompletedIssues, issueIds, issueTypeIds, releaseIds, workStatusCategoryIds, workStatusIds);
   }

   @NotNull
   public String toString() {
      return "GetExclusionRulesResponse(numberOfDaysToShowCompletedIssues=" + this.numberOfDaysToShowCompletedIssues + ", issueIds=" + this.issueIds + ", issueTypeIds=" + this.issueTypeIds + ", releaseIds=" + this.releaseIds + ", workStatusCategoryIds=" + this.workStatusCategoryIds + ", workStatusIds=" + this.workStatusIds + ")";
   }

   public int hashCode() {
      int var1 = Integer.hashCode(this.numberOfDaysToShowCompletedIssues);
      var1 = var1 * 31 + (this.issueIds == null ? 0 : this.issueIds.hashCode());
      var1 = var1 * 31 + (this.issueTypeIds == null ? 0 : this.issueTypeIds.hashCode());
      var1 = var1 * 31 + (this.releaseIds == null ? 0 : this.releaseIds.hashCode());
      var1 = var1 * 31 + (this.workStatusCategoryIds == null ? 0 : this.workStatusCategoryIds.hashCode());
      var1 = var1 * 31 + (this.workStatusIds == null ? 0 : this.workStatusIds.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof GetExclusionRulesResponse)) {
         return false;
      } else {
         GetExclusionRulesResponse var2 = (GetExclusionRulesResponse)other;
         if (this.numberOfDaysToShowCompletedIssues != var2.numberOfDaysToShowCompletedIssues) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueIds, var2.issueIds)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueTypeIds, var2.issueTypeIds)) {
            return false;
         } else if (!Intrinsics.areEqual(this.releaseIds, var2.releaseIds)) {
            return false;
         } else if (!Intrinsics.areEqual(this.workStatusCategoryIds, var2.workStatusCategoryIds)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.workStatusIds, var2.workStatusIds);
         }
      }
   }
}
