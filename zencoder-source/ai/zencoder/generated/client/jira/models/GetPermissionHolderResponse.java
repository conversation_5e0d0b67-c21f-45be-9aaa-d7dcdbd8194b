package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u0015B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/GetPermissionHolderResponse;", "", "type", "Lai/zencoder/generated/client/jira/models/GetPermissionHolderResponse$Type;", "value", "", "<init>", "(Lai/zencoder/generated/client/jira/models/GetPermissionHolderResponse$Type;Ljava/lang/String;)V", "getType", "()Lai/zencoder/generated/client/jira/models/GetPermissionHolderResponse$Type;", "getValue", "()Ljava/lang/String;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "Type", "zencoder"}
)
public final class GetPermissionHolderResponse {
   @NotNull
   private final Type type;
   @NotNull
   private final String value;

   public GetPermissionHolderResponse(@NotNull Type type, @NotNull String value) {
      Intrinsics.checkNotNullParameter(type, "");
      Intrinsics.checkNotNullParameter(value, "");
      super();
      this.type = type;
      this.value = value;
   }

   @JsonProperty("type")
   @NotNull
   public final Type getType() {
      return this.type;
   }

   @JsonProperty("value")
   @NotNull
   public final String getValue() {
      return this.value;
   }

   @NotNull
   public final Type component1() {
      return this.type;
   }

   @NotNull
   public final String component2() {
      return this.value;
   }

   @NotNull
   public final GetPermissionHolderResponse copy(@NotNull Type type, @NotNull String value) {
      Intrinsics.checkNotNullParameter(type, "");
      Intrinsics.checkNotNullParameter(value, "");
      return new GetPermissionHolderResponse(type, value);
   }

   @NotNull
   public String toString() {
      return "GetPermissionHolderResponse(type=" + this.type + ", value=" + this.value + ")";
   }

   public int hashCode() {
      int var1 = this.type.hashCode();
      var1 = var1 * 31 + this.value.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof GetPermissionHolderResponse)) {
         return false;
      } else {
         GetPermissionHolderResponse var2 = (GetPermissionHolderResponse)other;
         if (this.type != var2.type) {
            return false;
         } else {
            return Intrinsics.areEqual(this.value, var2.value);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/GetPermissionHolderResponse$Type;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "Group", "AccountId", "zencoder"}
   )
   public static enum Type {
      @NotNull
      private final String value;
      @JsonProperty("Group")
      Group("Group"),
      @JsonProperty("AccountId")
      AccountId("AccountId");

      private Type(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Type> getEntries() {
         return $ENTRIES;
      }
   }
}
