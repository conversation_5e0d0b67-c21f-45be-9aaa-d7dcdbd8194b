package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004BO\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0002\u00a2\u0006\u0004\b\u000b\u0010\fJ\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003JQ\u0010\u001a\u001a\u00020\u00002\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0002H\u00c6\u0001J\u0013\u0010\u001b\u001a\u00020\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001J\t\u0010 \u001a\u00020\u0002H\u00d6\u0001R\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u0015\u0010\b\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000eR\u0015\u0010\t\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000eR\u0015\u0010\n\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000e\u00a8\u0006!"},
   d2 = {"Lai/zencoder/generated/client/jira/models/HistoryMetadataParticipant;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "avatarUrl", "displayName", "displayNameKey", "id", "type", "url", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getAvatarUrl", "()Ljava/lang/String;", "getDisplayName", "getDisplayNameKey", "getId", "getType", "getUrl", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class HistoryMetadataParticipant extends HashMap<String, Object> {
   @Nullable
   private final String avatarUrl;
   @Nullable
   private final String displayName;
   @Nullable
   private final String displayNameKey;
   @Nullable
   private final String id;
   @Nullable
   private final String type;
   @Nullable
   private final String url;

   public HistoryMetadataParticipant(@Nullable String avatarUrl, @Nullable String displayName, @Nullable String displayNameKey, @Nullable String id, @Nullable String type, @Nullable String url) {
      this.avatarUrl = avatarUrl;
      this.displayName = displayName;
      this.displayNameKey = displayNameKey;
      this.id = id;
      this.type = type;
      this.url = url;
   }

   @JsonProperty("avatarUrl")
   @Nullable
   public final String getAvatarUrl() {
      return this.avatarUrl;
   }

   @JsonProperty("displayName")
   @Nullable
   public final String getDisplayName() {
      return this.displayName;
   }

   @JsonProperty("displayNameKey")
   @Nullable
   public final String getDisplayNameKey() {
      return this.displayNameKey;
   }

   @JsonProperty("id")
   @Nullable
   public final String getId() {
      return this.id;
   }

   @JsonProperty("type")
   @Nullable
   public final String getType() {
      return this.type;
   }

   @JsonProperty("url")
   @Nullable
   public final String getUrl() {
      return this.url;
   }

   @Nullable
   public final String component1() {
      return this.avatarUrl;
   }

   @Nullable
   public final String component2() {
      return this.displayName;
   }

   @Nullable
   public final String component3() {
      return this.displayNameKey;
   }

   @Nullable
   public final String component4() {
      return this.id;
   }

   @Nullable
   public final String component5() {
      return this.type;
   }

   @Nullable
   public final String component6() {
      return this.url;
   }

   @NotNull
   public final HistoryMetadataParticipant copy(@Nullable String avatarUrl, @Nullable String displayName, @Nullable String displayNameKey, @Nullable String id, @Nullable String type, @Nullable String url) {
      return new HistoryMetadataParticipant(avatarUrl, displayName, displayNameKey, id, type, url);
   }

   @NotNull
   public String toString() {
      return "HistoryMetadataParticipant(avatarUrl=" + this.avatarUrl + ", displayName=" + this.displayName + ", displayNameKey=" + this.displayNameKey + ", id=" + this.id + ", type=" + this.type + ", url=" + this.url + ")";
   }

   public int hashCode() {
      int var1 = this.avatarUrl == null ? 0 : this.avatarUrl.hashCode();
      var1 = var1 * 31 + (this.displayName == null ? 0 : this.displayName.hashCode());
      var1 = var1 * 31 + (this.displayNameKey == null ? 0 : this.displayNameKey.hashCode());
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      var1 = var1 * 31 + (this.type == null ? 0 : this.type.hashCode());
      var1 = var1 * 31 + (this.url == null ? 0 : this.url.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof HistoryMetadataParticipant)) {
         return false;
      } else {
         HistoryMetadataParticipant var2 = (HistoryMetadataParticipant)other;
         if (!Intrinsics.areEqual(this.avatarUrl, var2.avatarUrl)) {
            return false;
         } else if (!Intrinsics.areEqual(this.displayName, var2.displayName)) {
            return false;
         } else if (!Intrinsics.areEqual(this.displayNameKey, var2.displayNameKey)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.type, var2.type)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.url, var2.url);
         }
      }
   }

   public HistoryMetadataParticipant() {
      this((String)null, (String)null, (String)null, (String)null, (String)null, (String)null, 63, (DefaultConstructorMarker)null);
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
