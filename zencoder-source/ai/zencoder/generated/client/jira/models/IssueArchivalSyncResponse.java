package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000b\u0010\r\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000bJ&\u0010\u000f\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0010J\u0013\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\f\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueArchivalSyncResponse;", "", "errors", "Lai/zencoder/generated/client/jira/models/Errors;", "numberOfIssuesUpdated", "", "<init>", "(Lai/zencoder/generated/client/jira/models/Errors;Ljava/lang/Long;)V", "getErrors", "()Lai/zencoder/generated/client/jira/models/Errors;", "getNumberOfIssuesUpdated", "()Ljava/lang/Long;", "Ljava/lang/Long;", "component1", "component2", "copy", "(Lai/zencoder/generated/client/jira/models/Errors;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/IssueArchivalSyncResponse;", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class IssueArchivalSyncResponse {
   @Nullable
   private final Errors errors;
   @Nullable
   private final Long numberOfIssuesUpdated;

   public IssueArchivalSyncResponse(@Nullable Errors errors, @Nullable Long numberOfIssuesUpdated) {
      this.errors = errors;
      this.numberOfIssuesUpdated = numberOfIssuesUpdated;
   }

   @JsonProperty("errors")
   @Nullable
   public final Errors getErrors() {
      return this.errors;
   }

   @JsonProperty("numberOfIssuesUpdated")
   @Nullable
   public final Long getNumberOfIssuesUpdated() {
      return this.numberOfIssuesUpdated;
   }

   @Nullable
   public final Errors component1() {
      return this.errors;
   }

   @Nullable
   public final Long component2() {
      return this.numberOfIssuesUpdated;
   }

   @NotNull
   public final IssueArchivalSyncResponse copy(@Nullable Errors errors, @Nullable Long numberOfIssuesUpdated) {
      return new IssueArchivalSyncResponse(errors, numberOfIssuesUpdated);
   }

   @NotNull
   public String toString() {
      return "IssueArchivalSyncResponse(errors=" + this.errors + ", numberOfIssuesUpdated=" + this.numberOfIssuesUpdated + ")";
   }

   public int hashCode() {
      int var1 = this.errors == null ? 0 : this.errors.hashCode();
      var1 = var1 * 31 + (this.numberOfIssuesUpdated == null ? 0 : this.numberOfIssuesUpdated.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueArchivalSyncResponse)) {
         return false;
      } else {
         IssueArchivalSyncResponse var2 = (IssueArchivalSyncResponse)other;
         if (!Intrinsics.areEqual(this.errors, var2.errors)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.numberOfIssuesUpdated, var2.numberOfIssuesUpdated);
         }
      }
   }

   public IssueArchivalSyncResponse() {
      this((Errors)null, (Long)null, 3, (DefaultConstructorMarker)null);
   }
}
