package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0005\u00a2\u0006\u0004\b\t\u0010\nJ\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000bJ\u0011\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u0012\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0005H\u00c6\u0003J>\u0010\u0013\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0014J\u0013\u0010\u0015\u001a\u00020\u00032\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0006H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\f\u001a\u0004\b\u0002\u0010\u000bR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u001b\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000e\u00a8\u0006\u001a"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueBulkTransitionForWorkflow;", "", "isTransitionsFiltered", "", "issues", "", "", "transitions", "Lai/zencoder/generated/client/jira/models/SimplifiedIssueTransition;", "<init>", "(Ljava/lang/Boolean;Ljava/util/List;Ljava/util/List;)V", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getIssues", "()Ljava/util/List;", "getTransitions", "component1", "component2", "component3", "copy", "(Ljava/lang/Boolean;Ljava/util/List;Ljava/util/List;)Lai/zencoder/generated/client/jira/models/IssueBulkTransitionForWorkflow;", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class IssueBulkTransitionForWorkflow {
   @Nullable
   private final Boolean isTransitionsFiltered;
   @Nullable
   private final List<String> issues;
   @Nullable
   private final List<SimplifiedIssueTransition> transitions;

   public IssueBulkTransitionForWorkflow(@Nullable Boolean isTransitionsFiltered, @Nullable List<String> issues, @Nullable List<SimplifiedIssueTransition> transitions) {
      this.isTransitionsFiltered = isTransitionsFiltered;
      this.issues = issues;
      this.transitions = transitions;
   }

   @JsonProperty("isTransitionsFiltered")
   @Nullable
   public final Boolean isTransitionsFiltered() {
      return this.isTransitionsFiltered;
   }

   @JsonProperty("issues")
   @Nullable
   public final List<String> getIssues() {
      return this.issues;
   }

   @JsonProperty("transitions")
   @Nullable
   public final List<SimplifiedIssueTransition> getTransitions() {
      return this.transitions;
   }

   @Nullable
   public final Boolean component1() {
      return this.isTransitionsFiltered;
   }

   @Nullable
   public final List<String> component2() {
      return this.issues;
   }

   @Nullable
   public final List<SimplifiedIssueTransition> component3() {
      return this.transitions;
   }

   @NotNull
   public final IssueBulkTransitionForWorkflow copy(@Nullable Boolean isTransitionsFiltered, @Nullable List<String> issues, @Nullable List<SimplifiedIssueTransition> transitions) {
      return new IssueBulkTransitionForWorkflow(isTransitionsFiltered, issues, transitions);
   }

   @NotNull
   public String toString() {
      return "IssueBulkTransitionForWorkflow(isTransitionsFiltered=" + this.isTransitionsFiltered + ", issues=" + this.issues + ", transitions=" + this.transitions + ")";
   }

   public int hashCode() {
      int var1 = this.isTransitionsFiltered == null ? 0 : this.isTransitionsFiltered.hashCode();
      var1 = var1 * 31 + (this.issues == null ? 0 : this.issues.hashCode());
      var1 = var1 * 31 + (this.transitions == null ? 0 : this.transitions.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueBulkTransitionForWorkflow)) {
         return false;
      } else {
         IssueBulkTransitionForWorkflow var2 = (IssueBulkTransitionForWorkflow)other;
         if (!Intrinsics.areEqual(this.isTransitionsFiltered, var2.isTransitionsFiltered)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issues, var2.issues)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.transitions, var2.transitions);
         }
      }
   }

   public IssueBulkTransitionForWorkflow() {
      this((Boolean)null, (List)null, (List)null, 7, (DefaultConstructorMarker)null);
   }
}
