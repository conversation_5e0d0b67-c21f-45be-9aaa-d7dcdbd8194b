package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u000e\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B!\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\fJ*\u0010\u0010\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0011J\u0013\u0010\u0012\u001a\u00020\u00062\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0017\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\n\n\u0002\u0010\r\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueBulkTransitionPayload;", "", "bulkTransitionInputs", "", "Lai/zencoder/generated/client/jira/models/BulkTransitionSubmitInput;", "sendBulkNotification", "", "<init>", "(Ljava/util/List;Ljava/lang/Boolean;)V", "getBulkTransitionInputs", "()Ljava/util/List;", "getSendBulkNotification", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "component1", "component2", "copy", "(Ljava/util/List;Ljava/lang/Boolean;)Lai/zencoder/generated/client/jira/models/IssueBulkTransitionPayload;", "equals", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class IssueBulkTransitionPayload {
   @NotNull
   private final List<BulkTransitionSubmitInput> bulkTransitionInputs;
   @Nullable
   private final Boolean sendBulkNotification;

   public IssueBulkTransitionPayload(@NotNull List<BulkTransitionSubmitInput> bulkTransitionInputs, @Nullable Boolean sendBulkNotification) {
      Intrinsics.checkNotNullParameter(bulkTransitionInputs, "");
      super();
      this.bulkTransitionInputs = bulkTransitionInputs;
      this.sendBulkNotification = sendBulkNotification;
   }

   @JsonProperty("bulkTransitionInputs")
   @NotNull
   public final List<BulkTransitionSubmitInput> getBulkTransitionInputs() {
      return this.bulkTransitionInputs;
   }

   @JsonProperty("sendBulkNotification")
   @Nullable
   public final Boolean getSendBulkNotification() {
      return this.sendBulkNotification;
   }

   @NotNull
   public final List<BulkTransitionSubmitInput> component1() {
      return this.bulkTransitionInputs;
   }

   @Nullable
   public final Boolean component2() {
      return this.sendBulkNotification;
   }

   @NotNull
   public final IssueBulkTransitionPayload copy(@NotNull List<BulkTransitionSubmitInput> bulkTransitionInputs, @Nullable Boolean sendBulkNotification) {
      Intrinsics.checkNotNullParameter(bulkTransitionInputs, "");
      return new IssueBulkTransitionPayload(bulkTransitionInputs, sendBulkNotification);
   }

   @NotNull
   public String toString() {
      return "IssueBulkTransitionPayload(bulkTransitionInputs=" + this.bulkTransitionInputs + ", sendBulkNotification=" + this.sendBulkNotification + ")";
   }

   public int hashCode() {
      int var1 = this.bulkTransitionInputs.hashCode();
      var1 = var1 * 31 + (this.sendBulkNotification == null ? 0 : this.sendBulkNotification.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueBulkTransitionPayload)) {
         return false;
      } else {
         IssueBulkTransitionPayload var2 = (IssueBulkTransitionPayload)other;
         if (!Intrinsics.areEqual(this.bulkTransitionInputs, var2.bulkTransitionInputs)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.sendBulkNotification, var2.sendBulkNotification);
         }
      }
   }
}
