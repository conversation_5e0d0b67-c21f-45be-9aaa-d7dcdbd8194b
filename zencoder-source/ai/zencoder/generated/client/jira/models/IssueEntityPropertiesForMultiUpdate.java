package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0016\b\u0002\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0005\u00a2\u0006\u0004\b\b\u0010\tJ\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000bJ\u0017\u0010\u0010\u001a\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0005H\u00c6\u0003J2\u0010\u0011\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0016\b\u0002\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0012J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0006H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\f\u001a\u0004\b\n\u0010\u000bR!\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueEntityPropertiesForMultiUpdate;", "", "issueID", "", "properties", "", "", "Lai/zencoder/generated/client/jira/models/JsonNode;", "<init>", "(Ljava/lang/Long;Ljava/util/Map;)V", "getIssueID", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getProperties", "()Ljava/util/Map;", "component1", "component2", "copy", "(Ljava/lang/Long;Ljava/util/Map;)Lai/zencoder/generated/client/jira/models/IssueEntityPropertiesForMultiUpdate;", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class IssueEntityPropertiesForMultiUpdate {
   @Nullable
   private final Long issueID;
   @Nullable
   private final Map<String, JsonNode> properties;

   public IssueEntityPropertiesForMultiUpdate(@Nullable Long issueID, @Nullable Map<String, JsonNode> properties) {
      this.issueID = issueID;
      this.properties = properties;
   }

   @JsonProperty("issueID")
   @Nullable
   public final Long getIssueID() {
      return this.issueID;
   }

   @JsonProperty("properties")
   @Nullable
   public final Map<String, JsonNode> getProperties() {
      return this.properties;
   }

   @Nullable
   public final Long component1() {
      return this.issueID;
   }

   @Nullable
   public final Map<String, JsonNode> component2() {
      return this.properties;
   }

   @NotNull
   public final IssueEntityPropertiesForMultiUpdate copy(@Nullable Long issueID, @Nullable Map<String, JsonNode> properties) {
      return new IssueEntityPropertiesForMultiUpdate(issueID, properties);
   }

   @NotNull
   public String toString() {
      return "IssueEntityPropertiesForMultiUpdate(issueID=" + this.issueID + ", properties=" + this.properties + ")";
   }

   public int hashCode() {
      int var1 = this.issueID == null ? 0 : this.issueID.hashCode();
      var1 = var1 * 31 + (this.properties == null ? 0 : this.properties.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueEntityPropertiesForMultiUpdate)) {
         return false;
      } else {
         IssueEntityPropertiesForMultiUpdate var2 = (IssueEntityPropertiesForMultiUpdate)other;
         if (!Intrinsics.areEqual(this.issueID, var2.issueID)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.properties, var2.properties);
         }
      }
   }

   public IssueEntityPropertiesForMultiUpdate() {
      this((Long)null, (Map)null, 3, (DefaultConstructorMarker)null);
   }
}
