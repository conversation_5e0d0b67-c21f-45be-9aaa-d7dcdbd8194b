package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u0017B%\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0011\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J'\u0010\u000f\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueFieldOptionConfiguration;", "", "attributes", "", "Lai/zencoder/generated/client/jira/models/IssueFieldOptionConfiguration$Attributes;", "scope", "Lai/zencoder/generated/client/jira/models/IssueFieldOptionScopeBean;", "<init>", "(Ljava/util/Set;Lai/zencoder/generated/client/jira/models/IssueFieldOptionScopeBean;)V", "getAttributes", "()Ljava/util/Set;", "getScope", "()Lai/zencoder/generated/client/jira/models/IssueFieldOptionScopeBean;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "Attributes", "zencoder"}
)
public final class IssueFieldOptionConfiguration {
   @Nullable
   private final Set<Attributes> attributes;
   @Nullable
   private final IssueFieldOptionScopeBean scope;

   public IssueFieldOptionConfiguration(@Nullable Set<? extends Attributes> attributes, @Nullable IssueFieldOptionScopeBean scope) {
      this.attributes = attributes;
      this.scope = scope;
   }

   @JsonProperty("attributes")
   @Nullable
   public final Set<Attributes> getAttributes() {
      return this.attributes;
   }

   @JsonProperty("scope")
   @Nullable
   public final IssueFieldOptionScopeBean getScope() {
      return this.scope;
   }

   @Nullable
   public final Set<Attributes> component1() {
      return this.attributes;
   }

   @Nullable
   public final IssueFieldOptionScopeBean component2() {
      return this.scope;
   }

   @NotNull
   public final IssueFieldOptionConfiguration copy(@Nullable Set<? extends Attributes> attributes, @Nullable IssueFieldOptionScopeBean scope) {
      return new IssueFieldOptionConfiguration(attributes, scope);
   }

   @NotNull
   public String toString() {
      return "IssueFieldOptionConfiguration(attributes=" + this.attributes + ", scope=" + this.scope + ")";
   }

   public int hashCode() {
      int var1 = this.attributes == null ? 0 : this.attributes.hashCode();
      var1 = var1 * 31 + (this.scope == null ? 0 : this.scope.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueFieldOptionConfiguration)) {
         return false;
      } else {
         IssueFieldOptionConfiguration var2 = (IssueFieldOptionConfiguration)other;
         if (!Intrinsics.areEqual(this.attributes, var2.attributes)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.scope, var2.scope);
         }
      }
   }

   public IssueFieldOptionConfiguration() {
      this((Set)null, (IssueFieldOptionScopeBean)null, 3, (DefaultConstructorMarker)null);
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/IssueFieldOptionConfiguration$Attributes;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "notSelectable", "defaultValue", "zencoder"}
   )
   public static enum Attributes {
      @NotNull
      private final String value;
      @JsonProperty("notSelectable")
      notSelectable("notSelectable"),
      @JsonProperty("defaultValue")
      defaultValue("defaultValue");

      private Attributes(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Attributes> getEntries() {
         return $ENTRIES;
      }
   }
}
