package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004B3\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\u0016\b\u0002\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003\u0018\u00010\t\u00a2\u0006\u0004\b\n\u0010\u000bJ\t\u0010\u0012\u001a\u00020\u0002H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u0017\u0010\u0014\u001a\u0010\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003\u0018\u00010\tH\u00c6\u0003J7\u0010\u0015\u001a\u00020\u00002\b\b\u0002\u0010\u0005\u001a\u00020\u00022\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\u0016\b\u0002\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003\u0018\u00010\tH\u00c6\u0001J\u0013\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u0002H\u00d6\u0001R\u0013\u0010\u0005\u001a\u00020\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR!\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003\u0018\u00010\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueFieldOptionCreateBean;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "value", "config", "Lai/zencoder/generated/client/jira/models/IssueFieldOptionConfiguration;", "properties", "", "<init>", "(Ljava/lang/String;Lai/zencoder/generated/client/jira/models/IssueFieldOptionConfiguration;Ljava/util/Map;)V", "getValue", "()Ljava/lang/String;", "getConfig", "()Lai/zencoder/generated/client/jira/models/IssueFieldOptionConfiguration;", "getProperties", "()Ljava/util/Map;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class IssueFieldOptionCreateBean extends HashMap<String, Object> {
   @NotNull
   private final String value;
   @Nullable
   private final IssueFieldOptionConfiguration config;
   @Nullable
   private final Map<String, Object> properties;

   public IssueFieldOptionCreateBean(@NotNull String value, @Nullable IssueFieldOptionConfiguration config, @Nullable Map<String, ? extends Object> properties) {
      Intrinsics.checkNotNullParameter(value, "");
      super();
      this.value = value;
      this.config = config;
      this.properties = properties;
   }

   @JsonProperty("value")
   @NotNull
   public final String getValue() {
      return this.value;
   }

   @JsonProperty("config")
   @Nullable
   public final IssueFieldOptionConfiguration getConfig() {
      return this.config;
   }

   @JsonProperty("properties")
   @Nullable
   public final Map<String, Object> getProperties() {
      return this.properties;
   }

   @NotNull
   public final String component1() {
      return this.value;
   }

   @Nullable
   public final IssueFieldOptionConfiguration component2() {
      return this.config;
   }

   @Nullable
   public final Map<String, Object> component3() {
      return this.properties;
   }

   @NotNull
   public final IssueFieldOptionCreateBean copy(@NotNull String value, @Nullable IssueFieldOptionConfiguration config, @Nullable Map<String, ? extends Object> properties) {
      Intrinsics.checkNotNullParameter(value, "");
      return new IssueFieldOptionCreateBean(value, config, properties);
   }

   @NotNull
   public String toString() {
      return "IssueFieldOptionCreateBean(value=" + this.value + ", config=" + this.config + ", properties=" + this.properties + ")";
   }

   public int hashCode() {
      int var1 = this.value.hashCode();
      var1 = var1 * 31 + (this.config == null ? 0 : this.config.hashCode());
      var1 = var1 * 31 + (this.properties == null ? 0 : this.properties.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueFieldOptionCreateBean)) {
         return false;
      } else {
         IssueFieldOptionCreateBean var2 = (IssueFieldOptionCreateBean)other;
         if (!Intrinsics.areEqual(this.value, var2.value)) {
            return false;
         } else if (!Intrinsics.areEqual(this.config, var2.config)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.properties, var2.properties);
         }
      }
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
