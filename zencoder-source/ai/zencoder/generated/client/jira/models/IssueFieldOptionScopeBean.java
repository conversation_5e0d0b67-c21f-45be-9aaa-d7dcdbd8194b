package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\"\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0005\u00a2\u0006\u0004\b\t\u0010\nJ\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u0012\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0005H\u00c6\u0003J9\u0010\u0013\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u001b\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000e\u00a8\u0006\u001b"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueFieldOptionScopeBean;", "", "global", "Lai/zencoder/generated/client/jira/models/GlobalScopeBean;", "projects", "", "", "projects2", "Lai/zencoder/generated/client/jira/models/ProjectScopeBean;", "<init>", "(Lai/zencoder/generated/client/jira/models/GlobalScopeBean;Ljava/util/Set;Ljava/util/Set;)V", "getGlobal", "()Lai/zencoder/generated/client/jira/models/GlobalScopeBean;", "getProjects", "()Ljava/util/Set;", "getProjects2", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class IssueFieldOptionScopeBean {
   @Nullable
   private final GlobalScopeBean global;
   @Nullable
   private final Set<Long> projects;
   @Nullable
   private final Set<ProjectScopeBean> projects2;

   public IssueFieldOptionScopeBean(@Nullable GlobalScopeBean global, @Nullable Set<Long> projects, @Nullable Set<ProjectScopeBean> projects2) {
      this.global = global;
      this.projects = projects;
      this.projects2 = projects2;
   }

   @JsonProperty("global")
   @Nullable
   public final GlobalScopeBean getGlobal() {
      return this.global;
   }

   @JsonProperty("projects")
   @Nullable
   public final Set<Long> getProjects() {
      return this.projects;
   }

   @JsonProperty("projects2")
   @Nullable
   public final Set<ProjectScopeBean> getProjects2() {
      return this.projects2;
   }

   @Nullable
   public final GlobalScopeBean component1() {
      return this.global;
   }

   @Nullable
   public final Set<Long> component2() {
      return this.projects;
   }

   @Nullable
   public final Set<ProjectScopeBean> component3() {
      return this.projects2;
   }

   @NotNull
   public final IssueFieldOptionScopeBean copy(@Nullable GlobalScopeBean global, @Nullable Set<Long> projects, @Nullable Set<ProjectScopeBean> projects2) {
      return new IssueFieldOptionScopeBean(global, projects, projects2);
   }

   @NotNull
   public String toString() {
      return "IssueFieldOptionScopeBean(global=" + this.global + ", projects=" + this.projects + ", projects2=" + this.projects2 + ")";
   }

   public int hashCode() {
      int var1 = this.global == null ? 0 : this.global.hashCode();
      var1 = var1 * 31 + (this.projects == null ? 0 : this.projects.hashCode());
      var1 = var1 * 31 + (this.projects2 == null ? 0 : this.projects2.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueFieldOptionScopeBean)) {
         return false;
      } else {
         IssueFieldOptionScopeBean var2 = (IssueFieldOptionScopeBean)other;
         if (!Intrinsics.areEqual(this.global, var2.global)) {
            return false;
         } else if (!Intrinsics.areEqual(this.projects, var2.projects)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.projects2, var2.projects2);
         }
      }
   }

   public IssueFieldOptionScopeBean() {
      this((GlobalScopeBean)null, (Set)null, (Set)null, 7, (DefaultConstructorMarker)null);
   }
}
