package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0010\t\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0001\u0012\u0010\b\u0002\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0004\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J\u0011\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0004H\u00c6\u0003J'\u0010\u000e\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00012\u0010\b\u0002\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0004H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u001b\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueFilterForBulkPropertyDelete;", "", "currentValue", "entityIds", "", "", "<init>", "(Ljava/lang/Object;Ljava/util/Set;)V", "getCurrentValue", "()Ljava/lang/Object;", "getEntityIds", "()Ljava/util/Set;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class IssueFilterForBulkPropertyDelete {
   @Nullable
   private final Object currentValue;
   @Nullable
   private final Set<Long> entityIds;

   public IssueFilterForBulkPropertyDelete(@Nullable Object currentValue, @Nullable Set<Long> entityIds) {
      this.currentValue = currentValue;
      this.entityIds = entityIds;
   }

   @JsonProperty("currentValue")
   @Nullable
   public final Object getCurrentValue() {
      return this.currentValue;
   }

   @JsonProperty("entityIds")
   @Nullable
   public final Set<Long> getEntityIds() {
      return this.entityIds;
   }

   @Nullable
   public final Object component1() {
      return this.currentValue;
   }

   @Nullable
   public final Set<Long> component2() {
      return this.entityIds;
   }

   @NotNull
   public final IssueFilterForBulkPropertyDelete copy(@Nullable Object currentValue, @Nullable Set<Long> entityIds) {
      return new IssueFilterForBulkPropertyDelete(currentValue, entityIds);
   }

   @NotNull
   public String toString() {
      return "IssueFilterForBulkPropertyDelete(currentValue=" + this.currentValue + ", entityIds=" + this.entityIds + ")";
   }

   public int hashCode() {
      int var1 = this.currentValue == null ? 0 : this.currentValue.hashCode();
      var1 = var1 * 31 + (this.entityIds == null ? 0 : this.entityIds.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueFilterForBulkPropertyDelete)) {
         return false;
      } else {
         IssueFilterForBulkPropertyDelete var2 = (IssueFilterForBulkPropertyDelete)other;
         if (!Intrinsics.areEqual(this.currentValue, var2.currentValue)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.entityIds, var2.entityIds);
         }
      }
   }

   public IssueFilterForBulkPropertyDelete() {
      this((Object)null, (Set)null, 3, (DefaultConstructorMarker)null);
   }
}
