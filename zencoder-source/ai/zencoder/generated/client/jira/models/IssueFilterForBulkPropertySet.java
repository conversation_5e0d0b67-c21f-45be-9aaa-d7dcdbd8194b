package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0011\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0001\u0012\u0010\b\u0002\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0004\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J\u0011\u0010\u0012\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0004H\u00c6\u0003J\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000fJ8\u0010\u0014\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00012\u0010\b\u0002\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00042\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0015J\u0013\u0010\u0016\u001a\u00020\u00072\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u001b\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0017\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u0010\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueFilterForBulkPropertySet;", "", "currentValue", "entityIds", "", "", "hasProperty", "", "<init>", "(Ljava/lang/Object;Ljava/util/Set;Ljava/lang/Boolean;)V", "getCurrentValue", "()Ljava/lang/Object;", "getEntityIds", "()Ljava/util/Set;", "getHasProperty", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "component1", "component2", "component3", "copy", "(Ljava/lang/Object;Ljava/util/Set;Ljava/lang/Boolean;)Lai/zencoder/generated/client/jira/models/IssueFilterForBulkPropertySet;", "equals", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class IssueFilterForBulkPropertySet {
   @Nullable
   private final Object currentValue;
   @Nullable
   private final Set<Long> entityIds;
   @Nullable
   private final Boolean hasProperty;

   public IssueFilterForBulkPropertySet(@Nullable Object currentValue, @Nullable Set<Long> entityIds, @Nullable Boolean hasProperty) {
      this.currentValue = currentValue;
      this.entityIds = entityIds;
      this.hasProperty = hasProperty;
   }

   @JsonProperty("currentValue")
   @Nullable
   public final Object getCurrentValue() {
      return this.currentValue;
   }

   @JsonProperty("entityIds")
   @Nullable
   public final Set<Long> getEntityIds() {
      return this.entityIds;
   }

   @JsonProperty("hasProperty")
   @Nullable
   public final Boolean getHasProperty() {
      return this.hasProperty;
   }

   @Nullable
   public final Object component1() {
      return this.currentValue;
   }

   @Nullable
   public final Set<Long> component2() {
      return this.entityIds;
   }

   @Nullable
   public final Boolean component3() {
      return this.hasProperty;
   }

   @NotNull
   public final IssueFilterForBulkPropertySet copy(@Nullable Object currentValue, @Nullable Set<Long> entityIds, @Nullable Boolean hasProperty) {
      return new IssueFilterForBulkPropertySet(currentValue, entityIds, hasProperty);
   }

   @NotNull
   public String toString() {
      return "IssueFilterForBulkPropertySet(currentValue=" + this.currentValue + ", entityIds=" + this.entityIds + ", hasProperty=" + this.hasProperty + ")";
   }

   public int hashCode() {
      int var1 = this.currentValue == null ? 0 : this.currentValue.hashCode();
      var1 = var1 * 31 + (this.entityIds == null ? 0 : this.entityIds.hashCode());
      var1 = var1 * 31 + (this.hasProperty == null ? 0 : this.hasProperty.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueFilterForBulkPropertySet)) {
         return false;
      } else {
         IssueFilterForBulkPropertySet var2 = (IssueFilterForBulkPropertySet)other;
         if (!Intrinsics.areEqual(this.currentValue, var2.currentValue)) {
            return false;
         } else if (!Intrinsics.areEqual(this.entityIds, var2.entityIds)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.hasProperty, var2.hasProperty);
         }
      }
   }

   public IssueFilterForBulkPropertySet() {
      this((Object)null, (Set)null, (Boolean)null, 7, (DefaultConstructorMarker)null);
   }
}
