package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0016\b\u0002\u0010\u0002\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0017\u0010\n\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0003H\u00c6\u0003J!\u0010\u000b\u001a\u00020\u00002\u0016\b\u0002\u0010\u0002\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u0010\u001a\u00020\u0004H\u00d6\u0001R!\u0010\u0002\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\t\u00a8\u0006\u0011"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueLimitReportRequest;", "", "issuesApproachingLimitParams", "", "", "", "<init>", "(Ljava/util/Map;)V", "getIssuesApproachingLimitParams", "()Ljava/util/Map;", "component1", "copy", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class IssueLimitReportRequest {
   @Nullable
   private final Map<String, Integer> issuesApproachingLimitParams;

   public IssueLimitReportRequest(@Nullable Map<String, Integer> issuesApproachingLimitParams) {
      this.issuesApproachingLimitParams = issuesApproachingLimitParams;
   }

   @JsonProperty("issuesApproachingLimitParams")
   @Nullable
   public final Map<String, Integer> getIssuesApproachingLimitParams() {
      return this.issuesApproachingLimitParams;
   }

   @Nullable
   public final Map<String, Integer> component1() {
      return this.issuesApproachingLimitParams;
   }

   @NotNull
   public final IssueLimitReportRequest copy(@Nullable Map<String, Integer> issuesApproachingLimitParams) {
      return new IssueLimitReportRequest(issuesApproachingLimitParams);
   }

   @NotNull
   public String toString() {
      return "IssueLimitReportRequest(issuesApproachingLimitParams=" + this.issuesApproachingLimitParams + ")";
   }

   public int hashCode() {
      return this.issuesApproachingLimitParams == null ? 0 : this.issuesApproachingLimitParams.hashCode();
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueLimitReportRequest)) {
         return false;
      } else {
         IssueLimitReportRequest var2 = (IssueLimitReportRequest)other;
         return Intrinsics.areEqual(this.issuesApproachingLimitParams, var2.issuesApproachingLimitParams);
      }
   }

   public IssueLimitReportRequest() {
      this((Map)null, 1, (DefaultConstructorMarker)null);
   }
}
