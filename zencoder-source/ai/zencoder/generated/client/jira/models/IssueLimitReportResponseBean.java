package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001Bg\u0012\"\b\u0002\u0010\u0002\u001a\u001c\u0012\u0004\u0012\u00020\u0004\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0018\u00010\u0003\u0012\"\b\u0002\u0010\u0006\u001a\u001c\u0012\u0004\u0012\u00020\u0004\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0018\u00010\u0003\u0012\u0016\b\u0002\u0010\u0007\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003\u00a2\u0006\u0004\b\t\u0010\nJ#\u0010\u000f\u001a\u001c\u0012\u0004\u0012\u00020\u0004\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0018\u00010\u0003H\u00c6\u0003J#\u0010\u0010\u001a\u001c\u0012\u0004\u0012\u00020\u0004\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0018\u00010\u0003H\u00c6\u0003J\u0017\u0010\u0011\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003H\u00c6\u0003Ji\u0010\u0012\u001a\u00020\u00002\"\b\u0002\u0010\u0002\u001a\u001c\u0012\u0004\u0012\u00020\u0004\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0018\u00010\u00032\"\b\u0002\u0010\u0006\u001a\u001c\u0012\u0004\u0012\u00020\u0004\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0018\u00010\u00032\u0016\b\u0002\u0010\u0007\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\bH\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0004H\u00d6\u0001R-\u0010\u0002\u001a\u001c\u0012\u0004\u0012\u00020\u0004\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR-\u0010\u0006\u001a\u001c\u0012\u0004\u0012\u00020\u0004\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR!\u0010\u0007\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\b\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\f\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueLimitReportResponseBean;", "", "issuesApproachingLimit", "", "", "", "issuesBreachingLimit", "limits", "", "<init>", "(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V", "getIssuesApproachingLimit", "()Ljava/util/Map;", "getIssuesBreachingLimit", "getLimits", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class IssueLimitReportResponseBean {
   @Nullable
   private final Map<String, Map<String, Long>> issuesApproachingLimit;
   @Nullable
   private final Map<String, Map<String, Long>> issuesBreachingLimit;
   @Nullable
   private final Map<String, Integer> limits;

   public IssueLimitReportResponseBean(@Nullable Map<String, ? extends Map<String, Long>> issuesApproachingLimit, @Nullable Map<String, ? extends Map<String, Long>> issuesBreachingLimit, @Nullable Map<String, Integer> limits) {
      this.issuesApproachingLimit = issuesApproachingLimit;
      this.issuesBreachingLimit = issuesBreachingLimit;
      this.limits = limits;
   }

   @JsonProperty("issuesApproachingLimit")
   @Nullable
   public final Map<String, Map<String, Long>> getIssuesApproachingLimit() {
      return this.issuesApproachingLimit;
   }

   @JsonProperty("issuesBreachingLimit")
   @Nullable
   public final Map<String, Map<String, Long>> getIssuesBreachingLimit() {
      return this.issuesBreachingLimit;
   }

   @JsonProperty("limits")
   @Nullable
   public final Map<String, Integer> getLimits() {
      return this.limits;
   }

   @Nullable
   public final Map<String, Map<String, Long>> component1() {
      return this.issuesApproachingLimit;
   }

   @Nullable
   public final Map<String, Map<String, Long>> component2() {
      return this.issuesBreachingLimit;
   }

   @Nullable
   public final Map<String, Integer> component3() {
      return this.limits;
   }

   @NotNull
   public final IssueLimitReportResponseBean copy(@Nullable Map<String, ? extends Map<String, Long>> issuesApproachingLimit, @Nullable Map<String, ? extends Map<String, Long>> issuesBreachingLimit, @Nullable Map<String, Integer> limits) {
      return new IssueLimitReportResponseBean(issuesApproachingLimit, issuesBreachingLimit, limits);
   }

   @NotNull
   public String toString() {
      return "IssueLimitReportResponseBean(issuesApproachingLimit=" + this.issuesApproachingLimit + ", issuesBreachingLimit=" + this.issuesBreachingLimit + ", limits=" + this.limits + ")";
   }

   public int hashCode() {
      int var1 = this.issuesApproachingLimit == null ? 0 : this.issuesApproachingLimit.hashCode();
      var1 = var1 * 31 + (this.issuesBreachingLimit == null ? 0 : this.issuesBreachingLimit.hashCode());
      var1 = var1 * 31 + (this.limits == null ? 0 : this.limits.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueLimitReportResponseBean)) {
         return false;
      } else {
         IssueLimitReportResponseBean var2 = (IssueLimitReportResponseBean)other;
         if (!Intrinsics.areEqual(this.issuesApproachingLimit, var2.issuesApproachingLimit)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issuesBreachingLimit, var2.issuesBreachingLimit)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.limits, var2.limits);
         }
      }
   }

   public IssueLimitReportResponseBean() {
      this((Map)null, (Map)null, (Map)null, 7, (DefaultConstructorMarker)null);
   }
}
