package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BI\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\n\u0010\u000bJ\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0014\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003JK\u0010\u0018\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\rR\u0015\u0010\b\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\rR\u0015\u0010\t\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\r\u00a8\u0006\u001f"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssuePickerSuggestionsIssueType;", "", "id", "", "issues", "", "Lai/zencoder/generated/client/jira/models/SuggestedIssue;", "label", "msg", "sub", "<init>", "(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getId", "()Ljava/lang/String;", "getIssues", "()Ljava/util/List;", "getLabel", "getMsg", "getSub", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class IssuePickerSuggestionsIssueType {
   @Nullable
   private final String id;
   @Nullable
   private final List<SuggestedIssue> issues;
   @Nullable
   private final String label;
   @Nullable
   private final String msg;
   @Nullable
   private final String sub;

   public IssuePickerSuggestionsIssueType(@Nullable String id, @Nullable List<SuggestedIssue> issues, @Nullable String label, @Nullable String msg, @Nullable String sub) {
      this.id = id;
      this.issues = issues;
      this.label = label;
      this.msg = msg;
      this.sub = sub;
   }

   @JsonProperty("id")
   @Nullable
   public final String getId() {
      return this.id;
   }

   @JsonProperty("issues")
   @Nullable
   public final List<SuggestedIssue> getIssues() {
      return this.issues;
   }

   @JsonProperty("label")
   @Nullable
   public final String getLabel() {
      return this.label;
   }

   @JsonProperty("msg")
   @Nullable
   public final String getMsg() {
      return this.msg;
   }

   @JsonProperty("sub")
   @Nullable
   public final String getSub() {
      return this.sub;
   }

   @Nullable
   public final String component1() {
      return this.id;
   }

   @Nullable
   public final List<SuggestedIssue> component2() {
      return this.issues;
   }

   @Nullable
   public final String component3() {
      return this.label;
   }

   @Nullable
   public final String component4() {
      return this.msg;
   }

   @Nullable
   public final String component5() {
      return this.sub;
   }

   @NotNull
   public final IssuePickerSuggestionsIssueType copy(@Nullable String id, @Nullable List<SuggestedIssue> issues, @Nullable String label, @Nullable String msg, @Nullable String sub) {
      return new IssuePickerSuggestionsIssueType(id, issues, label, msg, sub);
   }

   @NotNull
   public String toString() {
      return "IssuePickerSuggestionsIssueType(id=" + this.id + ", issues=" + this.issues + ", label=" + this.label + ", msg=" + this.msg + ", sub=" + this.sub + ")";
   }

   public int hashCode() {
      int var1 = this.id == null ? 0 : this.id.hashCode();
      var1 = var1 * 31 + (this.issues == null ? 0 : this.issues.hashCode());
      var1 = var1 * 31 + (this.label == null ? 0 : this.label.hashCode());
      var1 = var1 * 31 + (this.msg == null ? 0 : this.msg.hashCode());
      var1 = var1 * 31 + (this.sub == null ? 0 : this.sub.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssuePickerSuggestionsIssueType)) {
         return false;
      } else {
         IssuePickerSuggestionsIssueType var2 = (IssuePickerSuggestionsIssueType)other;
         if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issues, var2.issues)) {
            return false;
         } else if (!Intrinsics.areEqual(this.label, var2.label)) {
            return false;
         } else if (!Intrinsics.areEqual(this.msg, var2.msg)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.sub, var2.sub);
         }
      }
   }

   public IssuePickerSuggestionsIssueType() {
      this((String)null, (List)null, (String)null, (String)null, (String)null, 31, (DefaultConstructorMarker)null);
   }
}
