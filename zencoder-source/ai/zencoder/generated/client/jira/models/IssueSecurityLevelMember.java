package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J'\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0006\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\f\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueSecurityLevelMember;", "", "holder", "Lai/zencoder/generated/client/jira/models/PermissionHolder;", "id", "", "issueSecurityLevelId", "<init>", "(Lai/zencoder/generated/client/jira/models/PermissionHolder;JJ)V", "getHolder", "()Lai/zencoder/generated/client/jira/models/PermissionHolder;", "getId", "()J", "getIssueSecurityLevelId", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class IssueSecurityLevelMember {
   @NotNull
   private final PermissionHolder holder;
   private final long id;
   private final long issueSecurityLevelId;

   public IssueSecurityLevelMember(@NotNull PermissionHolder holder, long id, long issueSecurityLevelId) {
      Intrinsics.checkNotNullParameter(holder, "");
      super();
      this.holder = holder;
      this.id = id;
      this.issueSecurityLevelId = issueSecurityLevelId;
   }

   @JsonProperty("holder")
   @NotNull
   public final PermissionHolder getHolder() {
      return this.holder;
   }

   @JsonProperty("id")
   public final long getId() {
      return this.id;
   }

   @JsonProperty("issueSecurityLevelId")
   public final long getIssueSecurityLevelId() {
      return this.issueSecurityLevelId;
   }

   @NotNull
   public final PermissionHolder component1() {
      return this.holder;
   }

   public final long component2() {
      return this.id;
   }

   public final long component3() {
      return this.issueSecurityLevelId;
   }

   @NotNull
   public final IssueSecurityLevelMember copy(@NotNull PermissionHolder holder, long id, long issueSecurityLevelId) {
      Intrinsics.checkNotNullParameter(holder, "");
      return new IssueSecurityLevelMember(holder, id, issueSecurityLevelId);
   }

   @NotNull
   public String toString() {
      return "IssueSecurityLevelMember(holder=" + this.holder + ", id=" + this.id + ", issueSecurityLevelId=" + this.issueSecurityLevelId + ")";
   }

   public int hashCode() {
      int var1 = this.holder.hashCode();
      var1 = var1 * 31 + Long.hashCode(this.id);
      var1 = var1 * 31 + Long.hashCode(this.issueSecurityLevelId);
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueSecurityLevelMember)) {
         return false;
      } else {
         IssueSecurityLevelMember var2 = (IssueSecurityLevelMember)other;
         if (!Intrinsics.areEqual(this.holder, var2.holder)) {
            return false;
         } else if (this.id != var2.id) {
            return false;
         } else {
            return this.issueSecurityLevelId == var2.issueSecurityLevelId;
         }
      }
   }
}
