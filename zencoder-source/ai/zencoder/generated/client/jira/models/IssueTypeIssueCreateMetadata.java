package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import java.util.UUID;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b+\b\u0086\b\u0018\u00002\u00020\u0001B\u00a3\u0001\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0005\u0012\u0016\b\u0002\u0010\t\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000b\u0018\u00010\n\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0012\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u00a2\u0006\u0004\b\u0016\u0010\u0017J\u0010\u0010.\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0019J\u000b\u0010/\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u00100\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u00101\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0017\u00102\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000b\u0018\u00010\nH\u00c6\u0003J\u0010\u00103\u001a\u0004\u0018\u00010\rH\u00c6\u0003\u00a2\u0006\u0002\u0010#J\u000b\u00104\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u00105\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u00106\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u00107\u001a\u0004\u0018\u00010\u0012H\u00c6\u0003J\u000b\u00108\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u00109\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003\u00a2\u0006\u0002\u0010,J\u00aa\u0001\u0010:\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00052\u0016\b\u0002\u0010\t\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000b\u0018\u00010\n2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00122\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0015H\u00c6\u0001\u00a2\u0006\u0002\u0010;J\u0013\u0010<\u001a\u00020\u00152\b\u0010=\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010>\u001a\u00020\rH\u00d6\u0001J\t\u0010?\u001a\u00020\u0005H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u0018\u0010\u0019R\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0015\u0010\b\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001cR!\u0010\t\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000b\u0018\u00010\n8G\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0017\u0010\f\u001a\u0004\u0018\u00010\r8G\u00a2\u0006\n\n\u0002\u0010$\u001a\u0004\b\"\u0010#R\u0015\u0010\u000e\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u001cR\u0015\u0010\u000f\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001cR\u0015\u0010\u0010\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b'\u0010\u001cR\u0015\u0010\u0011\u001a\u0004\u0018\u00010\u00128G\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0015\u0010\u0013\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u001cR\u0017\u0010\u0014\u001a\u0004\u0018\u00010\u00158G\u00a2\u0006\n\n\u0002\u0010-\u001a\u0004\b+\u0010,\u00a8\u0006@"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueTypeIssueCreateMetadata;", "", "avatarId", "", "description", "", "entityId", "Ljava/util/UUID;", "expand", "fields", "", "Lai/zencoder/generated/client/jira/models/FieldMetadata;", "hierarchyLevel", "", "iconUrl", "id", "NAME", "scope", "Lai/zencoder/generated/client/jira/models/Scope;", "self", "subtask", "", "<init>", "(Ljava/lang/Long;Ljava/lang/String;Ljava/util/UUID;Ljava/lang/String;Ljava/util/Map;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/Scope;Ljava/lang/String;Ljava/lang/Boolean;)V", "getAvatarId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getDescription", "()Ljava/lang/String;", "getEntityId", "()Ljava/util/UUID;", "getExpand", "getFields", "()Ljava/util/Map;", "getHierarchyLevel", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getIconUrl", "getId", "getNAME", "getScope", "()Lai/zencoder/generated/client/jira/models/Scope;", "getSelf", "getSubtask", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "copy", "(Ljava/lang/Long;Ljava/lang/String;Ljava/util/UUID;Ljava/lang/String;Ljava/util/Map;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/Scope;Ljava/lang/String;Ljava/lang/Boolean;)Lai/zencoder/generated/client/jira/models/IssueTypeIssueCreateMetadata;", "equals", "other", "hashCode", "toString", "zencoder"}
)
public final class IssueTypeIssueCreateMetadata {
   @Nullable
   private final Long avatarId;
   @Nullable
   private final String description;
   @Nullable
   private final UUID entityId;
   @Nullable
   private final String expand;
   @Nullable
   private final Map<String, FieldMetadata> fields;
   @Nullable
   private final Integer hierarchyLevel;
   @Nullable
   private final String iconUrl;
   @Nullable
   private final String id;
   @Nullable
   private final String NAME;
   @Nullable
   private final Scope scope;
   @Nullable
   private final String self;
   @Nullable
   private final Boolean subtask;

   public IssueTypeIssueCreateMetadata(@Nullable Long avatarId, @Nullable String description, @Nullable UUID entityId, @Nullable String expand, @Nullable Map<String, FieldMetadata> fields, @Nullable Integer hierarchyLevel, @Nullable String iconUrl, @Nullable String id, @Nullable String NAME, @Nullable Scope scope, @Nullable String self, @Nullable Boolean subtask) {
      this.avatarId = avatarId;
      this.description = description;
      this.entityId = entityId;
      this.expand = expand;
      this.fields = fields;
      this.hierarchyLevel = hierarchyLevel;
      this.iconUrl = iconUrl;
      this.id = id;
      this.NAME = NAME;
      this.scope = scope;
      this.self = self;
      this.subtask = subtask;
   }

   @JsonProperty("avatarId")
   @Nullable
   public final Long getAvatarId() {
      return this.avatarId;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("entityId")
   @Nullable
   public final UUID getEntityId() {
      return this.entityId;
   }

   @JsonProperty("expand")
   @Nullable
   public final String getExpand() {
      return this.expand;
   }

   @JsonProperty("fields")
   @Nullable
   public final Map<String, FieldMetadata> getFields() {
      return this.fields;
   }

   @JsonProperty("hierarchyLevel")
   @Nullable
   public final Integer getHierarchyLevel() {
      return this.hierarchyLevel;
   }

   @JsonProperty("iconUrl")
   @Nullable
   public final String getIconUrl() {
      return this.iconUrl;
   }

   @JsonProperty("id")
   @Nullable
   public final String getId() {
      return this.id;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("scope")
   @Nullable
   public final Scope getScope() {
      return this.scope;
   }

   @JsonProperty("self")
   @Nullable
   public final String getSelf() {
      return this.self;
   }

   @JsonProperty("subtask")
   @Nullable
   public final Boolean getSubtask() {
      return this.subtask;
   }

   @Nullable
   public final Long component1() {
      return this.avatarId;
   }

   @Nullable
   public final String component2() {
      return this.description;
   }

   @Nullable
   public final UUID component3() {
      return this.entityId;
   }

   @Nullable
   public final String component4() {
      return this.expand;
   }

   @Nullable
   public final Map<String, FieldMetadata> component5() {
      return this.fields;
   }

   @Nullable
   public final Integer component6() {
      return this.hierarchyLevel;
   }

   @Nullable
   public final String component7() {
      return this.iconUrl;
   }

   @Nullable
   public final String component8() {
      return this.id;
   }

   @Nullable
   public final String component9() {
      return this.NAME;
   }

   @Nullable
   public final Scope component10() {
      return this.scope;
   }

   @Nullable
   public final String component11() {
      return this.self;
   }

   @Nullable
   public final Boolean component12() {
      return this.subtask;
   }

   @NotNull
   public final IssueTypeIssueCreateMetadata copy(@Nullable Long avatarId, @Nullable String description, @Nullable UUID entityId, @Nullable String expand, @Nullable Map<String, FieldMetadata> fields, @Nullable Integer hierarchyLevel, @Nullable String iconUrl, @Nullable String id, @Nullable String NAME, @Nullable Scope scope, @Nullable String self, @Nullable Boolean subtask) {
      return new IssueTypeIssueCreateMetadata(avatarId, description, entityId, expand, fields, hierarchyLevel, iconUrl, id, NAME, scope, self, subtask);
   }

   @NotNull
   public String toString() {
      return "IssueTypeIssueCreateMetadata(avatarId=" + this.avatarId + ", description=" + this.description + ", entityId=" + this.entityId + ", expand=" + this.expand + ", fields=" + this.fields + ", hierarchyLevel=" + this.hierarchyLevel + ", iconUrl=" + this.iconUrl + ", id=" + this.id + ", NAME=" + this.NAME + ", scope=" + this.scope + ", self=" + this.self + ", subtask=" + this.subtask + ")";
   }

   public int hashCode() {
      int var1 = this.avatarId == null ? 0 : this.avatarId.hashCode();
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.entityId == null ? 0 : this.entityId.hashCode());
      var1 = var1 * 31 + (this.expand == null ? 0 : this.expand.hashCode());
      var1 = var1 * 31 + (this.fields == null ? 0 : this.fields.hashCode());
      var1 = var1 * 31 + (this.hierarchyLevel == null ? 0 : this.hierarchyLevel.hashCode());
      var1 = var1 * 31 + (this.iconUrl == null ? 0 : this.iconUrl.hashCode());
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.scope == null ? 0 : this.scope.hashCode());
      var1 = var1 * 31 + (this.self == null ? 0 : this.self.hashCode());
      var1 = var1 * 31 + (this.subtask == null ? 0 : this.subtask.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueTypeIssueCreateMetadata)) {
         return false;
      } else {
         IssueTypeIssueCreateMetadata var2 = (IssueTypeIssueCreateMetadata)other;
         if (!Intrinsics.areEqual(this.avatarId, var2.avatarId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.entityId, var2.entityId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.expand, var2.expand)) {
            return false;
         } else if (!Intrinsics.areEqual(this.fields, var2.fields)) {
            return false;
         } else if (!Intrinsics.areEqual(this.hierarchyLevel, var2.hierarchyLevel)) {
            return false;
         } else if (!Intrinsics.areEqual(this.iconUrl, var2.iconUrl)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else if (!Intrinsics.areEqual(this.scope, var2.scope)) {
            return false;
         } else if (!Intrinsics.areEqual(this.self, var2.self)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.subtask, var2.subtask);
         }
      }
   }

   public IssueTypeIssueCreateMetadata() {
      this((Long)null, (String)null, (UUID)null, (String)null, (Map)null, (Integer)null, (String)null, (String)null, (String)null, (Scope)null, (String)null, (Boolean)null, 4095, (DefaultConstructorMarker)null);
   }
}
