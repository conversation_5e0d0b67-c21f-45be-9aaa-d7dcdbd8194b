package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\r\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J-\u0010\u000f\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\t\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueTypeSchemeUpdateDetails;", "", "defaultIssueTypeId", "", "description", "NAME", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getDefaultIssueTypeId", "()Ljava/lang/String;", "getDescription", "getNAME", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class IssueTypeSchemeUpdateDetails {
   @Nullable
   private final String defaultIssueTypeId;
   @Nullable
   private final String description;
   @Nullable
   private final String NAME;

   public IssueTypeSchemeUpdateDetails(@Nullable String defaultIssueTypeId, @Nullable String description, @Nullable String NAME) {
      this.defaultIssueTypeId = defaultIssueTypeId;
      this.description = description;
      this.NAME = NAME;
   }

   @JsonProperty("defaultIssueTypeId")
   @Nullable
   public final String getDefaultIssueTypeId() {
      return this.defaultIssueTypeId;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @Nullable
   public final String component1() {
      return this.defaultIssueTypeId;
   }

   @Nullable
   public final String component2() {
      return this.description;
   }

   @Nullable
   public final String component3() {
      return this.NAME;
   }

   @NotNull
   public final IssueTypeSchemeUpdateDetails copy(@Nullable String defaultIssueTypeId, @Nullable String description, @Nullable String NAME) {
      return new IssueTypeSchemeUpdateDetails(defaultIssueTypeId, description, NAME);
   }

   @NotNull
   public String toString() {
      return "IssueTypeSchemeUpdateDetails(defaultIssueTypeId=" + this.defaultIssueTypeId + ", description=" + this.description + ", NAME=" + this.NAME + ")";
   }

   public int hashCode() {
      int var1 = this.defaultIssueTypeId == null ? 0 : this.defaultIssueTypeId.hashCode();
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueTypeSchemeUpdateDetails)) {
         return false;
      } else {
         IssueTypeSchemeUpdateDetails var2 = (IssueTypeSchemeUpdateDetails)other;
         if (!Intrinsics.areEqual(this.defaultIssueTypeId, var2.defaultIssueTypeId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.NAME, var2.NAME);
         }
      }
   }

   public IssueTypeSchemeUpdateDetails() {
      this((String)null, (String)null, (String)null, 7, (DefaultConstructorMarker)null);
   }
}
