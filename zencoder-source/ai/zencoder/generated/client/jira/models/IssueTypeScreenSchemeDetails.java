package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\b\u0010\tJ\u000f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J1\u0010\u0012\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0006H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\r\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueTypeScreenSchemeDetails;", "", "issueTypeMappings", "", "Lai/zencoder/generated/client/jira/models/IssueTypeScreenSchemeMapping;", "description", "", "NAME", "<init>", "(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;)V", "getIssueTypeMappings", "()Ljava/util/List;", "getDescription", "()Ljava/lang/String;", "getNAME", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class IssueTypeScreenSchemeDetails {
   @NotNull
   private final List<IssueTypeScreenSchemeMapping> issueTypeMappings;
   @Nullable
   private final String description;
   @Nullable
   private final String NAME;

   public IssueTypeScreenSchemeDetails(@NotNull List<IssueTypeScreenSchemeMapping> issueTypeMappings, @Nullable String description, @Nullable String NAME) {
      Intrinsics.checkNotNullParameter(issueTypeMappings, "");
      super();
      this.issueTypeMappings = issueTypeMappings;
      this.description = description;
      this.NAME = NAME;
   }

   @JsonProperty("issueTypeMappings")
   @NotNull
   public final List<IssueTypeScreenSchemeMapping> getIssueTypeMappings() {
      return this.issueTypeMappings;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @NotNull
   public final List<IssueTypeScreenSchemeMapping> component1() {
      return this.issueTypeMappings;
   }

   @Nullable
   public final String component2() {
      return this.description;
   }

   @Nullable
   public final String component3() {
      return this.NAME;
   }

   @NotNull
   public final IssueTypeScreenSchemeDetails copy(@NotNull List<IssueTypeScreenSchemeMapping> issueTypeMappings, @Nullable String description, @Nullable String NAME) {
      Intrinsics.checkNotNullParameter(issueTypeMappings, "");
      return new IssueTypeScreenSchemeDetails(issueTypeMappings, description, NAME);
   }

   @NotNull
   public String toString() {
      return "IssueTypeScreenSchemeDetails(issueTypeMappings=" + this.issueTypeMappings + ", description=" + this.description + ", NAME=" + this.NAME + ")";
   }

   public int hashCode() {
      int var1 = this.issueTypeMappings.hashCode();
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueTypeScreenSchemeDetails)) {
         return false;
      } else {
         IssueTypeScreenSchemeDetails var2 = (IssueTypeScreenSchemeDetails)other;
         if (!Intrinsics.areEqual(this.issueTypeMappings, var2.issueTypeMappings)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.NAME, var2.NAME);
         }
      }
   }
}
