package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J'\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0013\u0010\u0005\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\t\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueTypeScreenSchemeItem;", "", "issueTypeId", "", "issueTypeScreenSchemeId", "screenSchemeId", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getIssueTypeId", "()Ljava/lang/String;", "getIssueTypeScreenSchemeId", "getScreenSchemeId", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class IssueTypeScreenSchemeItem {
   @NotNull
   private final String issueTypeId;
   @NotNull
   private final String issueTypeScreenSchemeId;
   @NotNull
   private final String screenSchemeId;

   public IssueTypeScreenSchemeItem(@NotNull String issueTypeId, @NotNull String issueTypeScreenSchemeId, @NotNull String screenSchemeId) {
      Intrinsics.checkNotNullParameter(issueTypeId, "");
      Intrinsics.checkNotNullParameter(issueTypeScreenSchemeId, "");
      Intrinsics.checkNotNullParameter(screenSchemeId, "");
      super();
      this.issueTypeId = issueTypeId;
      this.issueTypeScreenSchemeId = issueTypeScreenSchemeId;
      this.screenSchemeId = screenSchemeId;
   }

   @JsonProperty("issueTypeId")
   @NotNull
   public final String getIssueTypeId() {
      return this.issueTypeId;
   }

   @JsonProperty("issueTypeScreenSchemeId")
   @NotNull
   public final String getIssueTypeScreenSchemeId() {
      return this.issueTypeScreenSchemeId;
   }

   @JsonProperty("screenSchemeId")
   @NotNull
   public final String getScreenSchemeId() {
      return this.screenSchemeId;
   }

   @NotNull
   public final String component1() {
      return this.issueTypeId;
   }

   @NotNull
   public final String component2() {
      return this.issueTypeScreenSchemeId;
   }

   @NotNull
   public final String component3() {
      return this.screenSchemeId;
   }

   @NotNull
   public final IssueTypeScreenSchemeItem copy(@NotNull String issueTypeId, @NotNull String issueTypeScreenSchemeId, @NotNull String screenSchemeId) {
      Intrinsics.checkNotNullParameter(issueTypeId, "");
      Intrinsics.checkNotNullParameter(issueTypeScreenSchemeId, "");
      Intrinsics.checkNotNullParameter(screenSchemeId, "");
      return new IssueTypeScreenSchemeItem(issueTypeId, issueTypeScreenSchemeId, screenSchemeId);
   }

   @NotNull
   public String toString() {
      return "IssueTypeScreenSchemeItem(issueTypeId=" + this.issueTypeId + ", issueTypeScreenSchemeId=" + this.issueTypeScreenSchemeId + ", screenSchemeId=" + this.screenSchemeId + ")";
   }

   public int hashCode() {
      int var1 = this.issueTypeId.hashCode();
      var1 = var1 * 31 + this.issueTypeScreenSchemeId.hashCode();
      var1 = var1 * 31 + this.screenSchemeId.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueTypeScreenSchemeItem)) {
         return false;
      } else {
         IssueTypeScreenSchemeItem var2 = (IssueTypeScreenSchemeItem)other;
         if (!Intrinsics.areEqual(this.issueTypeId, var2.issueTypeId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueTypeScreenSchemeId, var2.issueTypeScreenSchemeId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.screenSchemeId, var2.screenSchemeId);
         }
      }
   }
}
