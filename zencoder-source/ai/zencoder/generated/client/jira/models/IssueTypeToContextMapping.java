package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0010\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000bJ\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J0\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0012J\u0013\u0010\u0013\u001a\u00020\u00052\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\f\u001a\u0004\b\u0004\u0010\u000bR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\n\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueTypeToContextMapping;", "", "contextId", "", "isAnyIssueType", "", "issueTypeId", "<init>", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;)V", "getContextId", "()Ljava/lang/String;", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getIssueTypeId", "component1", "component2", "component3", "copy", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/IssueTypeToContextMapping;", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class IssueTypeToContextMapping {
   @NotNull
   private final String contextId;
   @Nullable
   private final Boolean isAnyIssueType;
   @Nullable
   private final String issueTypeId;

   public IssueTypeToContextMapping(@NotNull String contextId, @Nullable Boolean isAnyIssueType, @Nullable String issueTypeId) {
      Intrinsics.checkNotNullParameter(contextId, "");
      super();
      this.contextId = contextId;
      this.isAnyIssueType = isAnyIssueType;
      this.issueTypeId = issueTypeId;
   }

   @JsonProperty("contextId")
   @NotNull
   public final String getContextId() {
      return this.contextId;
   }

   @JsonProperty("isAnyIssueType")
   @Nullable
   public final Boolean isAnyIssueType() {
      return this.isAnyIssueType;
   }

   @JsonProperty("issueTypeId")
   @Nullable
   public final String getIssueTypeId() {
      return this.issueTypeId;
   }

   @NotNull
   public final String component1() {
      return this.contextId;
   }

   @Nullable
   public final Boolean component2() {
      return this.isAnyIssueType;
   }

   @Nullable
   public final String component3() {
      return this.issueTypeId;
   }

   @NotNull
   public final IssueTypeToContextMapping copy(@NotNull String contextId, @Nullable Boolean isAnyIssueType, @Nullable String issueTypeId) {
      Intrinsics.checkNotNullParameter(contextId, "");
      return new IssueTypeToContextMapping(contextId, isAnyIssueType, issueTypeId);
   }

   @NotNull
   public String toString() {
      return "IssueTypeToContextMapping(contextId=" + this.contextId + ", isAnyIssueType=" + this.isAnyIssueType + ", issueTypeId=" + this.issueTypeId + ")";
   }

   public int hashCode() {
      int var1 = this.contextId.hashCode();
      var1 = var1 * 31 + (this.isAnyIssueType == null ? 0 : this.isAnyIssueType.hashCode());
      var1 = var1 * 31 + (this.issueTypeId == null ? 0 : this.issueTypeId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueTypeToContextMapping)) {
         return false;
      } else {
         IssueTypeToContextMapping var2 = (IssueTypeToContextMapping)other;
         if (!Intrinsics.areEqual(this.contextId, var2.contextId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.isAnyIssueType, var2.isAnyIssueType)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.issueTypeId, var2.issueTypeId);
         }
      }
   }
}
