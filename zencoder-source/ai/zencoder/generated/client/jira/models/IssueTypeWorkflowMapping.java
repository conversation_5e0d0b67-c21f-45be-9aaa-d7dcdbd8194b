package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0011\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\fJ\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J2\u0010\u0012\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0013J\u0013\u0010\u0014\u001a\u00020\u00052\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\r\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\n\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueTypeWorkflowMapping;", "", "issueType", "", "updateDraftIfNeeded", "", "workflow", "<init>", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;)V", "getIssueType", "()Ljava/lang/String;", "getUpdateDraftIfNeeded", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getWorkflow", "component1", "component2", "component3", "copy", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/IssueTypeWorkflowMapping;", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class IssueTypeWorkflowMapping {
   @Nullable
   private final String issueType;
   @Nullable
   private final Boolean updateDraftIfNeeded;
   @Nullable
   private final String workflow;

   public IssueTypeWorkflowMapping(@Nullable String issueType, @Nullable Boolean updateDraftIfNeeded, @Nullable String workflow) {
      this.issueType = issueType;
      this.updateDraftIfNeeded = updateDraftIfNeeded;
      this.workflow = workflow;
   }

   @JsonProperty("issueType")
   @Nullable
   public final String getIssueType() {
      return this.issueType;
   }

   @JsonProperty("updateDraftIfNeeded")
   @Nullable
   public final Boolean getUpdateDraftIfNeeded() {
      return this.updateDraftIfNeeded;
   }

   @JsonProperty("workflow")
   @Nullable
   public final String getWorkflow() {
      return this.workflow;
   }

   @Nullable
   public final String component1() {
      return this.issueType;
   }

   @Nullable
   public final Boolean component2() {
      return this.updateDraftIfNeeded;
   }

   @Nullable
   public final String component3() {
      return this.workflow;
   }

   @NotNull
   public final IssueTypeWorkflowMapping copy(@Nullable String issueType, @Nullable Boolean updateDraftIfNeeded, @Nullable String workflow) {
      return new IssueTypeWorkflowMapping(issueType, updateDraftIfNeeded, workflow);
   }

   @NotNull
   public String toString() {
      return "IssueTypeWorkflowMapping(issueType=" + this.issueType + ", updateDraftIfNeeded=" + this.updateDraftIfNeeded + ", workflow=" + this.workflow + ")";
   }

   public int hashCode() {
      int var1 = this.issueType == null ? 0 : this.issueType.hashCode();
      var1 = var1 * 31 + (this.updateDraftIfNeeded == null ? 0 : this.updateDraftIfNeeded.hashCode());
      var1 = var1 * 31 + (this.workflow == null ? 0 : this.workflow.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueTypeWorkflowMapping)) {
         return false;
      } else {
         IssueTypeWorkflowMapping var2 = (IssueTypeWorkflowMapping)other;
         if (!Intrinsics.areEqual(this.issueType, var2.issueType)) {
            return false;
         } else if (!Intrinsics.areEqual(this.updateDraftIfNeeded, var2.updateDraftIfNeeded)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.workflow, var2.workflow);
         }
      }
   }

   public IssueTypeWorkflowMapping() {
      this((String)null, (Boolean)null, (String)null, 7, (DefaultConstructorMarker)null);
   }
}
