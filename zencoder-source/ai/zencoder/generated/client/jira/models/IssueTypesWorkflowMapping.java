package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0015\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\t\u0010\nJ\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\fJ\u0011\u0010\u0014\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\fJ\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003JD\u0010\u0017\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0018J\u0013\u0010\u0019\u001a\u00020\u00032\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u0006H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\r\u001a\u0004\b\u000b\u0010\fR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\r\u001a\u0004\b\u0010\u0010\fR\u0015\u0010\b\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006\u001e"},
   d2 = {"Lai/zencoder/generated/client/jira/models/IssueTypesWorkflowMapping;", "", "defaultMapping", "", "issueTypes", "", "", "updateDraftIfNeeded", "workflow", "<init>", "(Ljava/lang/Boolean;Ljava/util/List;Ljava/lang/Boolean;Ljava/lang/String;)V", "getDefaultMapping", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getIssueTypes", "()Ljava/util/List;", "getUpdateDraftIfNeeded", "getWorkflow", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "copy", "(Ljava/lang/Boolean;Ljava/util/List;Ljava/lang/Boolean;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/IssueTypesWorkflowMapping;", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class IssueTypesWorkflowMapping {
   @Nullable
   private final Boolean defaultMapping;
   @Nullable
   private final List<String> issueTypes;
   @Nullable
   private final Boolean updateDraftIfNeeded;
   @Nullable
   private final String workflow;

   public IssueTypesWorkflowMapping(@Nullable Boolean defaultMapping, @Nullable List<String> issueTypes, @Nullable Boolean updateDraftIfNeeded, @Nullable String workflow) {
      this.defaultMapping = defaultMapping;
      this.issueTypes = issueTypes;
      this.updateDraftIfNeeded = updateDraftIfNeeded;
      this.workflow = workflow;
   }

   @JsonProperty("defaultMapping")
   @Nullable
   public final Boolean getDefaultMapping() {
      return this.defaultMapping;
   }

   @JsonProperty("issueTypes")
   @Nullable
   public final List<String> getIssueTypes() {
      return this.issueTypes;
   }

   @JsonProperty("updateDraftIfNeeded")
   @Nullable
   public final Boolean getUpdateDraftIfNeeded() {
      return this.updateDraftIfNeeded;
   }

   @JsonProperty("workflow")
   @Nullable
   public final String getWorkflow() {
      return this.workflow;
   }

   @Nullable
   public final Boolean component1() {
      return this.defaultMapping;
   }

   @Nullable
   public final List<String> component2() {
      return this.issueTypes;
   }

   @Nullable
   public final Boolean component3() {
      return this.updateDraftIfNeeded;
   }

   @Nullable
   public final String component4() {
      return this.workflow;
   }

   @NotNull
   public final IssueTypesWorkflowMapping copy(@Nullable Boolean defaultMapping, @Nullable List<String> issueTypes, @Nullable Boolean updateDraftIfNeeded, @Nullable String workflow) {
      return new IssueTypesWorkflowMapping(defaultMapping, issueTypes, updateDraftIfNeeded, workflow);
   }

   @NotNull
   public String toString() {
      return "IssueTypesWorkflowMapping(defaultMapping=" + this.defaultMapping + ", issueTypes=" + this.issueTypes + ", updateDraftIfNeeded=" + this.updateDraftIfNeeded + ", workflow=" + this.workflow + ")";
   }

   public int hashCode() {
      int var1 = this.defaultMapping == null ? 0 : this.defaultMapping.hashCode();
      var1 = var1 * 31 + (this.issueTypes == null ? 0 : this.issueTypes.hashCode());
      var1 = var1 * 31 + (this.updateDraftIfNeeded == null ? 0 : this.updateDraftIfNeeded.hashCode());
      var1 = var1 * 31 + (this.workflow == null ? 0 : this.workflow.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof IssueTypesWorkflowMapping)) {
         return false;
      } else {
         IssueTypesWorkflowMapping var2 = (IssueTypesWorkflowMapping)other;
         if (!Intrinsics.areEqual(this.defaultMapping, var2.defaultMapping)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueTypes, var2.issueTypes)) {
            return false;
         } else if (!Intrinsics.areEqual(this.updateDraftIfNeeded, var2.updateDraftIfNeeded)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.workflow, var2.workflow);
         }
      }
   }

   public IssueTypesWorkflowMapping() {
      this((Boolean)null, (List)null, (Boolean)null, (String)null, 15, (DefaultConstructorMarker)null);
   }
}
