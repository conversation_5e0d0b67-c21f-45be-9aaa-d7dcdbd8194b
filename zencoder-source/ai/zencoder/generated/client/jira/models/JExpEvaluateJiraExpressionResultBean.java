package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0001\u0012\n\b\u0002\u0010\u0003\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\b\u0005\u0010\u0006J\u000b\u0010\u000b\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J!\u0010\r\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00012\n\b\u0002\u0010\u0003\u001a\u0004\u0018\u00010\u0004H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0015\u0010\u0003\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0015"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JExpEvaluateJiraExpressionResultBean;", "", "value", "meta", "Lai/zencoder/generated/client/jira/models/JExpEvaluateMetaDataBean;", "<init>", "(Ljava/lang/Object;Lai/zencoder/generated/client/jira/models/JExpEvaluateMetaDataBean;)V", "getValue", "()Ljava/lang/Object;", "getMeta", "()Lai/zencoder/generated/client/jira/models/JExpEvaluateMetaDataBean;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class JExpEvaluateJiraExpressionResultBean {
   @Nullable
   private final Object value;
   @Nullable
   private final JExpEvaluateMetaDataBean meta;

   public JExpEvaluateJiraExpressionResultBean(@Nullable Object value, @Nullable JExpEvaluateMetaDataBean meta) {
      this.value = value;
      this.meta = meta;
   }

   @JsonProperty("value")
   @Nullable
   public final Object getValue() {
      return this.value;
   }

   @JsonProperty("meta")
   @Nullable
   public final JExpEvaluateMetaDataBean getMeta() {
      return this.meta;
   }

   @Nullable
   public final Object component1() {
      return this.value;
   }

   @Nullable
   public final JExpEvaluateMetaDataBean component2() {
      return this.meta;
   }

   @NotNull
   public final JExpEvaluateJiraExpressionResultBean copy(@Nullable Object value, @Nullable JExpEvaluateMetaDataBean meta) {
      return new JExpEvaluateJiraExpressionResultBean(value, meta);
   }

   @NotNull
   public String toString() {
      return "JExpEvaluateJiraExpressionResultBean(value=" + this.value + ", meta=" + this.meta + ")";
   }

   public int hashCode() {
      int var1 = this.value == null ? 0 : this.value.hashCode();
      var1 = var1 * 31 + (this.meta == null ? 0 : this.meta.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JExpEvaluateJiraExpressionResultBean)) {
         return false;
      } else {
         JExpEvaluateJiraExpressionResultBean var2 = (JExpEvaluateJiraExpressionResultBean)other;
         if (!Intrinsics.areEqual(this.value, var2.value)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.meta, var2.meta);
         }
      }
   }
}
