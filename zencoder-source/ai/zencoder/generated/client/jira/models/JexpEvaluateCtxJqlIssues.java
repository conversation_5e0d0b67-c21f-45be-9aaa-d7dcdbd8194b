package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\nJ\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J2\u0010\u0012\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0013J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0005H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000b\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\r\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JexpEvaluateCtxJqlIssues;", "", "maxResults", "", "nextPageToken", "", "query", "<init>", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;)V", "getMaxResults", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getNextPageToken", "()Ljava/lang/String;", "getQuery", "component1", "component2", "component3", "copy", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/JexpEvaluateCtxJqlIssues;", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class JexpEvaluateCtxJqlIssues {
   @Nullable
   private final Integer maxResults;
   @Nullable
   private final String nextPageToken;
   @Nullable
   private final String query;

   public JexpEvaluateCtxJqlIssues(@Nullable Integer maxResults, @Nullable String nextPageToken, @Nullable String query) {
      this.maxResults = maxResults;
      this.nextPageToken = nextPageToken;
      this.query = query;
   }

   @JsonProperty("maxResults")
   @Nullable
   public final Integer getMaxResults() {
      return this.maxResults;
   }

   @JsonProperty("nextPageToken")
   @Nullable
   public final String getNextPageToken() {
      return this.nextPageToken;
   }

   @JsonProperty("query")
   @Nullable
   public final String getQuery() {
      return this.query;
   }

   @Nullable
   public final Integer component1() {
      return this.maxResults;
   }

   @Nullable
   public final String component2() {
      return this.nextPageToken;
   }

   @Nullable
   public final String component3() {
      return this.query;
   }

   @NotNull
   public final JexpEvaluateCtxJqlIssues copy(@Nullable Integer maxResults, @Nullable String nextPageToken, @Nullable String query) {
      return new JexpEvaluateCtxJqlIssues(maxResults, nextPageToken, query);
   }

   @NotNull
   public String toString() {
      return "JexpEvaluateCtxJqlIssues(maxResults=" + this.maxResults + ", nextPageToken=" + this.nextPageToken + ", query=" + this.query + ")";
   }

   public int hashCode() {
      int var1 = this.maxResults == null ? 0 : this.maxResults.hashCode();
      var1 = var1 * 31 + (this.nextPageToken == null ? 0 : this.nextPageToken.hashCode());
      var1 = var1 * 31 + (this.query == null ? 0 : this.query.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JexpEvaluateCtxJqlIssues)) {
         return false;
      } else {
         JexpEvaluateCtxJqlIssues var2 = (JexpEvaluateCtxJqlIssues)other;
         if (!Intrinsics.areEqual(this.maxResults, var2.maxResults)) {
            return false;
         } else if (!Intrinsics.areEqual(this.nextPageToken, var2.nextPageToken)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.query, var2.query);
         }
      }
   }

   public JexpEvaluateCtxJqlIssues() {
      this((Integer)null, (String)null, (String)null, 7, (DefaultConstructorMarker)null);
   }
}
