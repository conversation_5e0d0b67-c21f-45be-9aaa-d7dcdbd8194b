package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B#\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J)\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\f\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraCascadingSelectField;", "", "fieldId", "", "parentOptionValue", "Lai/zencoder/generated/client/jira/models/JiraSelectedOptionField;", "childOptionValue", "<init>", "(Ljava/lang/String;Lai/zencoder/generated/client/jira/models/JiraSelectedOptionField;Lai/zencoder/generated/client/jira/models/JiraSelectedOptionField;)V", "getFieldId", "()Ljava/lang/String;", "getParentOptionValue", "()Lai/zencoder/generated/client/jira/models/JiraSelectedOptionField;", "getChildOptionValue", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class JiraCascadingSelectField {
   @NotNull
   private final String fieldId;
   @NotNull
   private final JiraSelectedOptionField parentOptionValue;
   @Nullable
   private final JiraSelectedOptionField childOptionValue;

   public JiraCascadingSelectField(@NotNull String fieldId, @NotNull JiraSelectedOptionField parentOptionValue, @Nullable JiraSelectedOptionField childOptionValue) {
      Intrinsics.checkNotNullParameter(fieldId, "");
      Intrinsics.checkNotNullParameter(parentOptionValue, "");
      super();
      this.fieldId = fieldId;
      this.parentOptionValue = parentOptionValue;
      this.childOptionValue = childOptionValue;
   }

   @JsonProperty("fieldId")
   @NotNull
   public final String getFieldId() {
      return this.fieldId;
   }

   @JsonProperty("parentOptionValue")
   @NotNull
   public final JiraSelectedOptionField getParentOptionValue() {
      return this.parentOptionValue;
   }

   @JsonProperty("childOptionValue")
   @Nullable
   public final JiraSelectedOptionField getChildOptionValue() {
      return this.childOptionValue;
   }

   @NotNull
   public final String component1() {
      return this.fieldId;
   }

   @NotNull
   public final JiraSelectedOptionField component2() {
      return this.parentOptionValue;
   }

   @Nullable
   public final JiraSelectedOptionField component3() {
      return this.childOptionValue;
   }

   @NotNull
   public final JiraCascadingSelectField copy(@NotNull String fieldId, @NotNull JiraSelectedOptionField parentOptionValue, @Nullable JiraSelectedOptionField childOptionValue) {
      Intrinsics.checkNotNullParameter(fieldId, "");
      Intrinsics.checkNotNullParameter(parentOptionValue, "");
      return new JiraCascadingSelectField(fieldId, parentOptionValue, childOptionValue);
   }

   @NotNull
   public String toString() {
      return "JiraCascadingSelectField(fieldId=" + this.fieldId + ", parentOptionValue=" + this.parentOptionValue + ", childOptionValue=" + this.childOptionValue + ")";
   }

   public int hashCode() {
      int var1 = this.fieldId.hashCode();
      var1 = var1 * 31 + this.parentOptionValue.hashCode();
      var1 = var1 * 31 + (this.childOptionValue == null ? 0 : this.childOptionValue.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraCascadingSelectField)) {
         return false;
      } else {
         JiraCascadingSelectField var2 = (JiraCascadingSelectField)other;
         if (!Intrinsics.areEqual(this.fieldId, var2.fieldId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.parentOptionValue, var2.parentOptionValue)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.childOptionValue, var2.childOptionValue);
         }
      }
   }
}
