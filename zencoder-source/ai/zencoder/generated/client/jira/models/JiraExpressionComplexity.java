package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010$\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0016\b\u0002\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\u0017\u0010\r\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005H\u00c6\u0003J+\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0016\b\u0002\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR!\u0010\u0004\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraExpressionComplexity;", "", "expensiveOperations", "", "variables", "", "<init>", "(Ljava/lang/String;Ljava/util/Map;)V", "getExpensiveOperations", "()Ljava/lang/String;", "getVariables", "()Ljava/util/Map;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class JiraExpressionComplexity {
   @NotNull
   private final String expensiveOperations;
   @Nullable
   private final Map<String, String> variables;

   public JiraExpressionComplexity(@NotNull String expensiveOperations, @Nullable Map<String, String> variables) {
      Intrinsics.checkNotNullParameter(expensiveOperations, "");
      super();
      this.expensiveOperations = expensiveOperations;
      this.variables = variables;
   }

   @JsonProperty("expensiveOperations")
   @NotNull
   public final String getExpensiveOperations() {
      return this.expensiveOperations;
   }

   @JsonProperty("variables")
   @Nullable
   public final Map<String, String> getVariables() {
      return this.variables;
   }

   @NotNull
   public final String component1() {
      return this.expensiveOperations;
   }

   @Nullable
   public final Map<String, String> component2() {
      return this.variables;
   }

   @NotNull
   public final JiraExpressionComplexity copy(@NotNull String expensiveOperations, @Nullable Map<String, String> variables) {
      Intrinsics.checkNotNullParameter(expensiveOperations, "");
      return new JiraExpressionComplexity(expensiveOperations, variables);
   }

   @NotNull
   public String toString() {
      return "JiraExpressionComplexity(expensiveOperations=" + this.expensiveOperations + ", variables=" + this.variables + ")";
   }

   public int hashCode() {
      int var1 = this.expensiveOperations.hashCode();
      var1 = var1 * 31 + (this.variables == null ? 0 : this.variables.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraExpressionComplexity)) {
         return false;
      } else {
         JiraExpressionComplexity var2 = (JiraExpressionComplexity)other;
         if (!Intrinsics.areEqual(this.expensiveOperations, var2.expensiveOperations)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.variables, var2.variables);
         }
      }
   }
}
