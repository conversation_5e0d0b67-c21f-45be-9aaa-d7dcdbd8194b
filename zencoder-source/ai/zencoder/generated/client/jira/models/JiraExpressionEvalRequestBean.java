package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001b\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\r\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u001f\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraExpressionEvalRequestBean;", "", "expression", "", "context", "Lai/zencoder/generated/client/jira/models/JiraExpressionEvalContextBean;", "<init>", "(Ljava/lang/String;Lai/zencoder/generated/client/jira/models/JiraExpressionEvalContextBean;)V", "getExpression", "()Ljava/lang/String;", "getContext", "()Lai/zencoder/generated/client/jira/models/JiraExpressionEvalContextBean;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class JiraExpressionEvalRequestBean {
   @NotNull
   private final String expression;
   @Nullable
   private final JiraExpressionEvalContextBean context;

   public JiraExpressionEvalRequestBean(@NotNull String expression, @Nullable JiraExpressionEvalContextBean context) {
      Intrinsics.checkNotNullParameter(expression, "");
      super();
      this.expression = expression;
      this.context = context;
   }

   @JsonProperty("expression")
   @NotNull
   public final String getExpression() {
      return this.expression;
   }

   @JsonProperty("context")
   @Nullable
   public final JiraExpressionEvalContextBean getContext() {
      return this.context;
   }

   @NotNull
   public final String component1() {
      return this.expression;
   }

   @Nullable
   public final JiraExpressionEvalContextBean component2() {
      return this.context;
   }

   @NotNull
   public final JiraExpressionEvalRequestBean copy(@NotNull String expression, @Nullable JiraExpressionEvalContextBean context) {
      Intrinsics.checkNotNullParameter(expression, "");
      return new JiraExpressionEvalRequestBean(expression, context);
   }

   @NotNull
   public String toString() {
      return "JiraExpressionEvalRequestBean(expression=" + this.expression + ", context=" + this.context + ")";
   }

   public int hashCode() {
      int var1 = this.expression.hashCode();
      var1 = var1 * 31 + (this.context == null ? 0 : this.context.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraExpressionEvalRequestBean)) {
         return false;
      } else {
         JiraExpressionEvalRequestBean var2 = (JiraExpressionEvalRequestBean)other;
         if (!Intrinsics.areEqual(this.expression, var2.expression)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.context, var2.context);
         }
      }
   }
}
