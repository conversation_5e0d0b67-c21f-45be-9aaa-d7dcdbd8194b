package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001d\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001Bm\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0012J\u0011\u0010\u001f\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010 \u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0012J\u000b\u0010!\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u0010\u0010$\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0012J\u0010\u0010%\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0012Jt\u0010&\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010'J\u0013\u0010(\u001a\u00020)2\b\u0010*\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010+\u001a\u00020,H\u00d6\u0001J\t\u0010-\u001a\u00020.H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u0013\u001a\u0004\b\u0011\u0010\u0012R\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u0013\u001a\u0004\b\u0016\u0010\u0012R\u0015\u0010\b\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0015\u0010\n\u001a\u0004\u0018\u00010\u000b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0015\u0010\f\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0018R\u0017\u0010\r\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u0013\u001a\u0004\b\u001c\u0010\u0012R\u0017\u0010\u000e\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u0013\u001a\u0004\b\u001d\u0010\u0012\u00a8\u0006/"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraExpressionEvaluateContextBean;", "", "board", "", "custom", "", "Lai/zencoder/generated/client/jira/models/CustomContextVariable;", "customerRequest", "issue", "Lai/zencoder/generated/client/jira/models/IdOrKeyBean;", "issues", "Lai/zencoder/generated/client/jira/models/JexpEvaluateCtxIssues;", "project", "serviceDesk", "sprint", "<init>", "(Ljava/lang/Long;Ljava/util/List;Ljava/lang/Long;Lai/zencoder/generated/client/jira/models/IdOrKeyBean;Lai/zencoder/generated/client/jira/models/JexpEvaluateCtxIssues;Lai/zencoder/generated/client/jira/models/IdOrKeyBean;Ljava/lang/Long;Ljava/lang/Long;)V", "getBoard", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getCustom", "()Ljava/util/List;", "getCustomerRequest", "getIssue", "()Lai/zencoder/generated/client/jira/models/IdOrKeyBean;", "getIssues", "()Lai/zencoder/generated/client/jira/models/JexpEvaluateCtxIssues;", "getProject", "getServiceDesk", "getSprint", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "(Ljava/lang/Long;Ljava/util/List;Ljava/lang/Long;Lai/zencoder/generated/client/jira/models/IdOrKeyBean;Lai/zencoder/generated/client/jira/models/JexpEvaluateCtxIssues;Lai/zencoder/generated/client/jira/models/IdOrKeyBean;Ljava/lang/Long;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/JiraExpressionEvaluateContextBean;", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class JiraExpressionEvaluateContextBean {
   @Nullable
   private final Long board;
   @Nullable
   private final List<CustomContextVariable> custom;
   @Nullable
   private final Long customerRequest;
   @Nullable
   private final IdOrKeyBean issue;
   @Nullable
   private final JexpEvaluateCtxIssues issues;
   @Nullable
   private final IdOrKeyBean project;
   @Nullable
   private final Long serviceDesk;
   @Nullable
   private final Long sprint;

   public JiraExpressionEvaluateContextBean(@Nullable Long board, @Nullable List<? extends CustomContextVariable> custom, @Nullable Long customerRequest, @Nullable IdOrKeyBean issue, @Nullable JexpEvaluateCtxIssues issues, @Nullable IdOrKeyBean project, @Nullable Long serviceDesk, @Nullable Long sprint) {
      this.board = board;
      this.custom = custom;
      this.customerRequest = customerRequest;
      this.issue = issue;
      this.issues = issues;
      this.project = project;
      this.serviceDesk = serviceDesk;
      this.sprint = sprint;
   }

   @JsonProperty("board")
   @Nullable
   public final Long getBoard() {
      return this.board;
   }

   @JsonProperty("custom")
   @Nullable
   public final List<CustomContextVariable> getCustom() {
      return this.custom;
   }

   @JsonProperty("customerRequest")
   @Nullable
   public final Long getCustomerRequest() {
      return this.customerRequest;
   }

   @JsonProperty("issue")
   @Nullable
   public final IdOrKeyBean getIssue() {
      return this.issue;
   }

   @JsonProperty("issues")
   @Nullable
   public final JexpEvaluateCtxIssues getIssues() {
      return this.issues;
   }

   @JsonProperty("project")
   @Nullable
   public final IdOrKeyBean getProject() {
      return this.project;
   }

   @JsonProperty("serviceDesk")
   @Nullable
   public final Long getServiceDesk() {
      return this.serviceDesk;
   }

   @JsonProperty("sprint")
   @Nullable
   public final Long getSprint() {
      return this.sprint;
   }

   @Nullable
   public final Long component1() {
      return this.board;
   }

   @Nullable
   public final List<CustomContextVariable> component2() {
      return this.custom;
   }

   @Nullable
   public final Long component3() {
      return this.customerRequest;
   }

   @Nullable
   public final IdOrKeyBean component4() {
      return this.issue;
   }

   @Nullable
   public final JexpEvaluateCtxIssues component5() {
      return this.issues;
   }

   @Nullable
   public final IdOrKeyBean component6() {
      return this.project;
   }

   @Nullable
   public final Long component7() {
      return this.serviceDesk;
   }

   @Nullable
   public final Long component8() {
      return this.sprint;
   }

   @NotNull
   public final JiraExpressionEvaluateContextBean copy(@Nullable Long board, @Nullable List<? extends CustomContextVariable> custom, @Nullable Long customerRequest, @Nullable IdOrKeyBean issue, @Nullable JexpEvaluateCtxIssues issues, @Nullable IdOrKeyBean project, @Nullable Long serviceDesk, @Nullable Long sprint) {
      return new JiraExpressionEvaluateContextBean(board, custom, customerRequest, issue, issues, project, serviceDesk, sprint);
   }

   @NotNull
   public String toString() {
      return "JiraExpressionEvaluateContextBean(board=" + this.board + ", custom=" + this.custom + ", customerRequest=" + this.customerRequest + ", issue=" + this.issue + ", issues=" + this.issues + ", project=" + this.project + ", serviceDesk=" + this.serviceDesk + ", sprint=" + this.sprint + ")";
   }

   public int hashCode() {
      int var1 = this.board == null ? 0 : this.board.hashCode();
      var1 = var1 * 31 + (this.custom == null ? 0 : this.custom.hashCode());
      var1 = var1 * 31 + (this.customerRequest == null ? 0 : this.customerRequest.hashCode());
      var1 = var1 * 31 + (this.issue == null ? 0 : this.issue.hashCode());
      var1 = var1 * 31 + (this.issues == null ? 0 : this.issues.hashCode());
      var1 = var1 * 31 + (this.project == null ? 0 : this.project.hashCode());
      var1 = var1 * 31 + (this.serviceDesk == null ? 0 : this.serviceDesk.hashCode());
      var1 = var1 * 31 + (this.sprint == null ? 0 : this.sprint.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraExpressionEvaluateContextBean)) {
         return false;
      } else {
         JiraExpressionEvaluateContextBean var2 = (JiraExpressionEvaluateContextBean)other;
         if (!Intrinsics.areEqual(this.board, var2.board)) {
            return false;
         } else if (!Intrinsics.areEqual(this.custom, var2.custom)) {
            return false;
         } else if (!Intrinsics.areEqual(this.customerRequest, var2.customerRequest)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issue, var2.issue)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issues, var2.issues)) {
            return false;
         } else if (!Intrinsics.areEqual(this.project, var2.project)) {
            return false;
         } else if (!Intrinsics.areEqual(this.serviceDesk, var2.serviceDesk)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.sprint, var2.sprint);
         }
      }
   }

   public JiraExpressionEvaluateContextBean() {
      this((Long)null, (List)null, (Long)null, (IdOrKeyBean)null, (JexpEvaluateCtxIssues)null, (IdOrKeyBean)null, (Long)null, (Long)null, 255, (DefaultConstructorMarker)null);
   }
}
