package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\r\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J!\u0010\u000e\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraExpressionEvaluationMetaDataBean;", "", "complexity", "Lai/zencoder/generated/client/jira/models/JiraExpressionsComplexityBean;", "issues", "Lai/zencoder/generated/client/jira/models/IssuesMetaBean;", "<init>", "(Lai/zencoder/generated/client/jira/models/JiraExpressionsComplexityBean;Lai/zencoder/generated/client/jira/models/IssuesMetaBean;)V", "getComplexity", "()Lai/zencoder/generated/client/jira/models/JiraExpressionsComplexityBean;", "getIssues", "()Lai/zencoder/generated/client/jira/models/IssuesMetaBean;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class JiraExpressionEvaluationMetaDataBean {
   @Nullable
   private final JiraExpressionsComplexityBean complexity;
   @Nullable
   private final IssuesMetaBean issues;

   public JiraExpressionEvaluationMetaDataBean(@Nullable JiraExpressionsComplexityBean complexity, @Nullable IssuesMetaBean issues) {
      this.complexity = complexity;
      this.issues = issues;
   }

   @JsonProperty("complexity")
   @Nullable
   public final JiraExpressionsComplexityBean getComplexity() {
      return this.complexity;
   }

   @JsonProperty("issues")
   @Nullable
   public final IssuesMetaBean getIssues() {
      return this.issues;
   }

   @Nullable
   public final JiraExpressionsComplexityBean component1() {
      return this.complexity;
   }

   @Nullable
   public final IssuesMetaBean component2() {
      return this.issues;
   }

   @NotNull
   public final JiraExpressionEvaluationMetaDataBean copy(@Nullable JiraExpressionsComplexityBean complexity, @Nullable IssuesMetaBean issues) {
      return new JiraExpressionEvaluationMetaDataBean(complexity, issues);
   }

   @NotNull
   public String toString() {
      return "JiraExpressionEvaluationMetaDataBean(complexity=" + this.complexity + ", issues=" + this.issues + ")";
   }

   public int hashCode() {
      int var1 = this.complexity == null ? 0 : this.complexity.hashCode();
      var1 = var1 * 31 + (this.issues == null ? 0 : this.issues.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraExpressionEvaluationMetaDataBean)) {
         return false;
      } else {
         JiraExpressionEvaluationMetaDataBean var2 = (JiraExpressionEvaluationMetaDataBean)other;
         if (!Intrinsics.areEqual(this.complexity, var2.complexity)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.issues, var2.issues);
         }
      }
   }

   public JiraExpressionEvaluationMetaDataBean() {
      this((JiraExpressionsComplexityBean)null, (IssuesMetaBean)null, 3, (DefaultConstructorMarker)null);
   }
}
