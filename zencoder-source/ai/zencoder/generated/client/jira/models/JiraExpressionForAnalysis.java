package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010$\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0016\b\u0002\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u0017\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0006H\u00c6\u0003J1\u0010\u000f\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0016\b\u0002\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0004H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR!\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraExpressionForAnalysis;", "", "expressions", "", "", "contextVariables", "", "<init>", "(Ljava/util/List;Ljava/util/Map;)V", "getExpressions", "()Ljava/util/List;", "getContextVariables", "()Ljava/util/Map;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class JiraExpressionForAnalysis {
   @NotNull
   private final List<String> expressions;
   @Nullable
   private final Map<String, String> contextVariables;

   public JiraExpressionForAnalysis(@NotNull List<String> expressions, @Nullable Map<String, String> contextVariables) {
      Intrinsics.checkNotNullParameter(expressions, "");
      super();
      this.expressions = expressions;
      this.contextVariables = contextVariables;
   }

   @JsonProperty("expressions")
   @NotNull
   public final List<String> getExpressions() {
      return this.expressions;
   }

   @JsonProperty("contextVariables")
   @Nullable
   public final Map<String, String> getContextVariables() {
      return this.contextVariables;
   }

   @NotNull
   public final List<String> component1() {
      return this.expressions;
   }

   @Nullable
   public final Map<String, String> component2() {
      return this.contextVariables;
   }

   @NotNull
   public final JiraExpressionForAnalysis copy(@NotNull List<String> expressions, @Nullable Map<String, String> contextVariables) {
      Intrinsics.checkNotNullParameter(expressions, "");
      return new JiraExpressionForAnalysis(expressions, contextVariables);
   }

   @NotNull
   public String toString() {
      return "JiraExpressionForAnalysis(expressions=" + this.expressions + ", contextVariables=" + this.contextVariables + ")";
   }

   public int hashCode() {
      int var1 = this.expressions.hashCode();
      var1 = var1 * 31 + (this.contextVariables == null ? 0 : this.contextVariables.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraExpressionForAnalysis)) {
         return false;
      } else {
         JiraExpressionForAnalysis var2 = (JiraExpressionForAnalysis)other;
         if (!Intrinsics.areEqual(this.expressions, var2.expressions)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.contextVariables, var2.contextVariables);
         }
      }
   }
}
