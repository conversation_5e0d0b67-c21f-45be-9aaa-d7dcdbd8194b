package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0005\b\u0086\b\u0018\u00002\u00020\u0001:\u0001!B;\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\n\u0010\u000bJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0005H\u00c6\u0003J\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0011J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0011JF\u0010\u001a\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010\u001bJ\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020\u0007H\u00d6\u0001J\t\u0010 \u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0010\u0010\u0011R\u0015\u0010\b\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\rR\u0017\u0010\t\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0014\u0010\u0011\u00a8\u0006\""},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraExpressionValidationError;", "", "message", "", "type", "Lai/zencoder/generated/client/jira/models/JiraExpressionValidationError$Type;", "column", "", "expression", "line", "<init>", "(Ljava/lang/String;Lai/zencoder/generated/client/jira/models/JiraExpressionValidationError$Type;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;)V", "getMessage", "()Ljava/lang/String;", "getType", "()Lai/zencoder/generated/client/jira/models/JiraExpressionValidationError$Type;", "getColumn", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getExpression", "getLine", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/lang/String;Lai/zencoder/generated/client/jira/models/JiraExpressionValidationError$Type;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;)Lai/zencoder/generated/client/jira/models/JiraExpressionValidationError;", "equals", "", "other", "hashCode", "toString", "Type", "zencoder"}
)
public final class JiraExpressionValidationError {
   @NotNull
   private final String message;
   @NotNull
   private final Type type;
   @Nullable
   private final Integer column;
   @Nullable
   private final String expression;
   @Nullable
   private final Integer line;

   public JiraExpressionValidationError(@NotNull String message, @NotNull Type type, @Nullable Integer column, @Nullable String expression, @Nullable Integer line) {
      Intrinsics.checkNotNullParameter(message, "");
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.message = message;
      this.type = type;
      this.column = column;
      this.expression = expression;
      this.line = line;
   }

   @JsonProperty("message")
   @NotNull
   public final String getMessage() {
      return this.message;
   }

   @JsonProperty("type")
   @NotNull
   public final Type getType() {
      return this.type;
   }

   @JsonProperty("column")
   @Nullable
   public final Integer getColumn() {
      return this.column;
   }

   @JsonProperty("expression")
   @Nullable
   public final String getExpression() {
      return this.expression;
   }

   @JsonProperty("line")
   @Nullable
   public final Integer getLine() {
      return this.line;
   }

   @NotNull
   public final String component1() {
      return this.message;
   }

   @NotNull
   public final Type component2() {
      return this.type;
   }

   @Nullable
   public final Integer component3() {
      return this.column;
   }

   @Nullable
   public final String component4() {
      return this.expression;
   }

   @Nullable
   public final Integer component5() {
      return this.line;
   }

   @NotNull
   public final JiraExpressionValidationError copy(@NotNull String message, @NotNull Type type, @Nullable Integer column, @Nullable String expression, @Nullable Integer line) {
      Intrinsics.checkNotNullParameter(message, "");
      Intrinsics.checkNotNullParameter(type, "");
      return new JiraExpressionValidationError(message, type, column, expression, line);
   }

   @NotNull
   public String toString() {
      return "JiraExpressionValidationError(message=" + this.message + ", type=" + this.type + ", column=" + this.column + ", expression=" + this.expression + ", line=" + this.line + ")";
   }

   public int hashCode() {
      int var1 = this.message.hashCode();
      var1 = var1 * 31 + this.type.hashCode();
      var1 = var1 * 31 + (this.column == null ? 0 : this.column.hashCode());
      var1 = var1 * 31 + (this.expression == null ? 0 : this.expression.hashCode());
      var1 = var1 * 31 + (this.line == null ? 0 : this.line.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraExpressionValidationError)) {
         return false;
      } else {
         JiraExpressionValidationError var2 = (JiraExpressionValidationError)other;
         if (!Intrinsics.areEqual(this.message, var2.message)) {
            return false;
         } else if (this.type != var2.type) {
            return false;
         } else if (!Intrinsics.areEqual(this.column, var2.column)) {
            return false;
         } else if (!Intrinsics.areEqual(this.expression, var2.expression)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.line, var2.line);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"},
      d2 = {"Lai/zencoder/generated/client/jira/models/JiraExpressionValidationError$Type;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "syntax", "type", "other", "zencoder"}
   )
   public static enum Type {
      @NotNull
      private final String value;
      @JsonProperty("syntax")
      syntax("syntax"),
      @JsonProperty("type")
      type("type"),
      @JsonProperty("other")
      other("other");

      private Type(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Type> getEntries() {
         return $ENTRIES;
      }
   }
}
