package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J1\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0013\u0010\u0005\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\nR\u0013\u0010\u0006\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\n\u00a8\u0006\u001a"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraExpressionsComplexityBean;", "", "beans", "Lai/zencoder/generated/client/jira/models/JiraExpressionsComplexityValueBean;", "expensiveOperations", "primitiveValues", "steps", "<init>", "(Lai/zencoder/generated/client/jira/models/JiraExpressionsComplexityValueBean;Lai/zencoder/generated/client/jira/models/JiraExpressionsComplexityValueBean;Lai/zencoder/generated/client/jira/models/JiraExpressionsComplexityValueBean;Lai/zencoder/generated/client/jira/models/JiraExpressionsComplexityValueBean;)V", "getBeans", "()Lai/zencoder/generated/client/jira/models/JiraExpressionsComplexityValueBean;", "getExpensiveOperations", "getPrimitiveValues", "getSteps", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class JiraExpressionsComplexityBean {
   @NotNull
   private final JiraExpressionsComplexityValueBean beans;
   @NotNull
   private final JiraExpressionsComplexityValueBean expensiveOperations;
   @NotNull
   private final JiraExpressionsComplexityValueBean primitiveValues;
   @NotNull
   private final JiraExpressionsComplexityValueBean steps;

   public JiraExpressionsComplexityBean(@NotNull JiraExpressionsComplexityValueBean beans, @NotNull JiraExpressionsComplexityValueBean expensiveOperations, @NotNull JiraExpressionsComplexityValueBean primitiveValues, @NotNull JiraExpressionsComplexityValueBean steps) {
      Intrinsics.checkNotNullParameter(beans, "");
      Intrinsics.checkNotNullParameter(expensiveOperations, "");
      Intrinsics.checkNotNullParameter(primitiveValues, "");
      Intrinsics.checkNotNullParameter(steps, "");
      super();
      this.beans = beans;
      this.expensiveOperations = expensiveOperations;
      this.primitiveValues = primitiveValues;
      this.steps = steps;
   }

   @JsonProperty("beans")
   @NotNull
   public final JiraExpressionsComplexityValueBean getBeans() {
      return this.beans;
   }

   @JsonProperty("expensiveOperations")
   @NotNull
   public final JiraExpressionsComplexityValueBean getExpensiveOperations() {
      return this.expensiveOperations;
   }

   @JsonProperty("primitiveValues")
   @NotNull
   public final JiraExpressionsComplexityValueBean getPrimitiveValues() {
      return this.primitiveValues;
   }

   @JsonProperty("steps")
   @NotNull
   public final JiraExpressionsComplexityValueBean getSteps() {
      return this.steps;
   }

   @NotNull
   public final JiraExpressionsComplexityValueBean component1() {
      return this.beans;
   }

   @NotNull
   public final JiraExpressionsComplexityValueBean component2() {
      return this.expensiveOperations;
   }

   @NotNull
   public final JiraExpressionsComplexityValueBean component3() {
      return this.primitiveValues;
   }

   @NotNull
   public final JiraExpressionsComplexityValueBean component4() {
      return this.steps;
   }

   @NotNull
   public final JiraExpressionsComplexityBean copy(@NotNull JiraExpressionsComplexityValueBean beans, @NotNull JiraExpressionsComplexityValueBean expensiveOperations, @NotNull JiraExpressionsComplexityValueBean primitiveValues, @NotNull JiraExpressionsComplexityValueBean steps) {
      Intrinsics.checkNotNullParameter(beans, "");
      Intrinsics.checkNotNullParameter(expensiveOperations, "");
      Intrinsics.checkNotNullParameter(primitiveValues, "");
      Intrinsics.checkNotNullParameter(steps, "");
      return new JiraExpressionsComplexityBean(beans, expensiveOperations, primitiveValues, steps);
   }

   @NotNull
   public String toString() {
      return "JiraExpressionsComplexityBean(beans=" + this.beans + ", expensiveOperations=" + this.expensiveOperations + ", primitiveValues=" + this.primitiveValues + ", steps=" + this.steps + ")";
   }

   public int hashCode() {
      int var1 = this.beans.hashCode();
      var1 = var1 * 31 + this.expensiveOperations.hashCode();
      var1 = var1 * 31 + this.primitiveValues.hashCode();
      var1 = var1 * 31 + this.steps.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraExpressionsComplexityBean)) {
         return false;
      } else {
         JiraExpressionsComplexityBean var2 = (JiraExpressionsComplexityBean)other;
         if (!Intrinsics.areEqual(this.beans, var2.beans)) {
            return false;
         } else if (!Intrinsics.areEqual(this.expensiveOperations, var2.expensiveOperations)) {
            return false;
         } else if (!Intrinsics.areEqual(this.primitiveValues, var2.primitiveValues)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.steps, var2.steps);
         }
      }
   }
}
