package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u001bB%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0004\b\t\u0010\nJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\bH\u00c6\u0003J-\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\bH\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0007\u001a\u00020\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraMultiSelectComponentField;", "", "bulkEditMultiSelectFieldOption", "Lai/zencoder/generated/client/jira/models/JiraMultiSelectComponentField$BulkEditMultiSelectFieldOption;", "components", "", "Lai/zencoder/generated/client/jira/models/JiraComponentField;", "fieldId", "", "<init>", "(Lai/zencoder/generated/client/jira/models/JiraMultiSelectComponentField$BulkEditMultiSelectFieldOption;Ljava/util/List;Ljava/lang/String;)V", "getBulkEditMultiSelectFieldOption", "()Lai/zencoder/generated/client/jira/models/JiraMultiSelectComponentField$BulkEditMultiSelectFieldOption;", "getComponents", "()Ljava/util/List;", "getFieldId", "()Ljava/lang/String;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "BulkEditMultiSelectFieldOption", "zencoder"}
)
public final class JiraMultiSelectComponentField {
   @NotNull
   private final BulkEditMultiSelectFieldOption bulkEditMultiSelectFieldOption;
   @NotNull
   private final List<JiraComponentField> components;
   @NotNull
   private final String fieldId;

   public JiraMultiSelectComponentField(@NotNull BulkEditMultiSelectFieldOption bulkEditMultiSelectFieldOption, @NotNull List<JiraComponentField> components, @NotNull String fieldId) {
      Intrinsics.checkNotNullParameter(bulkEditMultiSelectFieldOption, "");
      Intrinsics.checkNotNullParameter(components, "");
      Intrinsics.checkNotNullParameter(fieldId, "");
      super();
      this.bulkEditMultiSelectFieldOption = bulkEditMultiSelectFieldOption;
      this.components = components;
      this.fieldId = fieldId;
   }

   @JsonProperty("bulkEditMultiSelectFieldOption")
   @NotNull
   public final BulkEditMultiSelectFieldOption getBulkEditMultiSelectFieldOption() {
      return this.bulkEditMultiSelectFieldOption;
   }

   @JsonProperty("components")
   @NotNull
   public final List<JiraComponentField> getComponents() {
      return this.components;
   }

   @JsonProperty("fieldId")
   @NotNull
   public final String getFieldId() {
      return this.fieldId;
   }

   @NotNull
   public final BulkEditMultiSelectFieldOption component1() {
      return this.bulkEditMultiSelectFieldOption;
   }

   @NotNull
   public final List<JiraComponentField> component2() {
      return this.components;
   }

   @NotNull
   public final String component3() {
      return this.fieldId;
   }

   @NotNull
   public final JiraMultiSelectComponentField copy(@NotNull BulkEditMultiSelectFieldOption bulkEditMultiSelectFieldOption, @NotNull List<JiraComponentField> components, @NotNull String fieldId) {
      Intrinsics.checkNotNullParameter(bulkEditMultiSelectFieldOption, "");
      Intrinsics.checkNotNullParameter(components, "");
      Intrinsics.checkNotNullParameter(fieldId, "");
      return new JiraMultiSelectComponentField(bulkEditMultiSelectFieldOption, components, fieldId);
   }

   @NotNull
   public String toString() {
      return "JiraMultiSelectComponentField(bulkEditMultiSelectFieldOption=" + this.bulkEditMultiSelectFieldOption + ", components=" + this.components + ", fieldId=" + this.fieldId + ")";
   }

   public int hashCode() {
      int var1 = this.bulkEditMultiSelectFieldOption.hashCode();
      var1 = var1 * 31 + this.components.hashCode();
      var1 = var1 * 31 + this.fieldId.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraMultiSelectComponentField)) {
         return false;
      } else {
         JiraMultiSelectComponentField var2 = (JiraMultiSelectComponentField)other;
         if (this.bulkEditMultiSelectFieldOption != var2.bulkEditMultiSelectFieldOption) {
            return false;
         } else if (!Intrinsics.areEqual(this.components, var2.components)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.fieldId, var2.fieldId);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"},
      d2 = {"Lai/zencoder/generated/client/jira/models/JiraMultiSelectComponentField$BulkEditMultiSelectFieldOption;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "ADD", "REMOVE", "REPLACE", "REMOVE_ALL", "zencoder"}
   )
   public static enum BulkEditMultiSelectFieldOption {
      @NotNull
      private final String value;
      @JsonProperty("ADD")
      ADD("ADD"),
      @JsonProperty("REMOVE")
      REMOVE("REMOVE"),
      @JsonProperty("REPLACE")
      REPLACE("REPLACE"),
      @JsonProperty("REMOVE_ALL")
      REMOVE_ALL("REMOVE_ALL");

      private BulkEditMultiSelectFieldOption(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<BulkEditMultiSelectFieldOption> getEntries() {
         return $ENTRIES;
      }
   }
}
