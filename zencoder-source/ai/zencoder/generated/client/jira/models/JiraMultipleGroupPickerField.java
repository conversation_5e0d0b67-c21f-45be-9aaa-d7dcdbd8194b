package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J#\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraMultipleGroupPickerField;", "", "fieldId", "", "groups", "", "Lai/zencoder/generated/client/jira/models/JiraGroupInput;", "<init>", "(Ljava/lang/String;Ljava/util/List;)V", "getFieldId", "()Ljava/lang/String;", "getGroups", "()Ljava/util/List;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class JiraMultipleGroupPickerField {
   @NotNull
   private final String fieldId;
   @NotNull
   private final List<JiraGroupInput> groups;

   public JiraMultipleGroupPickerField(@NotNull String fieldId, @NotNull List<JiraGroupInput> groups) {
      Intrinsics.checkNotNullParameter(fieldId, "");
      Intrinsics.checkNotNullParameter(groups, "");
      super();
      this.fieldId = fieldId;
      this.groups = groups;
   }

   @JsonProperty("fieldId")
   @NotNull
   public final String getFieldId() {
      return this.fieldId;
   }

   @JsonProperty("groups")
   @NotNull
   public final List<JiraGroupInput> getGroups() {
      return this.groups;
   }

   @NotNull
   public final String component1() {
      return this.fieldId;
   }

   @NotNull
   public final List<JiraGroupInput> component2() {
      return this.groups;
   }

   @NotNull
   public final JiraMultipleGroupPickerField copy(@NotNull String fieldId, @NotNull List<JiraGroupInput> groups) {
      Intrinsics.checkNotNullParameter(fieldId, "");
      Intrinsics.checkNotNullParameter(groups, "");
      return new JiraMultipleGroupPickerField(fieldId, groups);
   }

   @NotNull
   public String toString() {
      return "JiraMultipleGroupPickerField(fieldId=" + this.fieldId + ", groups=" + this.groups + ")";
   }

   public int hashCode() {
      int var1 = this.fieldId.hashCode();
      var1 = var1 * 31 + this.groups.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraMultipleGroupPickerField)) {
         return false;
      } else {
         JiraMultipleGroupPickerField var2 = (JiraMultipleGroupPickerField)other;
         if (!Intrinsics.areEqual(this.fieldId, var2.fieldId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.groups, var2.groups);
         }
      }
   }
}
