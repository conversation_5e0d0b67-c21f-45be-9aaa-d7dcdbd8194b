package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B!\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\u0011\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J%\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraMultipleSelectUserPickerField;", "", "fieldId", "", "users", "", "Lai/zencoder/generated/client/jira/models/JiraUserField;", "<init>", "(Ljava/lang/String;Ljava/util/List;)V", "getFieldId", "()Ljava/lang/String;", "getUsers", "()Ljava/util/List;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class JiraMultipleSelectUserPickerField {
   @NotNull
   private final String fieldId;
   @Nullable
   private final List<JiraUserField> users;

   public JiraMultipleSelectUserPickerField(@NotNull String fieldId, @Nullable List<JiraUserField> users) {
      Intrinsics.checkNotNullParameter(fieldId, "");
      super();
      this.fieldId = fieldId;
      this.users = users;
   }

   @JsonProperty("fieldId")
   @NotNull
   public final String getFieldId() {
      return this.fieldId;
   }

   @JsonProperty("users")
   @Nullable
   public final List<JiraUserField> getUsers() {
      return this.users;
   }

   @NotNull
   public final String component1() {
      return this.fieldId;
   }

   @Nullable
   public final List<JiraUserField> component2() {
      return this.users;
   }

   @NotNull
   public final JiraMultipleSelectUserPickerField copy(@NotNull String fieldId, @Nullable List<JiraUserField> users) {
      Intrinsics.checkNotNullParameter(fieldId, "");
      return new JiraMultipleSelectUserPickerField(fieldId, users);
   }

   @NotNull
   public String toString() {
      return "JiraMultipleSelectUserPickerField(fieldId=" + this.fieldId + ", users=" + this.users + ")";
   }

   public int hashCode() {
      int var1 = this.fieldId.hashCode();
      var1 = var1 * 31 + (this.users == null ? 0 : this.users.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraMultipleSelectUserPickerField)) {
         return false;
      } else {
         JiraMultipleSelectUserPickerField var2 = (JiraMultipleSelectUserPickerField)other;
         if (!Intrinsics.areEqual(this.fieldId, var2.fieldId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.users, var2.users);
         }
      }
   }
}
