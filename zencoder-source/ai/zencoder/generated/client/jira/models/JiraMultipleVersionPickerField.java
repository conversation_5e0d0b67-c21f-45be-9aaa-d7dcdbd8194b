package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u001bB%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\u0004\b\t\u0010\nJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J\u000f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J-\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0019\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JiraMultipleVersionPickerField;", "", "bulkEditMultiSelectFieldOption", "Lai/zencoder/generated/client/jira/models/JiraMultipleVersionPickerField$BulkEditMultiSelectFieldOption;", "fieldId", "", "versions", "", "Lai/zencoder/generated/client/jira/models/JiraVersionField;", "<init>", "(Lai/zencoder/generated/client/jira/models/JiraMultipleVersionPickerField$BulkEditMultiSelectFieldOption;Ljava/lang/String;Ljava/util/List;)V", "getBulkEditMultiSelectFieldOption", "()Lai/zencoder/generated/client/jira/models/JiraMultipleVersionPickerField$BulkEditMultiSelectFieldOption;", "getFieldId", "()Ljava/lang/String;", "getVersions", "()Ljava/util/List;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "BulkEditMultiSelectFieldOption", "zencoder"}
)
public final class JiraMultipleVersionPickerField {
   @NotNull
   private final BulkEditMultiSelectFieldOption bulkEditMultiSelectFieldOption;
   @NotNull
   private final String fieldId;
   @NotNull
   private final List<JiraVersionField> versions;

   public JiraMultipleVersionPickerField(@NotNull BulkEditMultiSelectFieldOption bulkEditMultiSelectFieldOption, @NotNull String fieldId, @NotNull List<JiraVersionField> versions) {
      Intrinsics.checkNotNullParameter(bulkEditMultiSelectFieldOption, "");
      Intrinsics.checkNotNullParameter(fieldId, "");
      Intrinsics.checkNotNullParameter(versions, "");
      super();
      this.bulkEditMultiSelectFieldOption = bulkEditMultiSelectFieldOption;
      this.fieldId = fieldId;
      this.versions = versions;
   }

   @JsonProperty("bulkEditMultiSelectFieldOption")
   @NotNull
   public final BulkEditMultiSelectFieldOption getBulkEditMultiSelectFieldOption() {
      return this.bulkEditMultiSelectFieldOption;
   }

   @JsonProperty("fieldId")
   @NotNull
   public final String getFieldId() {
      return this.fieldId;
   }

   @JsonProperty("versions")
   @NotNull
   public final List<JiraVersionField> getVersions() {
      return this.versions;
   }

   @NotNull
   public final BulkEditMultiSelectFieldOption component1() {
      return this.bulkEditMultiSelectFieldOption;
   }

   @NotNull
   public final String component2() {
      return this.fieldId;
   }

   @NotNull
   public final List<JiraVersionField> component3() {
      return this.versions;
   }

   @NotNull
   public final JiraMultipleVersionPickerField copy(@NotNull BulkEditMultiSelectFieldOption bulkEditMultiSelectFieldOption, @NotNull String fieldId, @NotNull List<JiraVersionField> versions) {
      Intrinsics.checkNotNullParameter(bulkEditMultiSelectFieldOption, "");
      Intrinsics.checkNotNullParameter(fieldId, "");
      Intrinsics.checkNotNullParameter(versions, "");
      return new JiraMultipleVersionPickerField(bulkEditMultiSelectFieldOption, fieldId, versions);
   }

   @NotNull
   public String toString() {
      return "JiraMultipleVersionPickerField(bulkEditMultiSelectFieldOption=" + this.bulkEditMultiSelectFieldOption + ", fieldId=" + this.fieldId + ", versions=" + this.versions + ")";
   }

   public int hashCode() {
      int var1 = this.bulkEditMultiSelectFieldOption.hashCode();
      var1 = var1 * 31 + this.fieldId.hashCode();
      var1 = var1 * 31 + this.versions.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JiraMultipleVersionPickerField)) {
         return false;
      } else {
         JiraMultipleVersionPickerField var2 = (JiraMultipleVersionPickerField)other;
         if (this.bulkEditMultiSelectFieldOption != var2.bulkEditMultiSelectFieldOption) {
            return false;
         } else if (!Intrinsics.areEqual(this.fieldId, var2.fieldId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.versions, var2.versions);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"},
      d2 = {"Lai/zencoder/generated/client/jira/models/JiraMultipleVersionPickerField$BulkEditMultiSelectFieldOption;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "ADD", "REMOVE", "REPLACE", "REMOVE_ALL", "zencoder"}
   )
   public static enum BulkEditMultiSelectFieldOption {
      @NotNull
      private final String value;
      @JsonProperty("ADD")
      ADD("ADD"),
      @JsonProperty("REMOVE")
      REMOVE("REMOVE"),
      @JsonProperty("REPLACE")
      REPLACE("REPLACE"),
      @JsonProperty("REMOVE_ALL")
      REMOVE_ALL("REMOVE_ALL");

      private BulkEditMultiSelectFieldOption(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<BulkEditMultiSelectFieldOption> getEntries() {
         return $ENTRIES;
      }
   }
}
