package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.OffsetDateTime;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b&\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0091\u0001\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0004\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0004\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0004\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0004\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0004\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0004\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\b\u0010\u0010\u0011J\u0011\u0010 \u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u000b\u0010'\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u000b\u0010(\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010)\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010*\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u0093\u0001\u0010+\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0004H\u00c6\u0001J\u0013\u0010,\u001a\u00020-2\b\u0010.\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010/\u001a\u000200H\u00d6\u0001J\t\u00101\u001a\u00020\u0004H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0015\u0010\b\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0017R\u0015\u0010\t\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0017R\u0015\u0010\n\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0017R\u0015\u0010\u000b\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0017R\u0015\u0010\f\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0017R\u0015\u0010\r\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0015R\u0015\u0010\u000e\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0015R\u0015\u0010\u000f\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0017\u00a8\u00062"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JqlFunctionPrecomputationBean;", "", "arguments", "", "", "created", "Ljava/time/OffsetDateTime;", "error", "field", "functionKey", "functionName", "id", "operator", "updated", "used", "value", "<init>", "(Ljava/util/List;Ljava/time/OffsetDateTime;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/time/OffsetDateTime;Ljava/time/OffsetDateTime;Ljava/lang/String;)V", "getArguments", "()Ljava/util/List;", "getCreated", "()Ljava/time/OffsetDateTime;", "getError", "()Ljava/lang/String;", "getField", "getFunctionKey", "getFunctionName", "getId", "getOperator", "getUpdated", "getUsed", "getValue", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class JqlFunctionPrecomputationBean {
   @Nullable
   private final List<String> arguments;
   @Nullable
   private final OffsetDateTime created;
   @Nullable
   private final String error;
   @Nullable
   private final String field;
   @Nullable
   private final String functionKey;
   @Nullable
   private final String functionName;
   @Nullable
   private final String id;
   @Nullable
   private final String operator;
   @Nullable
   private final OffsetDateTime updated;
   @Nullable
   private final OffsetDateTime used;
   @Nullable
   private final String value;

   public JqlFunctionPrecomputationBean(@Nullable List<String> arguments, @Nullable OffsetDateTime created, @Nullable String error, @Nullable String field, @Nullable String functionKey, @Nullable String functionName, @Nullable String id, @Nullable String operator, @Nullable OffsetDateTime updated, @Nullable OffsetDateTime used, @Nullable String value) {
      this.arguments = arguments;
      this.created = created;
      this.error = error;
      this.field = field;
      this.functionKey = functionKey;
      this.functionName = functionName;
      this.id = id;
      this.operator = operator;
      this.updated = updated;
      this.used = used;
      this.value = value;
   }

   @JsonProperty("arguments")
   @Nullable
   public final List<String> getArguments() {
      return this.arguments;
   }

   @JsonProperty("created")
   @Nullable
   public final OffsetDateTime getCreated() {
      return this.created;
   }

   @JsonProperty("error")
   @Nullable
   public final String getError() {
      return this.error;
   }

   @JsonProperty("field")
   @Nullable
   public final String getField() {
      return this.field;
   }

   @JsonProperty("functionKey")
   @Nullable
   public final String getFunctionKey() {
      return this.functionKey;
   }

   @JsonProperty("functionName")
   @Nullable
   public final String getFunctionName() {
      return this.functionName;
   }

   @JsonProperty("id")
   @Nullable
   public final String getId() {
      return this.id;
   }

   @JsonProperty("operator")
   @Nullable
   public final String getOperator() {
      return this.operator;
   }

   @JsonProperty("updated")
   @Nullable
   public final OffsetDateTime getUpdated() {
      return this.updated;
   }

   @JsonProperty("used")
   @Nullable
   public final OffsetDateTime getUsed() {
      return this.used;
   }

   @JsonProperty("value")
   @Nullable
   public final String getValue() {
      return this.value;
   }

   @Nullable
   public final List<String> component1() {
      return this.arguments;
   }

   @Nullable
   public final OffsetDateTime component2() {
      return this.created;
   }

   @Nullable
   public final String component3() {
      return this.error;
   }

   @Nullable
   public final String component4() {
      return this.field;
   }

   @Nullable
   public final String component5() {
      return this.functionKey;
   }

   @Nullable
   public final String component6() {
      return this.functionName;
   }

   @Nullable
   public final String component7() {
      return this.id;
   }

   @Nullable
   public final String component8() {
      return this.operator;
   }

   @Nullable
   public final OffsetDateTime component9() {
      return this.updated;
   }

   @Nullable
   public final OffsetDateTime component10() {
      return this.used;
   }

   @Nullable
   public final String component11() {
      return this.value;
   }

   @NotNull
   public final JqlFunctionPrecomputationBean copy(@Nullable List<String> arguments, @Nullable OffsetDateTime created, @Nullable String error, @Nullable String field, @Nullable String functionKey, @Nullable String functionName, @Nullable String id, @Nullable String operator, @Nullable OffsetDateTime updated, @Nullable OffsetDateTime used, @Nullable String value) {
      return new JqlFunctionPrecomputationBean(arguments, created, error, field, functionKey, functionName, id, operator, updated, used, value);
   }

   @NotNull
   public String toString() {
      return "JqlFunctionPrecomputationBean(arguments=" + this.arguments + ", created=" + this.created + ", error=" + this.error + ", field=" + this.field + ", functionKey=" + this.functionKey + ", functionName=" + this.functionName + ", id=" + this.id + ", operator=" + this.operator + ", updated=" + this.updated + ", used=" + this.used + ", value=" + this.value + ")";
   }

   public int hashCode() {
      int var1 = this.arguments == null ? 0 : this.arguments.hashCode();
      var1 = var1 * 31 + (this.created == null ? 0 : this.created.hashCode());
      var1 = var1 * 31 + (this.error == null ? 0 : this.error.hashCode());
      var1 = var1 * 31 + (this.field == null ? 0 : this.field.hashCode());
      var1 = var1 * 31 + (this.functionKey == null ? 0 : this.functionKey.hashCode());
      var1 = var1 * 31 + (this.functionName == null ? 0 : this.functionName.hashCode());
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      var1 = var1 * 31 + (this.operator == null ? 0 : this.operator.hashCode());
      var1 = var1 * 31 + (this.updated == null ? 0 : this.updated.hashCode());
      var1 = var1 * 31 + (this.used == null ? 0 : this.used.hashCode());
      var1 = var1 * 31 + (this.value == null ? 0 : this.value.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JqlFunctionPrecomputationBean)) {
         return false;
      } else {
         JqlFunctionPrecomputationBean var2 = (JqlFunctionPrecomputationBean)other;
         if (!Intrinsics.areEqual(this.arguments, var2.arguments)) {
            return false;
         } else if (!Intrinsics.areEqual(this.created, var2.created)) {
            return false;
         } else if (!Intrinsics.areEqual(this.error, var2.error)) {
            return false;
         } else if (!Intrinsics.areEqual(this.field, var2.field)) {
            return false;
         } else if (!Intrinsics.areEqual(this.functionKey, var2.functionKey)) {
            return false;
         } else if (!Intrinsics.areEqual(this.functionName, var2.functionName)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.operator, var2.operator)) {
            return false;
         } else if (!Intrinsics.areEqual(this.updated, var2.updated)) {
            return false;
         } else if (!Intrinsics.areEqual(this.used, var2.used)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.value, var2.value);
         }
      }
   }

   public JqlFunctionPrecomputationBean() {
      this((List)null, (OffsetDateTime)null, (String)null, (String)null, (String)null, (String)null, (String)null, (String)null, (OffsetDateTime)null, (OffsetDateTime)null, (String)null, 2047, (DefaultConstructorMarker)null);
   }
}
