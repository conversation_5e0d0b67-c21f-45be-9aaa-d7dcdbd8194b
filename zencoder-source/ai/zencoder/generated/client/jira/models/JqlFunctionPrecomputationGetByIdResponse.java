package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0011\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0003H\u00c6\u0003J-\u0010\u000e\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0004H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\n\u00a8\u0006\u0015"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JqlFunctionPrecomputationGetByIdResponse;", "", "notFoundPrecomputationIDs", "", "", "precomputations", "Lai/zencoder/generated/client/jira/models/JqlFunctionPrecomputationBean;", "<init>", "(Ljava/util/List;Ljava/util/List;)V", "getNotFoundPrecomputationIDs", "()Ljava/util/List;", "getPrecomputations", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class JqlFunctionPrecomputationGetByIdResponse {
   @Nullable
   private final List<String> notFoundPrecomputationIDs;
   @Nullable
   private final List<JqlFunctionPrecomputationBean> precomputations;

   public JqlFunctionPrecomputationGetByIdResponse(@Nullable List<String> notFoundPrecomputationIDs, @Nullable List<JqlFunctionPrecomputationBean> precomputations) {
      this.notFoundPrecomputationIDs = notFoundPrecomputationIDs;
      this.precomputations = precomputations;
   }

   @JsonProperty("notFoundPrecomputationIDs")
   @Nullable
   public final List<String> getNotFoundPrecomputationIDs() {
      return this.notFoundPrecomputationIDs;
   }

   @JsonProperty("precomputations")
   @Nullable
   public final List<JqlFunctionPrecomputationBean> getPrecomputations() {
      return this.precomputations;
   }

   @Nullable
   public final List<String> component1() {
      return this.notFoundPrecomputationIDs;
   }

   @Nullable
   public final List<JqlFunctionPrecomputationBean> component2() {
      return this.precomputations;
   }

   @NotNull
   public final JqlFunctionPrecomputationGetByIdResponse copy(@Nullable List<String> notFoundPrecomputationIDs, @Nullable List<JqlFunctionPrecomputationBean> precomputations) {
      return new JqlFunctionPrecomputationGetByIdResponse(notFoundPrecomputationIDs, precomputations);
   }

   @NotNull
   public String toString() {
      return "JqlFunctionPrecomputationGetByIdResponse(notFoundPrecomputationIDs=" + this.notFoundPrecomputationIDs + ", precomputations=" + this.precomputations + ")";
   }

   public int hashCode() {
      int var1 = this.notFoundPrecomputationIDs == null ? 0 : this.notFoundPrecomputationIDs.hashCode();
      var1 = var1 * 31 + (this.precomputations == null ? 0 : this.precomputations.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JqlFunctionPrecomputationGetByIdResponse)) {
         return false;
      } else {
         JqlFunctionPrecomputationGetByIdResponse var2 = (JqlFunctionPrecomputationGetByIdResponse)other;
         if (!Intrinsics.areEqual(this.notFoundPrecomputationIDs, var2.notFoundPrecomputationIDs)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.precomputations, var2.precomputations);
         }
      }
   }

   public JqlFunctionPrecomputationGetByIdResponse() {
      this((List)null, (List)null, 3, (DefaultConstructorMarker)null);
   }
}
