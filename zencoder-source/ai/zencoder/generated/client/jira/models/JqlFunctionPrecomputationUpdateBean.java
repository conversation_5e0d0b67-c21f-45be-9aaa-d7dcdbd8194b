package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\r\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J+\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\t\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JqlFunctionPrecomputationUpdateBean;", "", "id", "", "error", "value", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getId", "()Ljava/lang/String;", "getError", "getValue", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class JqlFunctionPrecomputationUpdateBean {
   @NotNull
   private final String id;
   @Nullable
   private final String error;
   @Nullable
   private final String value;

   public JqlFunctionPrecomputationUpdateBean(@NotNull String id, @Nullable String error, @Nullable String value) {
      Intrinsics.checkNotNullParameter(id, "");
      super();
      this.id = id;
      this.error = error;
      this.value = value;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("error")
   @Nullable
   public final String getError() {
      return this.error;
   }

   @JsonProperty("value")
   @Nullable
   public final String getValue() {
      return this.value;
   }

   @NotNull
   public final String component1() {
      return this.id;
   }

   @Nullable
   public final String component2() {
      return this.error;
   }

   @Nullable
   public final String component3() {
      return this.value;
   }

   @NotNull
   public final JqlFunctionPrecomputationUpdateBean copy(@NotNull String id, @Nullable String error, @Nullable String value) {
      Intrinsics.checkNotNullParameter(id, "");
      return new JqlFunctionPrecomputationUpdateBean(id, error, value);
   }

   @NotNull
   public String toString() {
      return "JqlFunctionPrecomputationUpdateBean(id=" + this.id + ", error=" + this.error + ", value=" + this.value + ")";
   }

   public int hashCode() {
      int var1 = this.id.hashCode();
      var1 = var1 * 31 + (this.error == null ? 0 : this.error.hashCode());
      var1 = var1 * 31 + (this.value == null ? 0 : this.value.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JqlFunctionPrecomputationUpdateBean)) {
         return false;
      } else {
         JqlFunctionPrecomputationUpdateBean var2 = (JqlFunctionPrecomputationUpdateBean)other;
         if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.error, var2.error)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.value, var2.value);
         }
      }
   }
}
