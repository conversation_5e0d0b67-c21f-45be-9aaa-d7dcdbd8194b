package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\"\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0011\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0006H\u00c6\u0003J-\u0010\u000f\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0004H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JqlFunctionPrecomputationUpdateErrorResponse;", "", "errorMessages", "", "", "notFoundPrecomputationIDs", "", "<init>", "(Ljava/util/List;Ljava/util/Set;)V", "getErrorMessages", "()Ljava/util/List;", "getNotFoundPrecomputationIDs", "()Ljava/util/Set;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class JqlFunctionPrecomputationUpdateErrorResponse {
   @Nullable
   private final List<String> errorMessages;
   @Nullable
   private final Set<String> notFoundPrecomputationIDs;

   public JqlFunctionPrecomputationUpdateErrorResponse(@Nullable List<String> errorMessages, @Nullable Set<String> notFoundPrecomputationIDs) {
      this.errorMessages = errorMessages;
      this.notFoundPrecomputationIDs = notFoundPrecomputationIDs;
   }

   @JsonProperty("errorMessages")
   @Nullable
   public final List<String> getErrorMessages() {
      return this.errorMessages;
   }

   @JsonProperty("notFoundPrecomputationIDs")
   @Nullable
   public final Set<String> getNotFoundPrecomputationIDs() {
      return this.notFoundPrecomputationIDs;
   }

   @Nullable
   public final List<String> component1() {
      return this.errorMessages;
   }

   @Nullable
   public final Set<String> component2() {
      return this.notFoundPrecomputationIDs;
   }

   @NotNull
   public final JqlFunctionPrecomputationUpdateErrorResponse copy(@Nullable List<String> errorMessages, @Nullable Set<String> notFoundPrecomputationIDs) {
      return new JqlFunctionPrecomputationUpdateErrorResponse(errorMessages, notFoundPrecomputationIDs);
   }

   @NotNull
   public String toString() {
      return "JqlFunctionPrecomputationUpdateErrorResponse(errorMessages=" + this.errorMessages + ", notFoundPrecomputationIDs=" + this.notFoundPrecomputationIDs + ")";
   }

   public int hashCode() {
      int var1 = this.errorMessages == null ? 0 : this.errorMessages.hashCode();
      var1 = var1 * 31 + (this.notFoundPrecomputationIDs == null ? 0 : this.notFoundPrecomputationIDs.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JqlFunctionPrecomputationUpdateErrorResponse)) {
         return false;
      } else {
         JqlFunctionPrecomputationUpdateErrorResponse var2 = (JqlFunctionPrecomputationUpdateErrorResponse)other;
         if (!Intrinsics.areEqual(this.errorMessages, var2.errorMessages)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.notFoundPrecomputationIDs, var2.notFoundPrecomputationIDs);
         }
      }
   }

   public JqlFunctionPrecomputationUpdateErrorResponse() {
      this((List)null, (Set)null, 3, (DefaultConstructorMarker)null);
   }
}
