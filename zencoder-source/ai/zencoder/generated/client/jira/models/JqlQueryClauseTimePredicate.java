package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u0016B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0017"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JqlQueryClauseTimePredicate;", "", "operand", "Lai/zencoder/generated/client/jira/models/JqlQueryClauseOperand;", "operator", "Lai/zencoder/generated/client/jira/models/JqlQueryClauseTimePredicate$Operator;", "<init>", "(Lai/zencoder/generated/client/jira/models/JqlQueryClauseOperand;Lai/zencoder/generated/client/jira/models/JqlQueryClauseTimePredicate$Operator;)V", "getOperand", "()Lai/zencoder/generated/client/jira/models/JqlQueryClauseOperand;", "getOperator", "()Lai/zencoder/generated/client/jira/models/JqlQueryClauseTimePredicate$Operator;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "Operator", "zencoder"}
)
public final class JqlQueryClauseTimePredicate {
   @NotNull
   private final JqlQueryClauseOperand operand;
   @NotNull
   private final Operator operator;

   public JqlQueryClauseTimePredicate(@NotNull JqlQueryClauseOperand operand, @NotNull Operator operator) {
      Intrinsics.checkNotNullParameter(operand, "");
      Intrinsics.checkNotNullParameter(operator, "");
      super();
      this.operand = operand;
      this.operator = operator;
   }

   @JsonProperty("operand")
   @NotNull
   public final JqlQueryClauseOperand getOperand() {
      return this.operand;
   }

   @JsonProperty("operator")
   @NotNull
   public final Operator getOperator() {
      return this.operator;
   }

   @NotNull
   public final JqlQueryClauseOperand component1() {
      return this.operand;
   }

   @NotNull
   public final Operator component2() {
      return this.operator;
   }

   @NotNull
   public final JqlQueryClauseTimePredicate copy(@NotNull JqlQueryClauseOperand operand, @NotNull Operator operator) {
      Intrinsics.checkNotNullParameter(operand, "");
      Intrinsics.checkNotNullParameter(operator, "");
      return new JqlQueryClauseTimePredicate(operand, operator);
   }

   @NotNull
   public String toString() {
      return "JqlQueryClauseTimePredicate(operand=" + this.operand + ", operator=" + this.operator + ")";
   }

   public int hashCode() {
      int var1 = this.operand.hashCode();
      var1 = var1 * 31 + this.operator.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JqlQueryClauseTimePredicate)) {
         return false;
      } else {
         JqlQueryClauseTimePredicate var2 = (JqlQueryClauseTimePredicate)other;
         if (!Intrinsics.areEqual(this.operand, var2.operand)) {
            return false;
         } else {
            return this.operator == var2.operator;
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000e\u00a8\u0006\u000f"},
      d2 = {"Lai/zencoder/generated/client/jira/models/JqlQueryClauseTimePredicate$Operator;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "before", "after", "from", "to", "on", "during", "by", "zencoder"}
   )
   public static enum Operator {
      @NotNull
      private final String value;
      @JsonProperty("before")
      before("before"),
      @JsonProperty("after")
      after("after"),
      @JsonProperty("from")
      from("from"),
      @JsonProperty("to")
      to("to"),
      @JsonProperty("on")
      on("on"),
      @JsonProperty("during")
      during("during"),
      @JsonProperty("by")
      by("by");

      private Operator(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Operator> getEntries() {
         return $ENTRIES;
      }
   }
}
