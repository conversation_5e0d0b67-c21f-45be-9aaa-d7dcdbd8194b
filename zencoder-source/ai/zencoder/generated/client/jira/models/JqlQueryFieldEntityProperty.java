package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u001bB+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J3\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0013\u0010\u0005\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000bR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JqlQueryFieldEntityProperty;", "", "entity", "", "key", "path", "type", "Lai/zencoder/generated/client/jira/models/JqlQueryFieldEntityProperty$Type;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/JqlQueryFieldEntityProperty$Type;)V", "getEntity", "()Ljava/lang/String;", "getKey", "getPath", "getType", "()Lai/zencoder/generated/client/jira/models/JqlQueryFieldEntityProperty$Type;", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "Type", "zencoder"}
)
public final class JqlQueryFieldEntityProperty {
   @NotNull
   private final String entity;
   @NotNull
   private final String key;
   @NotNull
   private final String path;
   @Nullable
   private final Type type;

   public JqlQueryFieldEntityProperty(@NotNull String entity, @NotNull String key, @NotNull String path, @Nullable Type type) {
      Intrinsics.checkNotNullParameter(entity, "");
      Intrinsics.checkNotNullParameter(key, "");
      Intrinsics.checkNotNullParameter(path, "");
      super();
      this.entity = entity;
      this.key = key;
      this.path = path;
      this.type = type;
   }

   @JsonProperty("entity")
   @NotNull
   public final String getEntity() {
      return this.entity;
   }

   @JsonProperty("key")
   @NotNull
   public final String getKey() {
      return this.key;
   }

   @JsonProperty("path")
   @NotNull
   public final String getPath() {
      return this.path;
   }

   @JsonProperty("type")
   @Nullable
   public final Type getType() {
      return this.type;
   }

   @NotNull
   public final String component1() {
      return this.entity;
   }

   @NotNull
   public final String component2() {
      return this.key;
   }

   @NotNull
   public final String component3() {
      return this.path;
   }

   @Nullable
   public final Type component4() {
      return this.type;
   }

   @NotNull
   public final JqlQueryFieldEntityProperty copy(@NotNull String entity, @NotNull String key, @NotNull String path, @Nullable Type type) {
      Intrinsics.checkNotNullParameter(entity, "");
      Intrinsics.checkNotNullParameter(key, "");
      Intrinsics.checkNotNullParameter(path, "");
      return new JqlQueryFieldEntityProperty(entity, key, path, type);
   }

   @NotNull
   public String toString() {
      return "JqlQueryFieldEntityProperty(entity=" + this.entity + ", key=" + this.key + ", path=" + this.path + ", type=" + this.type + ")";
   }

   public int hashCode() {
      int var1 = this.entity.hashCode();
      var1 = var1 * 31 + this.key.hashCode();
      var1 = var1 * 31 + this.path.hashCode();
      var1 = var1 * 31 + (this.type == null ? 0 : this.type.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JqlQueryFieldEntityProperty)) {
         return false;
      } else {
         JqlQueryFieldEntityProperty var2 = (JqlQueryFieldEntityProperty)other;
         if (!Intrinsics.areEqual(this.entity, var2.entity)) {
            return false;
         } else if (!Intrinsics.areEqual(this.key, var2.key)) {
            return false;
         } else if (!Intrinsics.areEqual(this.path, var2.path)) {
            return false;
         } else {
            return this.type == var2.type;
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\f\u00a8\u0006\r"},
      d2 = {"Lai/zencoder/generated/client/jira/models/JqlQueryFieldEntityProperty$Type;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "number", "string", "text", "date", "user", "zencoder"}
   )
   public static enum Type {
      @NotNull
      private final String value;
      @JsonProperty("number")
      number("number"),
      @JsonProperty("string")
      string("string"),
      @JsonProperty("text")
      text("text"),
      @JsonProperty("date")
      date("date"),
      @JsonProperty("user")
      user("user");

      private Type(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Type> getEntries() {
         return $ENTRIES;
      }
   }
}
