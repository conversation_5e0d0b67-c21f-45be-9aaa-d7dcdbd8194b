package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u0016B\u001b\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\r\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u001f\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0017"},
   d2 = {"Lai/zencoder/generated/client/jira/models/JqlQueryOrderByClauseElement;", "", "field", "Lai/zencoder/generated/client/jira/models/JqlQueryField;", "direction", "Lai/zencoder/generated/client/jira/models/JqlQueryOrderByClauseElement$Direction;", "<init>", "(Lai/zencoder/generated/client/jira/models/JqlQueryField;Lai/zencoder/generated/client/jira/models/JqlQueryOrderByClauseElement$Direction;)V", "getField", "()Lai/zencoder/generated/client/jira/models/JqlQueryField;", "getDirection", "()Lai/zencoder/generated/client/jira/models/JqlQueryOrderByClauseElement$Direction;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "Direction", "zencoder"}
)
public final class JqlQueryOrderByClauseElement {
   @NotNull
   private final JqlQueryField field;
   @Nullable
   private final Direction direction;

   public JqlQueryOrderByClauseElement(@NotNull JqlQueryField field, @Nullable Direction direction) {
      Intrinsics.checkNotNullParameter(field, "");
      super();
      this.field = field;
      this.direction = direction;
   }

   @JsonProperty("field")
   @NotNull
   public final JqlQueryField getField() {
      return this.field;
   }

   @JsonProperty("direction")
   @Nullable
   public final Direction getDirection() {
      return this.direction;
   }

   @NotNull
   public final JqlQueryField component1() {
      return this.field;
   }

   @Nullable
   public final Direction component2() {
      return this.direction;
   }

   @NotNull
   public final JqlQueryOrderByClauseElement copy(@NotNull JqlQueryField field, @Nullable Direction direction) {
      Intrinsics.checkNotNullParameter(field, "");
      return new JqlQueryOrderByClauseElement(field, direction);
   }

   @NotNull
   public String toString() {
      return "JqlQueryOrderByClauseElement(field=" + this.field + ", direction=" + this.direction + ")";
   }

   public int hashCode() {
      int var1 = this.field.hashCode();
      var1 = var1 * 31 + (this.direction == null ? 0 : this.direction.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof JqlQueryOrderByClauseElement)) {
         return false;
      } else {
         JqlQueryOrderByClauseElement var2 = (JqlQueryOrderByClauseElement)other;
         if (!Intrinsics.areEqual(this.field, var2.field)) {
            return false;
         } else {
            return this.direction == var2.direction;
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/JqlQueryOrderByClauseElement$Direction;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "asc", "desc", "zencoder"}
   )
   public static enum Direction {
      @NotNull
      private final String value;
      @JsonProperty("asc")
      asc("asc"),
      @JsonProperty("desc")
      desc("desc");

      private Direction(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Direction> getEntries() {
         return $ENTRIES;
      }
   }
}
