package ai.zencoder.generated.client.jira.models;

import java.util.ArrayList;
import kotlin.Metadata;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u0012\u0012\u0004\u0012\u00020\u00020\u0001j\b\u0012\u0004\u0012\u00020\u0002`\u0003B\u0007\u00a2\u0006\u0004\b\u0004\u0010\u0005\u00a8\u0006\u0006"},
   d2 = {"Lai/zencoder/generated/client/jira/models/LegacyJackson1ListAttachment;", "Ljava/util/ArrayList;", "Lai/zencoder/generated/client/jira/models/Attachment;", "Lkotlin/collections/ArrayList;", "<init>", "()V", "zencoder"}
)
public final class LegacyJackson1ListAttachment extends ArrayList<Attachment> {
   public int a() {
      return super.size();
   }

   public final int size() {
      return this.a();
   }

   public boolean a(Attachment var1) {
      return super.contains(var1);
   }

   public final boolean contains(Object element) {
      return !(element instanceof Attachment) ? false : this.a((Attachment)element);
   }

   public int a(Attachment var1) {
      return super.indexOf(var1);
   }

   public final int indexOf(Object element) {
      return !(element instanceof Attachment) ? -1 : this.a((Attachment)element);
   }

   public int b(Attachment var1) {
      return super.lastIndexOf(var1);
   }

   public final int lastIndexOf(Object element) {
      return !(element instanceof Attachment) ? -1 : this.b((Attachment)element);
   }

   public boolean b(Attachment var1) {
      return super.remove(var1);
   }

   public final boolean remove(Object element) {
      return !(element instanceof Attachment) ? false : this.b((Attachment)element);
   }

   public Attachment a(int var1) {
      return (Attachment)super.remove(var1);
   }

   public final Attachment b(int var1) {
      return this.a(var1);
   }
}
