package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\u0004\b\t\u0010\nJ\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\bH\u00c6\u0003J3\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\bH\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR\u0013\u0010\u0005\u001a\u00020\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u0006\u001e"},
   d2 = {"Lai/zencoder/generated/client/jira/models/LinkIssueRequestJsonBean;", "", "inwardIssue", "Lai/zencoder/generated/client/jira/models/LinkedIssue;", "outwardIssue", "type", "Lai/zencoder/generated/client/jira/models/IssueLinkType;", "comment", "Lai/zencoder/generated/client/jira/models/Comment;", "<init>", "(Lai/zencoder/generated/client/jira/models/LinkedIssue;Lai/zencoder/generated/client/jira/models/LinkedIssue;Lai/zencoder/generated/client/jira/models/IssueLinkType;Lai/zencoder/generated/client/jira/models/Comment;)V", "getInwardIssue", "()Lai/zencoder/generated/client/jira/models/LinkedIssue;", "getOutwardIssue", "getType", "()Lai/zencoder/generated/client/jira/models/IssueLinkType;", "getComment", "()Lai/zencoder/generated/client/jira/models/Comment;", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class LinkIssueRequestJsonBean {
   @NotNull
   private final LinkedIssue inwardIssue;
   @NotNull
   private final LinkedIssue outwardIssue;
   @NotNull
   private final IssueLinkType type;
   @Nullable
   private final Comment comment;

   public LinkIssueRequestJsonBean(@NotNull LinkedIssue inwardIssue, @NotNull LinkedIssue outwardIssue, @NotNull IssueLinkType type, @Nullable Comment comment) {
      Intrinsics.checkNotNullParameter(inwardIssue, "");
      Intrinsics.checkNotNullParameter(outwardIssue, "");
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.inwardIssue = inwardIssue;
      this.outwardIssue = outwardIssue;
      this.type = type;
      this.comment = comment;
   }

   @JsonProperty("inwardIssue")
   @NotNull
   public final LinkedIssue getInwardIssue() {
      return this.inwardIssue;
   }

   @JsonProperty("outwardIssue")
   @NotNull
   public final LinkedIssue getOutwardIssue() {
      return this.outwardIssue;
   }

   @JsonProperty("type")
   @NotNull
   public final IssueLinkType getType() {
      return this.type;
   }

   @JsonProperty("comment")
   @Nullable
   public final Comment getComment() {
      return this.comment;
   }

   @NotNull
   public final LinkedIssue component1() {
      return this.inwardIssue;
   }

   @NotNull
   public final LinkedIssue component2() {
      return this.outwardIssue;
   }

   @NotNull
   public final IssueLinkType component3() {
      return this.type;
   }

   @Nullable
   public final Comment component4() {
      return this.comment;
   }

   @NotNull
   public final LinkIssueRequestJsonBean copy(@NotNull LinkedIssue inwardIssue, @NotNull LinkedIssue outwardIssue, @NotNull IssueLinkType type, @Nullable Comment comment) {
      Intrinsics.checkNotNullParameter(inwardIssue, "");
      Intrinsics.checkNotNullParameter(outwardIssue, "");
      Intrinsics.checkNotNullParameter(type, "");
      return new LinkIssueRequestJsonBean(inwardIssue, outwardIssue, type, comment);
   }

   @NotNull
   public String toString() {
      return "LinkIssueRequestJsonBean(inwardIssue=" + this.inwardIssue + ", outwardIssue=" + this.outwardIssue + ", type=" + this.type + ", comment=" + this.comment + ")";
   }

   public int hashCode() {
      int var1 = this.inwardIssue.hashCode();
      var1 = var1 * 31 + this.outwardIssue.hashCode();
      var1 = var1 * 31 + this.type.hashCode();
      var1 = var1 * 31 + (this.comment == null ? 0 : this.comment.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof LinkIssueRequestJsonBean)) {
         return false;
      } else {
         LinkIssueRequestJsonBean var2 = (LinkIssueRequestJsonBean)other;
         if (!Intrinsics.areEqual(this.inwardIssue, var2.inwardIssue)) {
            return false;
         } else if (!Intrinsics.areEqual(this.outwardIssue, var2.outwardIssue)) {
            return false;
         } else if (!Intrinsics.areEqual(this.type, var2.type)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.comment, var2.comment);
         }
      }
   }
}
