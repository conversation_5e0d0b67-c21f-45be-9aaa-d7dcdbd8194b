package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0011\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u001bB%\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0001\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0001H\u00c6\u0003J\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000eJ.\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0014J\u0013\u0010\u0015\u001a\u00020\u00062\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u00020\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0017\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\n\n\u0002\u0010\u000f\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/jira/models/MandatoryFieldValueForADF;", "", "type", "Lai/zencoder/generated/client/jira/models/MandatoryFieldValueForADF$Type;", "value", "retain", "", "<init>", "(Lai/zencoder/generated/client/jira/models/MandatoryFieldValueForADF$Type;Ljava/lang/Object;Ljava/lang/Boolean;)V", "getType", "()Lai/zencoder/generated/client/jira/models/MandatoryFieldValueForADF$Type;", "getValue", "()Ljava/lang/Object;", "getRetain", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "component1", "component2", "component3", "copy", "(Lai/zencoder/generated/client/jira/models/MandatoryFieldValueForADF$Type;Ljava/lang/Object;Ljava/lang/Boolean;)Lai/zencoder/generated/client/jira/models/MandatoryFieldValueForADF;", "equals", "other", "hashCode", "", "toString", "", "Type", "zencoder"}
)
public final class MandatoryFieldValueForADF {
   @NotNull
   private final Type type;
   @NotNull
   private final Object value;
   @Nullable
   private final Boolean retain;

   public MandatoryFieldValueForADF(@NotNull Type type, @NotNull Object value, @Nullable Boolean retain) {
      Intrinsics.checkNotNullParameter(type, "");
      Intrinsics.checkNotNullParameter(value, "");
      super();
      this.type = type;
      this.value = value;
      this.retain = retain;
   }

   @JsonProperty("type")
   @NotNull
   public final Type getType() {
      return this.type;
   }

   @JsonProperty("value")
   @NotNull
   public final Object getValue() {
      return this.value;
   }

   @JsonProperty("retain")
   @Nullable
   public final Boolean getRetain() {
      return this.retain;
   }

   @NotNull
   public final Type component1() {
      return this.type;
   }

   @NotNull
   public final Object component2() {
      return this.value;
   }

   @Nullable
   public final Boolean component3() {
      return this.retain;
   }

   @NotNull
   public final MandatoryFieldValueForADF copy(@NotNull Type type, @NotNull Object value, @Nullable Boolean retain) {
      Intrinsics.checkNotNullParameter(type, "");
      Intrinsics.checkNotNullParameter(value, "");
      return new MandatoryFieldValueForADF(type, value, retain);
   }

   @NotNull
   public String toString() {
      return "MandatoryFieldValueForADF(type=" + this.type + ", value=" + this.value + ", retain=" + this.retain + ")";
   }

   public int hashCode() {
      int var1 = this.type.hashCode();
      var1 = var1 * 31 + this.value.hashCode();
      var1 = var1 * 31 + (this.retain == null ? 0 : this.retain.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof MandatoryFieldValueForADF)) {
         return false;
      } else {
         MandatoryFieldValueForADF var2 = (MandatoryFieldValueForADF)other;
         if (this.type != var2.type) {
            return false;
         } else if (!Intrinsics.areEqual(this.value, var2.value)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.retain, var2.retain);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/MandatoryFieldValueForADF$Type;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "adf", "raw", "zencoder"}
   )
   public static enum Type {
      @NotNull
      private final String value;
      @JsonProperty("adf")
      adf("adf"),
      @JsonProperty("raw")
      raw("raw");

      private Type(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Type> getEntries() {
         return $ENTRIES;
      }
   }
}
