package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J#\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/MappingsByIssueTypeOverride;", "", "issueTypeId", "", "statusMappings", "", "Lai/zencoder/generated/client/jira/models/WorkflowAssociationStatusMapping;", "<init>", "(Ljava/lang/String;Ljava/util/List;)V", "getIssueTypeId", "()Ljava/lang/String;", "getStatusMappings", "()Ljava/util/List;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class MappingsByIssueTypeOverride {
   @NotNull
   private final String issueTypeId;
   @NotNull
   private final List<WorkflowAssociationStatusMapping> statusMappings;

   public MappingsByIssueTypeOverride(@NotNull String issueTypeId, @NotNull List<WorkflowAssociationStatusMapping> statusMappings) {
      Intrinsics.checkNotNullParameter(issueTypeId, "");
      Intrinsics.checkNotNullParameter(statusMappings, "");
      super();
      this.issueTypeId = issueTypeId;
      this.statusMappings = statusMappings;
   }

   @JsonProperty("issueTypeId")
   @NotNull
   public final String getIssueTypeId() {
      return this.issueTypeId;
   }

   @JsonProperty("statusMappings")
   @NotNull
   public final List<WorkflowAssociationStatusMapping> getStatusMappings() {
      return this.statusMappings;
   }

   @NotNull
   public final String component1() {
      return this.issueTypeId;
   }

   @NotNull
   public final List<WorkflowAssociationStatusMapping> component2() {
      return this.statusMappings;
   }

   @NotNull
   public final MappingsByIssueTypeOverride copy(@NotNull String issueTypeId, @NotNull List<WorkflowAssociationStatusMapping> statusMappings) {
      Intrinsics.checkNotNullParameter(issueTypeId, "");
      Intrinsics.checkNotNullParameter(statusMappings, "");
      return new MappingsByIssueTypeOverride(issueTypeId, statusMappings);
   }

   @NotNull
   public String toString() {
      return "MappingsByIssueTypeOverride(issueTypeId=" + this.issueTypeId + ", statusMappings=" + this.statusMappings + ")";
   }

   public int hashCode() {
      int var1 = this.issueTypeId.hashCode();
      var1 = var1 * 31 + this.statusMappings.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof MappingsByIssueTypeOverride)) {
         return false;
      } else {
         MappingsByIssueTypeOverride var2 = (MappingsByIssueTypeOverride)other;
         if (!Intrinsics.areEqual(this.issueTypeId, var2.issueTypeId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.statusMappings, var2.statusMappings);
         }
      }
   }
}
