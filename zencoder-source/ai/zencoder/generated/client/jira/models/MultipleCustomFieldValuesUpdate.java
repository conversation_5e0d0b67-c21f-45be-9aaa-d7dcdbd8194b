package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0010\t\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0001\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J/\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0001H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001a"},
   d2 = {"Lai/zencoder/generated/client/jira/models/MultipleCustomFieldValuesUpdate;", "", "customField", "", "issueIds", "", "", "value", "<init>", "(Ljava/lang/String;Ljava/util/List;Ljava/lang/Object;)V", "getCustomField", "()Ljava/lang/String;", "getIssueIds", "()Ljava/util/List;", "getValue", "()Ljava/lang/Object;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class MultipleCustomFieldValuesUpdate {
   @NotNull
   private final String customField;
   @NotNull
   private final List<Long> issueIds;
   @Nullable
   private final Object value;

   public MultipleCustomFieldValuesUpdate(@NotNull String customField, @NotNull List<Long> issueIds, @Nullable Object value) {
      Intrinsics.checkNotNullParameter(customField, "");
      Intrinsics.checkNotNullParameter(issueIds, "");
      super();
      this.customField = customField;
      this.issueIds = issueIds;
      this.value = value;
   }

   @JsonProperty("customField")
   @NotNull
   public final String getCustomField() {
      return this.customField;
   }

   @JsonProperty("issueIds")
   @NotNull
   public final List<Long> getIssueIds() {
      return this.issueIds;
   }

   @JsonProperty("value")
   @Nullable
   public final Object getValue() {
      return this.value;
   }

   @NotNull
   public final String component1() {
      return this.customField;
   }

   @NotNull
   public final List<Long> component2() {
      return this.issueIds;
   }

   @Nullable
   public final Object component3() {
      return this.value;
   }

   @NotNull
   public final MultipleCustomFieldValuesUpdate copy(@NotNull String customField, @NotNull List<Long> issueIds, @Nullable Object value) {
      Intrinsics.checkNotNullParameter(customField, "");
      Intrinsics.checkNotNullParameter(issueIds, "");
      return new MultipleCustomFieldValuesUpdate(customField, issueIds, value);
   }

   @NotNull
   public String toString() {
      return "MultipleCustomFieldValuesUpdate(customField=" + this.customField + ", issueIds=" + this.issueIds + ", value=" + this.value + ")";
   }

   public int hashCode() {
      int var1 = this.customField.hashCode();
      var1 = var1 * 31 + this.issueIds.hashCode();
      var1 = var1 * 31 + (this.value == null ? 0 : this.value.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof MultipleCustomFieldValuesUpdate)) {
         return false;
      } else {
         MultipleCustomFieldValuesUpdate var2 = (MultipleCustomFieldValuesUpdate)other;
         if (!Intrinsics.areEqual(this.customField, var2.customField)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueIds, var2.issueIds)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.value, var2.value);
         }
      }
   }
}
