package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003\u00a2\u0006\u0004\b\t\u0010\nJ\u0011\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003H\u00c6\u0003J?\u0010\u0012\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00032\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0004H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR\u001b\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\f\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/NotificationRecipientsRestrictions;", "", "groupIds", "", "", "groups", "Lai/zencoder/generated/client/jira/models/GroupName;", "permissions", "Lai/zencoder/generated/client/jira/models/RestrictedPermission;", "<init>", "(Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getGroupIds", "()Ljava/util/List;", "getGroups", "getPermissions", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class NotificationRecipientsRestrictions {
   @Nullable
   private final List<String> groupIds;
   @Nullable
   private final List<GroupName> groups;
   @Nullable
   private final List<RestrictedPermission> permissions;

   public NotificationRecipientsRestrictions(@Nullable List<String> groupIds, @Nullable List<GroupName> groups, @Nullable List<RestrictedPermission> permissions) {
      this.groupIds = groupIds;
      this.groups = groups;
      this.permissions = permissions;
   }

   @JsonProperty("groupIds")
   @Nullable
   public final List<String> getGroupIds() {
      return this.groupIds;
   }

   @JsonProperty("groups")
   @Nullable
   public final List<GroupName> getGroups() {
      return this.groups;
   }

   @JsonProperty("permissions")
   @Nullable
   public final List<RestrictedPermission> getPermissions() {
      return this.permissions;
   }

   @Nullable
   public final List<String> component1() {
      return this.groupIds;
   }

   @Nullable
   public final List<GroupName> component2() {
      return this.groups;
   }

   @Nullable
   public final List<RestrictedPermission> component3() {
      return this.permissions;
   }

   @NotNull
   public final NotificationRecipientsRestrictions copy(@Nullable List<String> groupIds, @Nullable List<GroupName> groups, @Nullable List<RestrictedPermission> permissions) {
      return new NotificationRecipientsRestrictions(groupIds, groups, permissions);
   }

   @NotNull
   public String toString() {
      return "NotificationRecipientsRestrictions(groupIds=" + this.groupIds + ", groups=" + this.groups + ", permissions=" + this.permissions + ")";
   }

   public int hashCode() {
      int var1 = this.groupIds == null ? 0 : this.groupIds.hashCode();
      var1 = var1 * 31 + (this.groups == null ? 0 : this.groups.hashCode());
      var1 = var1 * 31 + (this.permissions == null ? 0 : this.permissions.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof NotificationRecipientsRestrictions)) {
         return false;
      } else {
         NotificationRecipientsRestrictions var2 = (NotificationRecipientsRestrictions)other;
         if (!Intrinsics.areEqual(this.groupIds, var2.groupIds)) {
            return false;
         } else if (!Intrinsics.areEqual(this.groups, var2.groups)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.permissions, var2.permissions);
         }
      }
   }

   public NotificationRecipientsRestrictions() {
      this((List)null, (List)null, (List)null, 7, (DefaultConstructorMarker)null);
   }
}
