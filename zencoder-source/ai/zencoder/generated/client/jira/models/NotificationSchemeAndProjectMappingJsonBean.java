package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0005\u0010\u0006J\u000b\u0010\n\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J!\u0010\f\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\r\u001a\u00020\u000e2\b\u0010\u000f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001J\t\u0010\u0012\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\b\u00a8\u0006\u0013"},
   d2 = {"Lai/zencoder/generated/client/jira/models/NotificationSchemeAndProjectMappingJsonBean;", "", "notificationSchemeId", "", "projectId", "<init>", "(Ljava/lang/String;Ljava/lang/String;)V", "getNotificationSchemeId", "()Ljava/lang/String;", "getProjectId", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class NotificationSchemeAndProjectMappingJsonBean {
   @Nullable
   private final String notificationSchemeId;
   @Nullable
   private final String projectId;

   public NotificationSchemeAndProjectMappingJsonBean(@Nullable String notificationSchemeId, @Nullable String projectId) {
      this.notificationSchemeId = notificationSchemeId;
      this.projectId = projectId;
   }

   @JsonProperty("notificationSchemeId")
   @Nullable
   public final String getNotificationSchemeId() {
      return this.notificationSchemeId;
   }

   @JsonProperty("projectId")
   @Nullable
   public final String getProjectId() {
      return this.projectId;
   }

   @Nullable
   public final String component1() {
      return this.notificationSchemeId;
   }

   @Nullable
   public final String component2() {
      return this.projectId;
   }

   @NotNull
   public final NotificationSchemeAndProjectMappingJsonBean copy(@Nullable String notificationSchemeId, @Nullable String projectId) {
      return new NotificationSchemeAndProjectMappingJsonBean(notificationSchemeId, projectId);
   }

   @NotNull
   public String toString() {
      return "NotificationSchemeAndProjectMappingJsonBean(notificationSchemeId=" + this.notificationSchemeId + ", projectId=" + this.projectId + ")";
   }

   public int hashCode() {
      int var1 = this.notificationSchemeId == null ? 0 : this.notificationSchemeId.hashCode();
      var1 = var1 * 31 + (this.projectId == null ? 0 : this.projectId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof NotificationSchemeAndProjectMappingJsonBean)) {
         return false;
      } else {
         NotificationSchemeAndProjectMappingJsonBean var2 = (NotificationSchemeAndProjectMappingJsonBean)other;
         if (!Intrinsics.areEqual(this.notificationSchemeId, var2.notificationSchemeId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.projectId, var2.projectId);
         }
      }
   }

   public NotificationSchemeAndProjectMappingJsonBean() {
      this((String)null, (String)null, 3, (DefaultConstructorMarker)null);
   }
}
