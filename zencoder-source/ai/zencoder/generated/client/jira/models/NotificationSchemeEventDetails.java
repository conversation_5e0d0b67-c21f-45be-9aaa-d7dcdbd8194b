package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004B\u001d\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b\u00a2\u0006\u0004\b\n\u0010\u000bJ\t\u0010\u0010\u001a\u00020\u0006H\u00c6\u0003J\u000f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u00c6\u0003J#\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0005\u001a\u00020\u00062\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0002H\u00d6\u0001R\u0013\u0010\u0005\u001a\u00020\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0019\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/NotificationSchemeEventDetails;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "event", "Lai/zencoder/generated/client/jira/models/NotificationSchemeEventTypeId;", "notifications", "", "Lai/zencoder/generated/client/jira/models/NotificationSchemeNotificationDetails;", "<init>", "(Lai/zencoder/generated/client/jira/models/NotificationSchemeEventTypeId;Ljava/util/List;)V", "getEvent", "()Lai/zencoder/generated/client/jira/models/NotificationSchemeEventTypeId;", "getNotifications", "()Ljava/util/List;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class NotificationSchemeEventDetails extends HashMap<String, Object> {
   @NotNull
   private final NotificationSchemeEventTypeId event;
   @NotNull
   private final List<NotificationSchemeNotificationDetails> notifications;

   public NotificationSchemeEventDetails(@NotNull NotificationSchemeEventTypeId event, @NotNull List<NotificationSchemeNotificationDetails> notifications) {
      Intrinsics.checkNotNullParameter(event, "");
      Intrinsics.checkNotNullParameter(notifications, "");
      super();
      this.event = event;
      this.notifications = notifications;
   }

   @JsonProperty("event")
   @NotNull
   public final NotificationSchemeEventTypeId getEvent() {
      return this.event;
   }

   @JsonProperty("notifications")
   @NotNull
   public final List<NotificationSchemeNotificationDetails> getNotifications() {
      return this.notifications;
   }

   @NotNull
   public final NotificationSchemeEventTypeId component1() {
      return this.event;
   }

   @NotNull
   public final List<NotificationSchemeNotificationDetails> component2() {
      return this.notifications;
   }

   @NotNull
   public final NotificationSchemeEventDetails copy(@NotNull NotificationSchemeEventTypeId event, @NotNull List<NotificationSchemeNotificationDetails> notifications) {
      Intrinsics.checkNotNullParameter(event, "");
      Intrinsics.checkNotNullParameter(notifications, "");
      return new NotificationSchemeEventDetails(event, notifications);
   }

   @NotNull
   public String toString() {
      return "NotificationSchemeEventDetails(event=" + this.event + ", notifications=" + this.notifications + ")";
   }

   public int hashCode() {
      int var1 = this.event.hashCode();
      var1 = var1 * 31 + this.notifications.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof NotificationSchemeEventDetails)) {
         return false;
      } else {
         NotificationSchemeEventDetails var2 = (NotificationSchemeEventDetails)other;
         if (!Intrinsics.areEqual(this.event, var2.event)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.notifications, var2.notifications);
         }
      }
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
