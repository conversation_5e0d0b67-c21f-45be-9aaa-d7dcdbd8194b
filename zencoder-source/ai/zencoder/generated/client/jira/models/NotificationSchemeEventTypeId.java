package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004B\u000f\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\n\u001a\u00020\u0002H\u00c6\u0003J\u0013\u0010\u000b\u001a\u00020\u00002\b\b\u0002\u0010\u0005\u001a\u00020\u0002H\u00c6\u0001J\u0013\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0002H\u00d6\u0001R\u0013\u0010\u0005\u001a\u00020\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\t\u00a8\u0006\u0012"},
   d2 = {"Lai/zencoder/generated/client/jira/models/NotificationSchemeEventTypeId;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "id", "<init>", "(Ljava/lang/String;)V", "getId", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class NotificationSchemeEventTypeId extends HashMap<String, Object> {
   @NotNull
   private final String id;

   public NotificationSchemeEventTypeId(@NotNull String id) {
      Intrinsics.checkNotNullParameter(id, "");
      super();
      this.id = id;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @NotNull
   public final String component1() {
      return this.id;
   }

   @NotNull
   public final NotificationSchemeEventTypeId copy(@NotNull String id) {
      Intrinsics.checkNotNullParameter(id, "");
      return new NotificationSchemeEventTypeId(id);
   }

   @NotNull
   public String toString() {
      return "NotificationSchemeEventTypeId(id=" + this.id + ")";
   }

   public int hashCode() {
      return this.id.hashCode();
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof NotificationSchemeEventTypeId)) {
         return false;
      } else {
         NotificationSchemeEventTypeId var2 = (NotificationSchemeEventTypeId)other;
         return Intrinsics.areEqual(this.id, var2.id);
      }
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
