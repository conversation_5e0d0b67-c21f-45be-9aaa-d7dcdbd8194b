package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0005\u0010\u0006J\t\u0010\n\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\u001d\u0010\f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\r\u001a\u00020\u000e2\b\u0010\u000f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001J\t\u0010\u0012\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\b\u00a8\u0006\u0013"},
   d2 = {"Lai/zencoder/generated/client/jira/models/OldToNewSecurityLevelMappingsBean;", "", "newLevelId", "", "oldLevelId", "<init>", "(Ljava/lang/String;Ljava/lang/String;)V", "getNewLevelId", "()Ljava/lang/String;", "getOldLevelId", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class OldToNewSecurityLevelMappingsBean {
   @NotNull
   private final String newLevelId;
   @NotNull
   private final String oldLevelId;

   public OldToNewSecurityLevelMappingsBean(@NotNull String newLevelId, @NotNull String oldLevelId) {
      Intrinsics.checkNotNullParameter(newLevelId, "");
      Intrinsics.checkNotNullParameter(oldLevelId, "");
      super();
      this.newLevelId = newLevelId;
      this.oldLevelId = oldLevelId;
   }

   @JsonProperty("newLevelId")
   @NotNull
   public final String getNewLevelId() {
      return this.newLevelId;
   }

   @JsonProperty("oldLevelId")
   @NotNull
   public final String getOldLevelId() {
      return this.oldLevelId;
   }

   @NotNull
   public final String component1() {
      return this.newLevelId;
   }

   @NotNull
   public final String component2() {
      return this.oldLevelId;
   }

   @NotNull
   public final OldToNewSecurityLevelMappingsBean copy(@NotNull String newLevelId, @NotNull String oldLevelId) {
      Intrinsics.checkNotNullParameter(newLevelId, "");
      Intrinsics.checkNotNullParameter(oldLevelId, "");
      return new OldToNewSecurityLevelMappingsBean(newLevelId, oldLevelId);
   }

   @NotNull
   public String toString() {
      return "OldToNewSecurityLevelMappingsBean(newLevelId=" + this.newLevelId + ", oldLevelId=" + this.oldLevelId + ")";
   }

   public int hashCode() {
      int var1 = this.newLevelId.hashCode();
      var1 = var1 * 31 + this.oldLevelId.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof OldToNewSecurityLevelMappingsBean)) {
         return false;
      } else {
         OldToNewSecurityLevelMappingsBean var2 = (OldToNewSecurityLevelMappingsBean)other;
         if (!Intrinsics.areEqual(this.newLevelId, var2.newLevelId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.oldLevelId, var2.oldLevelId);
         }
      }
   }
}
