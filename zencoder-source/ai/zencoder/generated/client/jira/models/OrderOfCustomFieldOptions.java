package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u001aB-\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u000f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J1\u0010\u0013\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0004H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001b"},
   d2 = {"Lai/zencoder/generated/client/jira/models/OrderOfCustomFieldOptions;", "", "customFieldOptionIds", "", "", "after", "position", "Lai/zencoder/generated/client/jira/models/OrderOfCustomFieldOptions$Position;", "<init>", "(Ljava/util/List;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/OrderOfCustomFieldOptions$Position;)V", "getCustomFieldOptionIds", "()Ljava/util/List;", "getAfter", "()Ljava/lang/String;", "getPosition", "()Lai/zencoder/generated/client/jira/models/OrderOfCustomFieldOptions$Position;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "Position", "zencoder"}
)
public final class OrderOfCustomFieldOptions {
   @NotNull
   private final List<String> customFieldOptionIds;
   @Nullable
   private final String after;
   @Nullable
   private final Position position;

   public OrderOfCustomFieldOptions(@NotNull List<String> customFieldOptionIds, @Nullable String after, @Nullable Position position) {
      Intrinsics.checkNotNullParameter(customFieldOptionIds, "");
      super();
      this.customFieldOptionIds = customFieldOptionIds;
      this.after = after;
      this.position = position;
   }

   @JsonProperty("customFieldOptionIds")
   @NotNull
   public final List<String> getCustomFieldOptionIds() {
      return this.customFieldOptionIds;
   }

   @JsonProperty("after")
   @Nullable
   public final String getAfter() {
      return this.after;
   }

   @JsonProperty("position")
   @Nullable
   public final Position getPosition() {
      return this.position;
   }

   @NotNull
   public final List<String> component1() {
      return this.customFieldOptionIds;
   }

   @Nullable
   public final String component2() {
      return this.after;
   }

   @Nullable
   public final Position component3() {
      return this.position;
   }

   @NotNull
   public final OrderOfCustomFieldOptions copy(@NotNull List<String> customFieldOptionIds, @Nullable String after, @Nullable Position position) {
      Intrinsics.checkNotNullParameter(customFieldOptionIds, "");
      return new OrderOfCustomFieldOptions(customFieldOptionIds, after, position);
   }

   @NotNull
   public String toString() {
      return "OrderOfCustomFieldOptions(customFieldOptionIds=" + this.customFieldOptionIds + ", after=" + this.after + ", position=" + this.position + ")";
   }

   public int hashCode() {
      int var1 = this.customFieldOptionIds.hashCode();
      var1 = var1 * 31 + (this.after == null ? 0 : this.after.hashCode());
      var1 = var1 * 31 + (this.position == null ? 0 : this.position.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof OrderOfCustomFieldOptions)) {
         return false;
      } else {
         OrderOfCustomFieldOptions var2 = (OrderOfCustomFieldOptions)other;
         if (!Intrinsics.areEqual(this.customFieldOptionIds, var2.customFieldOptionIds)) {
            return false;
         } else if (!Intrinsics.areEqual(this.after, var2.after)) {
            return false;
         } else {
            return this.position == var2.position;
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/OrderOfCustomFieldOptions$Position;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "First", "Last", "zencoder"}
   )
   public static enum Position {
      @NotNull
      private final String value;
      @JsonProperty("First")
      First("First"),
      @JsonProperty("Last")
      Last("Last");

      private Position(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Position> getEntries() {
         return $ENTRIES;
      }
   }
}
