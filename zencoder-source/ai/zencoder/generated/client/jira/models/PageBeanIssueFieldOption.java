package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.net.URI;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u001d\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001Ba\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n\u0012\u0010\b\u0002\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u000e\u0018\u00010\r\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u0010\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0011J\u0010\u0010 \u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0014J\u000b\u0010!\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u0010\u0010#\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001aJ\u0010\u0010$\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001aJ\u0011\u0010%\u001a\n\u0012\u0004\u0012\u00020\u000e\u0018\u00010\rH\u00c6\u0003Jh\u0010&\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n2\u0010\b\u0002\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u000e\u0018\u00010\rH\u00c6\u0001\u00a2\u0006\u0002\u0010'J\u0013\u0010(\u001a\u00020\u00032\b\u0010)\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010*\u001a\u00020\u0005H\u00d6\u0001J\t\u0010+\u001a\u00020,H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0002\u0010\u0011R\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\u0015\u001a\u0004\b\u0013\u0010\u0014R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0015\u0010\b\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0017R\u0017\u0010\t\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\n\n\u0002\u0010\u001b\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\u000b\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\n\n\u0002\u0010\u001b\u001a\u0004\b\u001c\u0010\u001aR\u001b\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u000e\u0018\u00010\r8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001e\u00a8\u0006-"},
   d2 = {"Lai/zencoder/generated/client/jira/models/PageBeanIssueFieldOption;", "", "isLast", "", "maxResults", "", "nextPage", "Ljava/net/URI;", "self", "startAt", "", "total", "propertyValues", "", "Lai/zencoder/generated/client/jira/models/IssueFieldOption;", "<init>", "(Ljava/lang/Boolean;Ljava/lang/Integer;Ljava/net/URI;Ljava/net/URI;Ljava/lang/Long;Ljava/lang/Long;Ljava/util/List;)V", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getMaxResults", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getNextPage", "()Ljava/net/URI;", "getSelf", "getStartAt", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getTotal", "getPropertyValues", "()Ljava/util/List;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "(Ljava/lang/Boolean;Ljava/lang/Integer;Ljava/net/URI;Ljava/net/URI;Ljava/lang/Long;Ljava/lang/Long;Ljava/util/List;)Lai/zencoder/generated/client/jira/models/PageBeanIssueFieldOption;", "equals", "other", "hashCode", "toString", "", "zencoder"}
)
public final class PageBeanIssueFieldOption {
   @Nullable
   private final Boolean isLast;
   @Nullable
   private final Integer maxResults;
   @Nullable
   private final URI nextPage;
   @Nullable
   private final URI self;
   @Nullable
   private final Long startAt;
   @Nullable
   private final Long total;
   @Nullable
   private final List<IssueFieldOption> propertyValues;

   public PageBeanIssueFieldOption(@Nullable Boolean isLast, @Nullable Integer maxResults, @Nullable URI nextPage, @Nullable URI self, @Nullable Long startAt, @Nullable Long total, @Nullable List<IssueFieldOption> propertyValues) {
      this.isLast = isLast;
      this.maxResults = maxResults;
      this.nextPage = nextPage;
      this.self = self;
      this.startAt = startAt;
      this.total = total;
      this.propertyValues = propertyValues;
   }

   @JsonProperty("isLast")
   @Nullable
   public final Boolean isLast() {
      return this.isLast;
   }

   @JsonProperty("maxResults")
   @Nullable
   public final Integer getMaxResults() {
      return this.maxResults;
   }

   @JsonProperty("nextPage")
   @Nullable
   public final URI getNextPage() {
      return this.nextPage;
   }

   @JsonProperty("self")
   @Nullable
   public final URI getSelf() {
      return this.self;
   }

   @JsonProperty("startAt")
   @Nullable
   public final Long getStartAt() {
      return this.startAt;
   }

   @JsonProperty("total")
   @Nullable
   public final Long getTotal() {
      return this.total;
   }

   @JsonProperty("values")
   @Nullable
   public final List<IssueFieldOption> getPropertyValues() {
      return this.propertyValues;
   }

   @Nullable
   public final Boolean component1() {
      return this.isLast;
   }

   @Nullable
   public final Integer component2() {
      return this.maxResults;
   }

   @Nullable
   public final URI component3() {
      return this.nextPage;
   }

   @Nullable
   public final URI component4() {
      return this.self;
   }

   @Nullable
   public final Long component5() {
      return this.startAt;
   }

   @Nullable
   public final Long component6() {
      return this.total;
   }

   @Nullable
   public final List<IssueFieldOption> component7() {
      return this.propertyValues;
   }

   @NotNull
   public final PageBeanIssueFieldOption copy(@Nullable Boolean isLast, @Nullable Integer maxResults, @Nullable URI nextPage, @Nullable URI self, @Nullable Long startAt, @Nullable Long total, @Nullable List<IssueFieldOption> propertyValues) {
      return new PageBeanIssueFieldOption(isLast, maxResults, nextPage, self, startAt, total, propertyValues);
   }

   @NotNull
   public String toString() {
      return "PageBeanIssueFieldOption(isLast=" + this.isLast + ", maxResults=" + this.maxResults + ", nextPage=" + this.nextPage + ", self=" + this.self + ", startAt=" + this.startAt + ", total=" + this.total + ", propertyValues=" + this.propertyValues + ")";
   }

   public int hashCode() {
      int var1 = this.isLast == null ? 0 : this.isLast.hashCode();
      var1 = var1 * 31 + (this.maxResults == null ? 0 : this.maxResults.hashCode());
      var1 = var1 * 31 + (this.nextPage == null ? 0 : this.nextPage.hashCode());
      var1 = var1 * 31 + (this.self == null ? 0 : this.self.hashCode());
      var1 = var1 * 31 + (this.startAt == null ? 0 : this.startAt.hashCode());
      var1 = var1 * 31 + (this.total == null ? 0 : this.total.hashCode());
      var1 = var1 * 31 + (this.propertyValues == null ? 0 : this.propertyValues.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof PageBeanIssueFieldOption)) {
         return false;
      } else {
         PageBeanIssueFieldOption var2 = (PageBeanIssueFieldOption)other;
         if (!Intrinsics.areEqual(this.isLast, var2.isLast)) {
            return false;
         } else if (!Intrinsics.areEqual(this.maxResults, var2.maxResults)) {
            return false;
         } else if (!Intrinsics.areEqual(this.nextPage, var2.nextPage)) {
            return false;
         } else if (!Intrinsics.areEqual(this.self, var2.self)) {
            return false;
         } else if (!Intrinsics.areEqual(this.startAt, var2.startAt)) {
            return false;
         } else if (!Intrinsics.areEqual(this.total, var2.total)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.propertyValues, var2.propertyValues);
         }
      }
   }

   public PageBeanIssueFieldOption() {
      this((Boolean)null, (Integer)null, (URI)null, (URI)null, (Long)null, (Long)null, (List)null, 127, (DefaultConstructorMarker)null);
   }
}
