package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004BO\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\f\u00a2\u0006\u0004\b\u000e\u0010\u000fJ\u0011\u0010\u001a\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006H\u00c6\u0003J\u0010\u0010\u001b\u001a\u0004\u0018\u00010\tH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u0011\u0010\u001c\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006H\u00c6\u0003J\u0010\u0010\u001d\u001a\u0004\u0018\u00010\fH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0017J\u0010\u0010\u001e\u001a\u0004\u0018\u00010\fH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0017JV\u0010\u001f\u001a\u00020\u00002\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00062\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00062\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\fH\u00c6\u0001\u00a2\u0006\u0002\u0010 J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010$\u001a\u00020\tH\u00d6\u0001J\t\u0010%\u001a\u00020\u0002H\u00d6\u0001R\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0017\u0010\b\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b\u0012\u0010\u0013R\u001b\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0011R\u0017\u0010\u000b\u001a\u0004\u0018\u00010\f8G\u00a2\u0006\n\n\u0002\u0010\u0018\u001a\u0004\b\u0016\u0010\u0017R\u0017\u0010\r\u001a\u0004\u0018\u00010\f8G\u00a2\u0006\n\n\u0002\u0010\u0018\u001a\u0004\b\u0019\u0010\u0017\u00a8\u0006&"},
   d2 = {"Lai/zencoder/generated/client/jira/models/PageOfCreateMetaIssueTypeWithField;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "fields", "", "Lai/zencoder/generated/client/jira/models/FieldCreateMetadata;", "maxResults", "", "results", "startAt", "", "total", "<init>", "(Ljava/util/List;Ljava/lang/Integer;Ljava/util/List;Ljava/lang/Long;Ljava/lang/Long;)V", "getFields", "()Ljava/util/List;", "getMaxResults", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getResults", "getStartAt", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getTotal", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/util/List;Ljava/lang/Integer;Ljava/util/List;Ljava/lang/Long;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/PageOfCreateMetaIssueTypeWithField;", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class PageOfCreateMetaIssueTypeWithField extends HashMap<String, Object> {
   @Nullable
   private final List<FieldCreateMetadata> fields;
   @Nullable
   private final Integer maxResults;
   @Nullable
   private final List<FieldCreateMetadata> results;
   @Nullable
   private final Long startAt;
   @Nullable
   private final Long total;

   public PageOfCreateMetaIssueTypeWithField(@Nullable List<FieldCreateMetadata> fields, @Nullable Integer maxResults, @Nullable List<FieldCreateMetadata> results, @Nullable Long startAt, @Nullable Long total) {
      this.fields = fields;
      this.maxResults = maxResults;
      this.results = results;
      this.startAt = startAt;
      this.total = total;
   }

   @JsonProperty("fields")
   @Nullable
   public final List<FieldCreateMetadata> getFields() {
      return this.fields;
   }

   @JsonProperty("maxResults")
   @Nullable
   public final Integer getMaxResults() {
      return this.maxResults;
   }

   @JsonProperty("results")
   @Nullable
   public final List<FieldCreateMetadata> getResults() {
      return this.results;
   }

   @JsonProperty("startAt")
   @Nullable
   public final Long getStartAt() {
      return this.startAt;
   }

   @JsonProperty("total")
   @Nullable
   public final Long getTotal() {
      return this.total;
   }

   @Nullable
   public final List<FieldCreateMetadata> component1() {
      return this.fields;
   }

   @Nullable
   public final Integer component2() {
      return this.maxResults;
   }

   @Nullable
   public final List<FieldCreateMetadata> component3() {
      return this.results;
   }

   @Nullable
   public final Long component4() {
      return this.startAt;
   }

   @Nullable
   public final Long component5() {
      return this.total;
   }

   @NotNull
   public final PageOfCreateMetaIssueTypeWithField copy(@Nullable List<FieldCreateMetadata> fields, @Nullable Integer maxResults, @Nullable List<FieldCreateMetadata> results, @Nullable Long startAt, @Nullable Long total) {
      return new PageOfCreateMetaIssueTypeWithField(fields, maxResults, results, startAt, total);
   }

   @NotNull
   public String toString() {
      return "PageOfCreateMetaIssueTypeWithField(fields=" + this.fields + ", maxResults=" + this.maxResults + ", results=" + this.results + ", startAt=" + this.startAt + ", total=" + this.total + ")";
   }

   public int hashCode() {
      int var1 = this.fields == null ? 0 : this.fields.hashCode();
      var1 = var1 * 31 + (this.maxResults == null ? 0 : this.maxResults.hashCode());
      var1 = var1 * 31 + (this.results == null ? 0 : this.results.hashCode());
      var1 = var1 * 31 + (this.startAt == null ? 0 : this.startAt.hashCode());
      var1 = var1 * 31 + (this.total == null ? 0 : this.total.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof PageOfCreateMetaIssueTypeWithField)) {
         return false;
      } else {
         PageOfCreateMetaIssueTypeWithField var2 = (PageOfCreateMetaIssueTypeWithField)other;
         if (!Intrinsics.areEqual(this.fields, var2.fields)) {
            return false;
         } else if (!Intrinsics.areEqual(this.maxResults, var2.maxResults)) {
            return false;
         } else if (!Intrinsics.areEqual(this.results, var2.results)) {
            return false;
         } else if (!Intrinsics.areEqual(this.startAt, var2.startAt)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.total, var2.total);
         }
      }
   }

   public PageOfCreateMetaIssueTypeWithField() {
      this((List)null, (Integer)null, (List)null, (Long)null, (Long)null, 31, (DefaultConstructorMarker)null);
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
