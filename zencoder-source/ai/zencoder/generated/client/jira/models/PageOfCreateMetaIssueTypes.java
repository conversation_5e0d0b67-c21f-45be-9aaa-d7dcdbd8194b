package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004BO\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006\u0012\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\f\u00a2\u0006\u0004\b\u000e\u0010\u000fJ\u0011\u0010\u001a\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006H\u00c6\u0003J\u0011\u0010\u001b\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006H\u00c6\u0003J\u0010\u0010\u001c\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0014J\u0010\u0010\u001d\u001a\u0004\u0018\u00010\fH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0017J\u0010\u0010\u001e\u001a\u0004\u0018\u00010\fH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0017JV\u0010\u001f\u001a\u00020\u00002\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00062\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00062\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\fH\u00c6\u0001\u00a2\u0006\u0002\u0010 J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010$\u001a\u00020\nH\u00d6\u0001J\t\u0010%\u001a\u00020\u0002H\u00d6\u0001R\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u001b\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0017\u0010\t\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\n\n\u0002\u0010\u0015\u001a\u0004\b\u0013\u0010\u0014R\u0017\u0010\u000b\u001a\u0004\u0018\u00010\f8G\u00a2\u0006\n\n\u0002\u0010\u0018\u001a\u0004\b\u0016\u0010\u0017R\u0017\u0010\r\u001a\u0004\u0018\u00010\f8G\u00a2\u0006\n\n\u0002\u0010\u0018\u001a\u0004\b\u0019\u0010\u0017\u00a8\u0006&"},
   d2 = {"Lai/zencoder/generated/client/jira/models/PageOfCreateMetaIssueTypes;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "createMetaIssueType", "", "Lai/zencoder/generated/client/jira/models/IssueTypeIssueCreateMetadata;", "issueTypes", "maxResults", "", "startAt", "", "total", "<init>", "(Ljava/util/List;Ljava/util/List;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;)V", "getCreateMetaIssueType", "()Ljava/util/List;", "getIssueTypes", "getMaxResults", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getStartAt", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getTotal", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/util/List;Ljava/util/List;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/PageOfCreateMetaIssueTypes;", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class PageOfCreateMetaIssueTypes extends HashMap<String, Object> {
   @Nullable
   private final List<IssueTypeIssueCreateMetadata> createMetaIssueType;
   @Nullable
   private final List<IssueTypeIssueCreateMetadata> issueTypes;
   @Nullable
   private final Integer maxResults;
   @Nullable
   private final Long startAt;
   @Nullable
   private final Long total;

   public PageOfCreateMetaIssueTypes(@Nullable List<IssueTypeIssueCreateMetadata> createMetaIssueType, @Nullable List<IssueTypeIssueCreateMetadata> issueTypes, @Nullable Integer maxResults, @Nullable Long startAt, @Nullable Long total) {
      this.createMetaIssueType = createMetaIssueType;
      this.issueTypes = issueTypes;
      this.maxResults = maxResults;
      this.startAt = startAt;
      this.total = total;
   }

   @JsonProperty("createMetaIssueType")
   @Nullable
   public final List<IssueTypeIssueCreateMetadata> getCreateMetaIssueType() {
      return this.createMetaIssueType;
   }

   @JsonProperty("issueTypes")
   @Nullable
   public final List<IssueTypeIssueCreateMetadata> getIssueTypes() {
      return this.issueTypes;
   }

   @JsonProperty("maxResults")
   @Nullable
   public final Integer getMaxResults() {
      return this.maxResults;
   }

   @JsonProperty("startAt")
   @Nullable
   public final Long getStartAt() {
      return this.startAt;
   }

   @JsonProperty("total")
   @Nullable
   public final Long getTotal() {
      return this.total;
   }

   @Nullable
   public final List<IssueTypeIssueCreateMetadata> component1() {
      return this.createMetaIssueType;
   }

   @Nullable
   public final List<IssueTypeIssueCreateMetadata> component2() {
      return this.issueTypes;
   }

   @Nullable
   public final Integer component3() {
      return this.maxResults;
   }

   @Nullable
   public final Long component4() {
      return this.startAt;
   }

   @Nullable
   public final Long component5() {
      return this.total;
   }

   @NotNull
   public final PageOfCreateMetaIssueTypes copy(@Nullable List<IssueTypeIssueCreateMetadata> createMetaIssueType, @Nullable List<IssueTypeIssueCreateMetadata> issueTypes, @Nullable Integer maxResults, @Nullable Long startAt, @Nullable Long total) {
      return new PageOfCreateMetaIssueTypes(createMetaIssueType, issueTypes, maxResults, startAt, total);
   }

   @NotNull
   public String toString() {
      return "PageOfCreateMetaIssueTypes(createMetaIssueType=" + this.createMetaIssueType + ", issueTypes=" + this.issueTypes + ", maxResults=" + this.maxResults + ", startAt=" + this.startAt + ", total=" + this.total + ")";
   }

   public int hashCode() {
      int var1 = this.createMetaIssueType == null ? 0 : this.createMetaIssueType.hashCode();
      var1 = var1 * 31 + (this.issueTypes == null ? 0 : this.issueTypes.hashCode());
      var1 = var1 * 31 + (this.maxResults == null ? 0 : this.maxResults.hashCode());
      var1 = var1 * 31 + (this.startAt == null ? 0 : this.startAt.hashCode());
      var1 = var1 * 31 + (this.total == null ? 0 : this.total.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof PageOfCreateMetaIssueTypes)) {
         return false;
      } else {
         PageOfCreateMetaIssueTypes var2 = (PageOfCreateMetaIssueTypes)other;
         if (!Intrinsics.areEqual(this.createMetaIssueType, var2.createMetaIssueType)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueTypes, var2.issueTypes)) {
            return false;
         } else if (!Intrinsics.areEqual(this.maxResults, var2.maxResults)) {
            return false;
         } else if (!Intrinsics.areEqual(this.startAt, var2.startAt)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.total, var2.total);
         }
      }
   }

   public PageOfCreateMetaIssueTypes() {
      this((List)null, (List)null, (Integer)null, (Long)null, (Long)null, 31, (DefaultConstructorMarker)null);
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
