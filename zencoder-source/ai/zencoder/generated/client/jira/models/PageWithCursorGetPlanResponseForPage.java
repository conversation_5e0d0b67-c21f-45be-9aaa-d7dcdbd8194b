package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u001d\b\u0086\b\u0018\u00002\u00020\u0001BU\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\r\u0018\u00010\f\u00a2\u0006\u0004\b\u000e\u0010\u000fJ\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u001f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u000b\u0010 \u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010!\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0017J\u0010\u0010\"\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001aJ\u0011\u0010#\u001a\n\u0012\u0004\u0012\u00020\r\u0018\u00010\fH\u00c6\u0003J\\\u0010$\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\r\u0018\u00010\fH\u00c6\u0001\u00a2\u0006\u0002\u0010%J\u0013\u0010&\u001a\u00020\u00052\b\u0010'\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010(\u001a\u00020\bH\u00d6\u0001J\t\u0010)\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b\u0012\u0010\u0013R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0011R\u0017\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\n\n\u0002\u0010\u0018\u001a\u0004\b\u0016\u0010\u0017R\u0017\u0010\t\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\n\n\u0002\u0010\u001b\u001a\u0004\b\u0019\u0010\u001aR\u001b\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\r\u0018\u00010\f8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001d\u00a8\u0006*"},
   d2 = {"Lai/zencoder/generated/client/jira/models/PageWithCursorGetPlanResponseForPage;", "", "cursor", "", "last", "", "nextPageCursor", "propertySize", "", "total", "", "propertyValues", "", "Lai/zencoder/generated/client/jira/models/GetPlanResponseForPage;", "<init>", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Long;Ljava/util/List;)V", "getCursor", "()Ljava/lang/String;", "getLast", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getNextPageCursor", "getPropertySize", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getTotal", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getPropertyValues", "()Ljava/util/List;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Long;Ljava/util/List;)Lai/zencoder/generated/client/jira/models/PageWithCursorGetPlanResponseForPage;", "equals", "other", "hashCode", "toString", "zencoder"}
)
public final class PageWithCursorGetPlanResponseForPage {
   @Nullable
   private final String cursor;
   @Nullable
   private final Boolean last;
   @Nullable
   private final String nextPageCursor;
   @Nullable
   private final Integer propertySize;
   @Nullable
   private final Long total;
   @Nullable
   private final List<GetPlanResponseForPage> propertyValues;

   public PageWithCursorGetPlanResponseForPage(@Nullable String cursor, @Nullable Boolean last, @Nullable String nextPageCursor, @Nullable Integer propertySize, @Nullable Long total, @Nullable List<GetPlanResponseForPage> propertyValues) {
      this.cursor = cursor;
      this.last = last;
      this.nextPageCursor = nextPageCursor;
      this.propertySize = propertySize;
      this.total = total;
      this.propertyValues = propertyValues;
   }

   @JsonProperty("cursor")
   @Nullable
   public final String getCursor() {
      return this.cursor;
   }

   @JsonProperty("last")
   @Nullable
   public final Boolean getLast() {
      return this.last;
   }

   @JsonProperty("nextPageCursor")
   @Nullable
   public final String getNextPageCursor() {
      return this.nextPageCursor;
   }

   @JsonProperty("size")
   @Nullable
   public final Integer getPropertySize() {
      return this.propertySize;
   }

   @JsonProperty("total")
   @Nullable
   public final Long getTotal() {
      return this.total;
   }

   @JsonProperty("values")
   @Nullable
   public final List<GetPlanResponseForPage> getPropertyValues() {
      return this.propertyValues;
   }

   @Nullable
   public final String component1() {
      return this.cursor;
   }

   @Nullable
   public final Boolean component2() {
      return this.last;
   }

   @Nullable
   public final String component3() {
      return this.nextPageCursor;
   }

   @Nullable
   public final Integer component4() {
      return this.propertySize;
   }

   @Nullable
   public final Long component5() {
      return this.total;
   }

   @Nullable
   public final List<GetPlanResponseForPage> component6() {
      return this.propertyValues;
   }

   @NotNull
   public final PageWithCursorGetPlanResponseForPage copy(@Nullable String cursor, @Nullable Boolean last, @Nullable String nextPageCursor, @Nullable Integer propertySize, @Nullable Long total, @Nullable List<GetPlanResponseForPage> propertyValues) {
      return new PageWithCursorGetPlanResponseForPage(cursor, last, nextPageCursor, propertySize, total, propertyValues);
   }

   @NotNull
   public String toString() {
      return "PageWithCursorGetPlanResponseForPage(cursor=" + this.cursor + ", last=" + this.last + ", nextPageCursor=" + this.nextPageCursor + ", propertySize=" + this.propertySize + ", total=" + this.total + ", propertyValues=" + this.propertyValues + ")";
   }

   public int hashCode() {
      int var1 = this.cursor == null ? 0 : this.cursor.hashCode();
      var1 = var1 * 31 + (this.last == null ? 0 : this.last.hashCode());
      var1 = var1 * 31 + (this.nextPageCursor == null ? 0 : this.nextPageCursor.hashCode());
      var1 = var1 * 31 + (this.propertySize == null ? 0 : this.propertySize.hashCode());
      var1 = var1 * 31 + (this.total == null ? 0 : this.total.hashCode());
      var1 = var1 * 31 + (this.propertyValues == null ? 0 : this.propertyValues.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof PageWithCursorGetPlanResponseForPage)) {
         return false;
      } else {
         PageWithCursorGetPlanResponseForPage var2 = (PageWithCursorGetPlanResponseForPage)other;
         if (!Intrinsics.areEqual(this.cursor, var2.cursor)) {
            return false;
         } else if (!Intrinsics.areEqual(this.last, var2.last)) {
            return false;
         } else if (!Intrinsics.areEqual(this.nextPageCursor, var2.nextPageCursor)) {
            return false;
         } else if (!Intrinsics.areEqual(this.propertySize, var2.propertySize)) {
            return false;
         } else if (!Intrinsics.areEqual(this.total, var2.total)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.propertyValues, var2.propertyValues);
         }
      }
   }

   public PageWithCursorGetPlanResponseForPage() {
      this((String)null, (Boolean)null, (String)null, (Integer)null, (Long)null, (List)null, 63, (DefaultConstructorMarker)null);
   }
}
