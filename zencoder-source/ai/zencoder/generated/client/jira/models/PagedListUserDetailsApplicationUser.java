package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BI\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\n\u0010\u000bJ\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ\u0011\u0010\u0015\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJP\u0010\u0019\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010\u001aJ\u0013\u0010\u001b\u001a\u00020\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\f\u0010\rR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\u0011\u0010\rR\u0017\u0010\b\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\u0012\u0010\rR\u0017\u0010\t\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\u0013\u0010\r\u00a8\u0006!"},
   d2 = {"Lai/zencoder/generated/client/jira/models/PagedListUserDetailsApplicationUser;", "", "endIndex", "", "items", "", "Lai/zencoder/generated/client/jira/models/UserDetails;", "maxResults", "propertySize", "startIndex", "<init>", "(Ljava/lang/Integer;Ljava/util/List;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)V", "getEndIndex", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getItems", "()Ljava/util/List;", "getMaxResults", "getPropertySize", "getStartIndex", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/lang/Integer;Ljava/util/List;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Lai/zencoder/generated/client/jira/models/PagedListUserDetailsApplicationUser;", "equals", "", "other", "hashCode", "toString", "", "zencoder"}
)
public final class PagedListUserDetailsApplicationUser {
   @Nullable
   private final Integer endIndex;
   @Nullable
   private final List<UserDetails> items;
   @Nullable
   private final Integer maxResults;
   @Nullable
   private final Integer propertySize;
   @Nullable
   private final Integer startIndex;

   public PagedListUserDetailsApplicationUser(@Nullable Integer endIndex, @Nullable List<UserDetails> items, @Nullable Integer maxResults, @Nullable Integer propertySize, @Nullable Integer startIndex) {
      this.endIndex = endIndex;
      this.items = items;
      this.maxResults = maxResults;
      this.propertySize = propertySize;
      this.startIndex = startIndex;
   }

   @JsonProperty("end-index")
   @Nullable
   public final Integer getEndIndex() {
      return this.endIndex;
   }

   @JsonProperty("items")
   @Nullable
   public final List<UserDetails> getItems() {
      return this.items;
   }

   @JsonProperty("max-results")
   @Nullable
   public final Integer getMaxResults() {
      return this.maxResults;
   }

   @JsonProperty("size")
   @Nullable
   public final Integer getPropertySize() {
      return this.propertySize;
   }

   @JsonProperty("start-index")
   @Nullable
   public final Integer getStartIndex() {
      return this.startIndex;
   }

   @Nullable
   public final Integer component1() {
      return this.endIndex;
   }

   @Nullable
   public final List<UserDetails> component2() {
      return this.items;
   }

   @Nullable
   public final Integer component3() {
      return this.maxResults;
   }

   @Nullable
   public final Integer component4() {
      return this.propertySize;
   }

   @Nullable
   public final Integer component5() {
      return this.startIndex;
   }

   @NotNull
   public final PagedListUserDetailsApplicationUser copy(@Nullable Integer endIndex, @Nullable List<UserDetails> items, @Nullable Integer maxResults, @Nullable Integer propertySize, @Nullable Integer startIndex) {
      return new PagedListUserDetailsApplicationUser(endIndex, items, maxResults, propertySize, startIndex);
   }

   @NotNull
   public String toString() {
      return "PagedListUserDetailsApplicationUser(endIndex=" + this.endIndex + ", items=" + this.items + ", maxResults=" + this.maxResults + ", propertySize=" + this.propertySize + ", startIndex=" + this.startIndex + ")";
   }

   public int hashCode() {
      int var1 = this.endIndex == null ? 0 : this.endIndex.hashCode();
      var1 = var1 * 31 + (this.items == null ? 0 : this.items.hashCode());
      var1 = var1 * 31 + (this.maxResults == null ? 0 : this.maxResults.hashCode());
      var1 = var1 * 31 + (this.propertySize == null ? 0 : this.propertySize.hashCode());
      var1 = var1 * 31 + (this.startIndex == null ? 0 : this.startIndex.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof PagedListUserDetailsApplicationUser)) {
         return false;
      } else {
         PagedListUserDetailsApplicationUser var2 = (PagedListUserDetailsApplicationUser)other;
         if (!Intrinsics.areEqual(this.endIndex, var2.endIndex)) {
            return false;
         } else if (!Intrinsics.areEqual(this.items, var2.items)) {
            return false;
         } else if (!Intrinsics.areEqual(this.maxResults, var2.maxResults)) {
            return false;
         } else if (!Intrinsics.areEqual(this.propertySize, var2.propertySize)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.startIndex, var2.startIndex);
         }
      }
   }

   public PagedListUserDetailsApplicationUser() {
      this((Integer)null, (List)null, (Integer)null, (Integer)null, (Integer)null, 31, (DefaultConstructorMarker)null);
   }
}
