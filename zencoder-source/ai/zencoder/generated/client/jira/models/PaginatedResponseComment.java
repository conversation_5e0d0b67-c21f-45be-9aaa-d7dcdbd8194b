package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b\u00a2\u0006\u0004\b\n\u0010\u000bJ\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ\u0011\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010\u0017\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0012J\u0010\u0010\u0018\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0012JD\u0010\u0019\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\bH\u00c6\u0001\u00a2\u0006\u0002\u0010\u001aJ\u0013\u0010\u001b\u001a\u00020\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\f\u0010\rR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\n\n\u0002\u0010\u0013\u001a\u0004\b\u0011\u0010\u0012R\u0017\u0010\t\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\n\n\u0002\u0010\u0013\u001a\u0004\b\u0014\u0010\u0012\u00a8\u0006!"},
   d2 = {"Lai/zencoder/generated/client/jira/models/PaginatedResponseComment;", "", "maxResults", "", "results", "", "Lai/zencoder/generated/client/jira/models/Comment;", "startAt", "", "total", "<init>", "(Ljava/lang/Integer;Ljava/util/List;Ljava/lang/Long;Ljava/lang/Long;)V", "getMaxResults", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getResults", "()Ljava/util/List;", "getStartAt", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getTotal", "component1", "component2", "component3", "component4", "copy", "(Ljava/lang/Integer;Ljava/util/List;Ljava/lang/Long;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/PaginatedResponseComment;", "equals", "", "other", "hashCode", "toString", "", "zencoder"}
)
public final class PaginatedResponseComment {
   @Nullable
   private final Integer maxResults;
   @Nullable
   private final List<Comment> results;
   @Nullable
   private final Long startAt;
   @Nullable
   private final Long total;

   public PaginatedResponseComment(@Nullable Integer maxResults, @Nullable List<Comment> results, @Nullable Long startAt, @Nullable Long total) {
      this.maxResults = maxResults;
      this.results = results;
      this.startAt = startAt;
      this.total = total;
   }

   @JsonProperty("maxResults")
   @Nullable
   public final Integer getMaxResults() {
      return this.maxResults;
   }

   @JsonProperty("results")
   @Nullable
   public final List<Comment> getResults() {
      return this.results;
   }

   @JsonProperty("startAt")
   @Nullable
   public final Long getStartAt() {
      return this.startAt;
   }

   @JsonProperty("total")
   @Nullable
   public final Long getTotal() {
      return this.total;
   }

   @Nullable
   public final Integer component1() {
      return this.maxResults;
   }

   @Nullable
   public final List<Comment> component2() {
      return this.results;
   }

   @Nullable
   public final Long component3() {
      return this.startAt;
   }

   @Nullable
   public final Long component4() {
      return this.total;
   }

   @NotNull
   public final PaginatedResponseComment copy(@Nullable Integer maxResults, @Nullable List<Comment> results, @Nullable Long startAt, @Nullable Long total) {
      return new PaginatedResponseComment(maxResults, results, startAt, total);
   }

   @NotNull
   public String toString() {
      return "PaginatedResponseComment(maxResults=" + this.maxResults + ", results=" + this.results + ", startAt=" + this.startAt + ", total=" + this.total + ")";
   }

   public int hashCode() {
      int var1 = this.maxResults == null ? 0 : this.maxResults.hashCode();
      var1 = var1 * 31 + (this.results == null ? 0 : this.results.hashCode());
      var1 = var1 * 31 + (this.startAt == null ? 0 : this.startAt.hashCode());
      var1 = var1 * 31 + (this.total == null ? 0 : this.total.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof PaginatedResponseComment)) {
         return false;
      } else {
         PaginatedResponseComment var2 = (PaginatedResponseComment)other;
         if (!Intrinsics.areEqual(this.maxResults, var2.maxResults)) {
            return false;
         } else if (!Intrinsics.areEqual(this.results, var2.results)) {
            return false;
         } else if (!Intrinsics.areEqual(this.startAt, var2.startAt)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.total, var2.total);
         }
      }
   }

   public PaginatedResponseComment() {
      this((Integer)null, (List)null, (Long)null, (Long)null, 15, (DefaultConstructorMarker)null);
   }
}
