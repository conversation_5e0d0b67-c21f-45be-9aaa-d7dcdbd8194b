package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001e\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004Bo\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0002\u00a2\u0006\u0004\b\u0011\u0010\u0012J\t\u0010 \u001a\u00020\u0002H\u00c6\u0003J\u0010\u0010!\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0016J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u0010\u0010$\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0016J\u000b\u0010%\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\u000b\u0010'\u001a\u0004\u0018\u00010\u000fH\u00c6\u0003J\u000b\u0010(\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003Jx\u0010)\u001a\u00020\u00002\b\b\u0002\u0010\u0005\u001a\u00020\u00022\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0002H\u00c6\u0001\u00a2\u0006\u0002\u0010*J\u0013\u0010+\u001a\u00020\u00072\b\u0010,\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010-\u001a\u00020.H\u00d6\u0001J\t\u0010/\u001a\u00020\u0002H\u00d6\u0001R\u0013\u0010\u0005\u001a\u00020\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0017\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u0017\u001a\u0004\b\u0015\u0010\u0016R\u0015\u0010\b\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0014R\u0015\u0010\t\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0014R\u0017\u0010\n\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u0017\u001a\u0004\b\n\u0010\u0016R\u0015\u0010\u000b\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0014R\u0015\u0010\f\u001a\u0004\u0018\u00010\r8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0015\u0010\u000e\u001a\u0004\u0018\u00010\u000f8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0015\u0010\u0010\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0014\u00a8\u00060"},
   d2 = {"Lai/zencoder/generated/client/jira/models/PrioritySchemeWithPaginatedPrioritiesAndProjects;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "id", "default", "", "defaultPriorityId", "description", "isDefault", "NAME", "priorities", "Lai/zencoder/generated/client/jira/models/PageBeanPriorityWithSequence;", "projects", "Lai/zencoder/generated/client/jira/models/PageBeanProjectDetails;", "self", "<init>", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/PageBeanPriorityWithSequence;Lai/zencoder/generated/client/jira/models/PageBeanProjectDetails;Ljava/lang/String;)V", "getId", "()Ljava/lang/String;", "getDefault", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getDefaultPriorityId", "getDescription", "getNAME", "getPriorities", "()Lai/zencoder/generated/client/jira/models/PageBeanPriorityWithSequence;", "getProjects", "()Lai/zencoder/generated/client/jira/models/PageBeanProjectDetails;", "getSelf", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/PageBeanPriorityWithSequence;Lai/zencoder/generated/client/jira/models/PageBeanProjectDetails;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/PrioritySchemeWithPaginatedPrioritiesAndProjects;", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class PrioritySchemeWithPaginatedPrioritiesAndProjects extends HashMap<String, Object> {
   @NotNull
   private final String id;
   @Nullable
   private final Boolean default;
   @Nullable
   private final String defaultPriorityId;
   @Nullable
   private final String description;
   @Nullable
   private final Boolean isDefault;
   @Nullable
   private final String NAME;
   @Nullable
   private final PageBeanPriorityWithSequence priorities;
   @Nullable
   private final PageBeanProjectDetails projects;
   @Nullable
   private final String self;

   public PrioritySchemeWithPaginatedPrioritiesAndProjects(@NotNull String id, @Nullable Boolean var2, @Nullable String defaultPriorityId, @Nullable String description, @Nullable Boolean isDefault, @Nullable String NAME, @Nullable PageBeanPriorityWithSequence priorities, @Nullable PageBeanProjectDetails projects, @Nullable String self) {
      Intrinsics.checkNotNullParameter(id, "");
      super();
      this.id = id;
      this.default = var2;
      this.defaultPriorityId = defaultPriorityId;
      this.description = description;
      this.isDefault = isDefault;
      this.NAME = NAME;
      this.priorities = priorities;
      this.projects = projects;
      this.self = self;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("default")
   @Nullable
   public final Boolean getDefault() {
      return this.default;
   }

   @JsonProperty("defaultPriorityId")
   @Nullable
   public final String getDefaultPriorityId() {
      return this.defaultPriorityId;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("isDefault")
   @Nullable
   public final Boolean isDefault() {
      return this.isDefault;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("priorities")
   @Nullable
   public final PageBeanPriorityWithSequence getPriorities() {
      return this.priorities;
   }

   @JsonProperty("projects")
   @Nullable
   public final PageBeanProjectDetails getProjects() {
      return this.projects;
   }

   @JsonProperty("self")
   @Nullable
   public final String getSelf() {
      return this.self;
   }

   @NotNull
   public final String component1() {
      return this.id;
   }

   @Nullable
   public final Boolean component2() {
      return this.default;
   }

   @Nullable
   public final String component3() {
      return this.defaultPriorityId;
   }

   @Nullable
   public final String component4() {
      return this.description;
   }

   @Nullable
   public final Boolean component5() {
      return this.isDefault;
   }

   @Nullable
   public final String component6() {
      return this.NAME;
   }

   @Nullable
   public final PageBeanPriorityWithSequence component7() {
      return this.priorities;
   }

   @Nullable
   public final PageBeanProjectDetails component8() {
      return this.projects;
   }

   @Nullable
   public final String component9() {
      return this.self;
   }

   @NotNull
   public final PrioritySchemeWithPaginatedPrioritiesAndProjects copy(@NotNull String id, @Nullable Boolean var2, @Nullable String defaultPriorityId, @Nullable String description, @Nullable Boolean isDefault, @Nullable String NAME, @Nullable PageBeanPriorityWithSequence priorities, @Nullable PageBeanProjectDetails projects, @Nullable String self) {
      Intrinsics.checkNotNullParameter(id, "");
      return new PrioritySchemeWithPaginatedPrioritiesAndProjects(id, var2, defaultPriorityId, description, isDefault, NAME, priorities, projects, self);
   }

   @NotNull
   public String toString() {
      return "PrioritySchemeWithPaginatedPrioritiesAndProjects(id=" + this.id + ", default=" + this.default + ", defaultPriorityId=" + this.defaultPriorityId + ", description=" + this.description + ", isDefault=" + this.isDefault + ", NAME=" + this.NAME + ", priorities=" + this.priorities + ", projects=" + this.projects + ", self=" + this.self + ")";
   }

   public int hashCode() {
      int var1 = this.id.hashCode();
      var1 = var1 * 31 + (this.default == null ? 0 : this.default.hashCode());
      var1 = var1 * 31 + (this.defaultPriorityId == null ? 0 : this.defaultPriorityId.hashCode());
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.isDefault == null ? 0 : this.isDefault.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.priorities == null ? 0 : this.priorities.hashCode());
      var1 = var1 * 31 + (this.projects == null ? 0 : this.projects.hashCode());
      var1 = var1 * 31 + (this.self == null ? 0 : this.self.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof PrioritySchemeWithPaginatedPrioritiesAndProjects)) {
         return false;
      } else {
         PrioritySchemeWithPaginatedPrioritiesAndProjects var2 = (PrioritySchemeWithPaginatedPrioritiesAndProjects)other;
         if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.default, var2.default)) {
            return false;
         } else if (!Intrinsics.areEqual(this.defaultPriorityId, var2.defaultPriorityId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.isDefault, var2.isDefault)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else if (!Intrinsics.areEqual(this.priorities, var2.priorities)) {
            return false;
         } else if (!Intrinsics.areEqual(this.projects, var2.projects)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.self, var2.self);
         }
      }
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
