package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0018\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Ba\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\b\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\r\u0010\u000eJ\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u001c\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\bH\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003Jc\u0010 \u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\b2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010$\u001a\u00020%H\u00d6\u0001J\t\u0010&\u001a\u00020\u0005H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u001b\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0015\u0010\n\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0012R\u0015\u0010\u000b\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0012R\u0015\u0010\f\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0012\u00a8\u0006'"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ProjectIssueCreateMetadata;", "", "avatarUrls", "Lai/zencoder/generated/client/jira/models/AvatarUrlsBean;", "expand", "", "id", "issuetypes", "", "Lai/zencoder/generated/client/jira/models/IssueTypeIssueCreateMetadata;", "key", "NAME", "self", "<init>", "(Lai/zencoder/generated/client/jira/models/AvatarUrlsBean;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getAvatarUrls", "()Lai/zencoder/generated/client/jira/models/AvatarUrlsBean;", "getExpand", "()Ljava/lang/String;", "getId", "getIssuetypes", "()Ljava/util/List;", "getKey", "getNAME", "getSelf", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class ProjectIssueCreateMetadata {
   @Nullable
   private final AvatarUrlsBean avatarUrls;
   @Nullable
   private final String expand;
   @Nullable
   private final String id;
   @Nullable
   private final List<IssueTypeIssueCreateMetadata> issuetypes;
   @Nullable
   private final String key;
   @Nullable
   private final String NAME;
   @Nullable
   private final String self;

   public ProjectIssueCreateMetadata(@Nullable AvatarUrlsBean avatarUrls, @Nullable String expand, @Nullable String id, @Nullable List<IssueTypeIssueCreateMetadata> issuetypes, @Nullable String key, @Nullable String NAME, @Nullable String self) {
      this.avatarUrls = avatarUrls;
      this.expand = expand;
      this.id = id;
      this.issuetypes = issuetypes;
      this.key = key;
      this.NAME = NAME;
      this.self = self;
   }

   @JsonProperty("avatarUrls")
   @Nullable
   public final AvatarUrlsBean getAvatarUrls() {
      return this.avatarUrls;
   }

   @JsonProperty("expand")
   @Nullable
   public final String getExpand() {
      return this.expand;
   }

   @JsonProperty("id")
   @Nullable
   public final String getId() {
      return this.id;
   }

   @JsonProperty("issuetypes")
   @Nullable
   public final List<IssueTypeIssueCreateMetadata> getIssuetypes() {
      return this.issuetypes;
   }

   @JsonProperty("key")
   @Nullable
   public final String getKey() {
      return this.key;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("self")
   @Nullable
   public final String getSelf() {
      return this.self;
   }

   @Nullable
   public final AvatarUrlsBean component1() {
      return this.avatarUrls;
   }

   @Nullable
   public final String component2() {
      return this.expand;
   }

   @Nullable
   public final String component3() {
      return this.id;
   }

   @Nullable
   public final List<IssueTypeIssueCreateMetadata> component4() {
      return this.issuetypes;
   }

   @Nullable
   public final String component5() {
      return this.key;
   }

   @Nullable
   public final String component6() {
      return this.NAME;
   }

   @Nullable
   public final String component7() {
      return this.self;
   }

   @NotNull
   public final ProjectIssueCreateMetadata copy(@Nullable AvatarUrlsBean avatarUrls, @Nullable String expand, @Nullable String id, @Nullable List<IssueTypeIssueCreateMetadata> issuetypes, @Nullable String key, @Nullable String NAME, @Nullable String self) {
      return new ProjectIssueCreateMetadata(avatarUrls, expand, id, issuetypes, key, NAME, self);
   }

   @NotNull
   public String toString() {
      return "ProjectIssueCreateMetadata(avatarUrls=" + this.avatarUrls + ", expand=" + this.expand + ", id=" + this.id + ", issuetypes=" + this.issuetypes + ", key=" + this.key + ", NAME=" + this.NAME + ", self=" + this.self + ")";
   }

   public int hashCode() {
      int var1 = this.avatarUrls == null ? 0 : this.avatarUrls.hashCode();
      var1 = var1 * 31 + (this.expand == null ? 0 : this.expand.hashCode());
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      var1 = var1 * 31 + (this.issuetypes == null ? 0 : this.issuetypes.hashCode());
      var1 = var1 * 31 + (this.key == null ? 0 : this.key.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.self == null ? 0 : this.self.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ProjectIssueCreateMetadata)) {
         return false;
      } else {
         ProjectIssueCreateMetadata var2 = (ProjectIssueCreateMetadata)other;
         if (!Intrinsics.areEqual(this.avatarUrls, var2.avatarUrls)) {
            return false;
         } else if (!Intrinsics.areEqual(this.expand, var2.expand)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issuetypes, var2.issuetypes)) {
            return false;
         } else if (!Intrinsics.areEqual(this.key, var2.key)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.self, var2.self);
         }
      }
   }

   public ProjectIssueCreateMetadata() {
      this((AvatarUrlsBean)null, (String)null, (String)null, (List)null, (String)null, (String)null, (String)null, 127, (DefaultConstructorMarker)null);
   }
}
