package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0011\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\fJ,\u0010\u0010\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0011J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0017\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\n\n\u0002\u0010\r\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ProjectIssueTypeHierarchy;", "", "hierarchy", "", "Lai/zencoder/generated/client/jira/models/ProjectIssueTypesHierarchyLevel;", "projectId", "", "<init>", "(Ljava/util/List;Ljava/lang/Long;)V", "getHierarchy", "()Ljava/util/List;", "getProjectId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "component1", "component2", "copy", "(Ljava/util/List;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/ProjectIssueTypeHierarchy;", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class ProjectIssueTypeHierarchy {
   @Nullable
   private final List<ProjectIssueTypesHierarchyLevel> hierarchy;
   @Nullable
   private final Long projectId;

   public ProjectIssueTypeHierarchy(@Nullable List<ProjectIssueTypesHierarchyLevel> hierarchy, @Nullable Long projectId) {
      this.hierarchy = hierarchy;
      this.projectId = projectId;
   }

   @JsonProperty("hierarchy")
   @Nullable
   public final List<ProjectIssueTypesHierarchyLevel> getHierarchy() {
      return this.hierarchy;
   }

   @JsonProperty("projectId")
   @Nullable
   public final Long getProjectId() {
      return this.projectId;
   }

   @Nullable
   public final List<ProjectIssueTypesHierarchyLevel> component1() {
      return this.hierarchy;
   }

   @Nullable
   public final Long component2() {
      return this.projectId;
   }

   @NotNull
   public final ProjectIssueTypeHierarchy copy(@Nullable List<ProjectIssueTypesHierarchyLevel> hierarchy, @Nullable Long projectId) {
      return new ProjectIssueTypeHierarchy(hierarchy, projectId);
   }

   @NotNull
   public String toString() {
      return "ProjectIssueTypeHierarchy(hierarchy=" + this.hierarchy + ", projectId=" + this.projectId + ")";
   }

   public int hashCode() {
      int var1 = this.hierarchy == null ? 0 : this.hierarchy.hashCode();
      var1 = var1 * 31 + (this.projectId == null ? 0 : this.projectId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ProjectIssueTypeHierarchy)) {
         return false;
      } else {
         ProjectIssueTypeHierarchy var2 = (ProjectIssueTypeHierarchy)other;
         if (!Intrinsics.areEqual(this.hierarchy, var2.hierarchy)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.projectId, var2.projectId);
         }
      }
   }

   public ProjectIssueTypeHierarchy() {
      this((List)null, (Long)null, 3, (DefaultConstructorMarker)null);
   }
}
