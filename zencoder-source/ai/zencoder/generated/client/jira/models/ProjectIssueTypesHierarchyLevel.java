package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0004\b\u000b\u0010\fJ\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0017\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010\u0018\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0012J\u000b\u0010\u0019\u001a\u0004\u0018\u00010\nH\u00c6\u0003JD\u0010\u001a\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\nH\u00c6\u0001\u00a2\u0006\u0002\u0010\u001bJ\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020\bH\u00d6\u0001J\t\u0010 \u001a\u00020\nH\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\n\n\u0002\u0010\u0013\u001a\u0004\b\u0011\u0010\u0012R\u0015\u0010\t\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015\u00a8\u0006!"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ProjectIssueTypesHierarchyLevel;", "", "entityId", "Ljava/util/UUID;", "issueTypes", "", "Lai/zencoder/generated/client/jira/models/IssueTypeInfo;", "level", "", "NAME", "", "<init>", "(Ljava/util/UUID;Ljava/util/List;Ljava/lang/Integer;Ljava/lang/String;)V", "getEntityId", "()Ljava/util/UUID;", "getIssueTypes", "()Ljava/util/List;", "getLevel", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getNAME", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "copy", "(Ljava/util/UUID;Ljava/util/List;Ljava/lang/Integer;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/ProjectIssueTypesHierarchyLevel;", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class ProjectIssueTypesHierarchyLevel {
   @Nullable
   private final UUID entityId;
   @Nullable
   private final List<IssueTypeInfo> issueTypes;
   @Nullable
   private final Integer level;
   @Nullable
   private final String NAME;

   public ProjectIssueTypesHierarchyLevel(@Nullable UUID entityId, @Nullable List<IssueTypeInfo> issueTypes, @Nullable Integer level, @Nullable String NAME) {
      this.entityId = entityId;
      this.issueTypes = issueTypes;
      this.level = level;
      this.NAME = NAME;
   }

   @JsonProperty("entityId")
   @Nullable
   public final UUID getEntityId() {
      return this.entityId;
   }

   @JsonProperty("issueTypes")
   @Nullable
   public final List<IssueTypeInfo> getIssueTypes() {
      return this.issueTypes;
   }

   @JsonProperty("level")
   @Nullable
   public final Integer getLevel() {
      return this.level;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @Nullable
   public final UUID component1() {
      return this.entityId;
   }

   @Nullable
   public final List<IssueTypeInfo> component2() {
      return this.issueTypes;
   }

   @Nullable
   public final Integer component3() {
      return this.level;
   }

   @Nullable
   public final String component4() {
      return this.NAME;
   }

   @NotNull
   public final ProjectIssueTypesHierarchyLevel copy(@Nullable UUID entityId, @Nullable List<IssueTypeInfo> issueTypes, @Nullable Integer level, @Nullable String NAME) {
      return new ProjectIssueTypesHierarchyLevel(entityId, issueTypes, level, NAME);
   }

   @NotNull
   public String toString() {
      return "ProjectIssueTypesHierarchyLevel(entityId=" + this.entityId + ", issueTypes=" + this.issueTypes + ", level=" + this.level + ", NAME=" + this.NAME + ")";
   }

   public int hashCode() {
      int var1 = this.entityId == null ? 0 : this.entityId.hashCode();
      var1 = var1 * 31 + (this.issueTypes == null ? 0 : this.issueTypes.hashCode());
      var1 = var1 * 31 + (this.level == null ? 0 : this.level.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ProjectIssueTypesHierarchyLevel)) {
         return false;
      } else {
         ProjectIssueTypesHierarchyLevel var2 = (ProjectIssueTypesHierarchyLevel)other;
         if (!Intrinsics.areEqual(this.entityId, var2.entityId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueTypes, var2.issueTypes)) {
            return false;
         } else if (!Intrinsics.areEqual(this.level, var2.level)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.NAME, var2.NAME);
         }
      }
   }

   public ProjectIssueTypesHierarchyLevel() {
      this((UUID)null, (List)null, (Integer)null, (String)null, 15, (DefaultConstructorMarker)null);
   }
}
