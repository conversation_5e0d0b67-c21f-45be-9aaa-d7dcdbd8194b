package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010 \n\u0000\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\u001c\b\u0002\u0010\u0002\u001a\u0016\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u0005\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u001d\u0010\u000f\u001a\u0016\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u0005\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ8\u0010\u0011\u001a\u00020\u00002\u001c\b\u0002\u0010\u0002\u001a\u0016\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u0005\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0012J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0004H\u00d6\u0001R'\u0010\u0002\u001a\u0016\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u0005\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0017\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ProjectRoleActorsUpdateBean;", "", "categorisedActors", "", "", "", "id", "", "<init>", "(Ljava/util/Map;Ljava/lang/Long;)V", "getCategorisedActors", "()Ljava/util/Map;", "getId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "component1", "component2", "copy", "(Ljava/util/Map;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/ProjectRoleActorsUpdateBean;", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class ProjectRoleActorsUpdateBean {
   @Nullable
   private final Map<String, List<String>> categorisedActors;
   @Nullable
   private final Long id;

   public ProjectRoleActorsUpdateBean(@Nullable Map<String, ? extends List<String>> categorisedActors, @Nullable Long id) {
      this.categorisedActors = categorisedActors;
      this.id = id;
   }

   @JsonProperty("categorisedActors")
   @Nullable
   public final Map<String, List<String>> getCategorisedActors() {
      return this.categorisedActors;
   }

   @JsonProperty("id")
   @Nullable
   public final Long getId() {
      return this.id;
   }

   @Nullable
   public final Map<String, List<String>> component1() {
      return this.categorisedActors;
   }

   @Nullable
   public final Long component2() {
      return this.id;
   }

   @NotNull
   public final ProjectRoleActorsUpdateBean copy(@Nullable Map<String, ? extends List<String>> categorisedActors, @Nullable Long id) {
      return new ProjectRoleActorsUpdateBean(categorisedActors, id);
   }

   @NotNull
   public String toString() {
      return "ProjectRoleActorsUpdateBean(categorisedActors=" + this.categorisedActors + ", id=" + this.id + ")";
   }

   public int hashCode() {
      int var1 = this.categorisedActors == null ? 0 : this.categorisedActors.hashCode();
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ProjectRoleActorsUpdateBean)) {
         return false;
      } else {
         ProjectRoleActorsUpdateBean var2 = (ProjectRoleActorsUpdateBean)other;
         if (!Intrinsics.areEqual(this.categorisedActors, var2.categorisedActors)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.id, var2.id);
         }
      }
   }

   public ProjectRoleActorsUpdateBean() {
      this((Map)null, (Long)null, 3, (DefaultConstructorMarker)null);
   }
}
