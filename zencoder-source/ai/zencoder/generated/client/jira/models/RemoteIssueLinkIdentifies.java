package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0010\u0010\r\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\tJ\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J&\u0010\u000f\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0010J\u0013\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0005H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\n\u001a\u0004\b\b\u0010\tR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0017"},
   d2 = {"Lai/zencoder/generated/client/jira/models/RemoteIssueLinkIdentifies;", "", "id", "", "self", "", "<init>", "(Ljava/lang/Long;Ljava/lang/String;)V", "getId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getSelf", "()Ljava/lang/String;", "component1", "component2", "copy", "(Ljava/lang/Long;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/RemoteIssueLinkIdentifies;", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class RemoteIssueLinkIdentifies {
   @Nullable
   private final Long id;
   @Nullable
   private final String self;

   public RemoteIssueLinkIdentifies(@Nullable Long id, @Nullable String self) {
      this.id = id;
      this.self = self;
   }

   @JsonProperty("id")
   @Nullable
   public final Long getId() {
      return this.id;
   }

   @JsonProperty("self")
   @Nullable
   public final String getSelf() {
      return this.self;
   }

   @Nullable
   public final Long component1() {
      return this.id;
   }

   @Nullable
   public final String component2() {
      return this.self;
   }

   @NotNull
   public final RemoteIssueLinkIdentifies copy(@Nullable Long id, @Nullable String self) {
      return new RemoteIssueLinkIdentifies(id, self);
   }

   @NotNull
   public String toString() {
      return "RemoteIssueLinkIdentifies(id=" + this.id + ", self=" + this.self + ")";
   }

   public int hashCode() {
      int var1 = this.id == null ? 0 : this.id.hashCode();
      var1 = var1 * 31 + (this.self == null ? 0 : this.self.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof RemoteIssueLinkIdentifies)) {
         return false;
      } else {
         RemoteIssueLinkIdentifies var2 = (RemoteIssueLinkIdentifies)other;
         if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.self, var2.self);
         }
      }
   }

   public RemoteIssueLinkIdentifies() {
      this((Long)null, (String)null, 3, (DefaultConstructorMarker)null);
   }
}
