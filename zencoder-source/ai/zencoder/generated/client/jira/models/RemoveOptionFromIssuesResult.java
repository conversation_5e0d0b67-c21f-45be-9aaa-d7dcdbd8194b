package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\t\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u00a2\u0006\u0004\b\b\u0010\tJ\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J9\u0010\u0012\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001b\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\r\u00a8\u0006\u001a"},
   d2 = {"Lai/zencoder/generated/client/jira/models/RemoveOptionFromIssuesResult;", "", "errors", "Lai/zencoder/generated/client/jira/models/SimpleErrorCollection;", "modifiedIssues", "", "", "unmodifiedIssues", "<init>", "(Lai/zencoder/generated/client/jira/models/SimpleErrorCollection;Ljava/util/List;Ljava/util/List;)V", "getErrors", "()Lai/zencoder/generated/client/jira/models/SimpleErrorCollection;", "getModifiedIssues", "()Ljava/util/List;", "getUnmodifiedIssues", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class RemoveOptionFromIssuesResult {
   @Nullable
   private final SimpleErrorCollection errors;
   @Nullable
   private final List<Long> modifiedIssues;
   @Nullable
   private final List<Long> unmodifiedIssues;

   public RemoveOptionFromIssuesResult(@Nullable SimpleErrorCollection errors, @Nullable List<Long> modifiedIssues, @Nullable List<Long> unmodifiedIssues) {
      this.errors = errors;
      this.modifiedIssues = modifiedIssues;
      this.unmodifiedIssues = unmodifiedIssues;
   }

   @JsonProperty("errors")
   @Nullable
   public final SimpleErrorCollection getErrors() {
      return this.errors;
   }

   @JsonProperty("modifiedIssues")
   @Nullable
   public final List<Long> getModifiedIssues() {
      return this.modifiedIssues;
   }

   @JsonProperty("unmodifiedIssues")
   @Nullable
   public final List<Long> getUnmodifiedIssues() {
      return this.unmodifiedIssues;
   }

   @Nullable
   public final SimpleErrorCollection component1() {
      return this.errors;
   }

   @Nullable
   public final List<Long> component2() {
      return this.modifiedIssues;
   }

   @Nullable
   public final List<Long> component3() {
      return this.unmodifiedIssues;
   }

   @NotNull
   public final RemoveOptionFromIssuesResult copy(@Nullable SimpleErrorCollection errors, @Nullable List<Long> modifiedIssues, @Nullable List<Long> unmodifiedIssues) {
      return new RemoveOptionFromIssuesResult(errors, modifiedIssues, unmodifiedIssues);
   }

   @NotNull
   public String toString() {
      return "RemoveOptionFromIssuesResult(errors=" + this.errors + ", modifiedIssues=" + this.modifiedIssues + ", unmodifiedIssues=" + this.unmodifiedIssues + ")";
   }

   public int hashCode() {
      int var1 = this.errors == null ? 0 : this.errors.hashCode();
      var1 = var1 * 31 + (this.modifiedIssues == null ? 0 : this.modifiedIssues.hashCode());
      var1 = var1 * 31 + (this.unmodifiedIssues == null ? 0 : this.unmodifiedIssues.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof RemoveOptionFromIssuesResult)) {
         return false;
      } else {
         RemoveOptionFromIssuesResult var2 = (RemoveOptionFromIssuesResult)other;
         if (!Intrinsics.areEqual(this.errors, var2.errors)) {
            return false;
         } else if (!Intrinsics.areEqual(this.modifiedIssues, var2.modifiedIssues)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.unmodifiedIssues, var2.unmodifiedIssues);
         }
      }
   }

   public RemoveOptionFromIssuesResult() {
      this((SimpleErrorCollection)null, (List)null, (List)null, 7, (DefaultConstructorMarker)null);
   }
}
