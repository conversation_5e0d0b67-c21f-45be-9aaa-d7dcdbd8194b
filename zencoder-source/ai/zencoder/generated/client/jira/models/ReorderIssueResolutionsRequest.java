package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J1\u0010\u0011\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00042\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0004H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0004H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\f\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ReorderIssueResolutionsRequest;", "", "ids", "", "", "after", "position", "<init>", "(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;)V", "getIds", "()Ljava/util/List;", "getAfter", "()Ljava/lang/String;", "getPosition", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class ReorderIssueResolutionsRequest {
   @NotNull
   private final List<String> ids;
   @Nullable
   private final String after;
   @Nullable
   private final String position;

   public ReorderIssueResolutionsRequest(@NotNull List<String> ids, @Nullable String after, @Nullable String position) {
      Intrinsics.checkNotNullParameter(ids, "");
      super();
      this.ids = ids;
      this.after = after;
      this.position = position;
   }

   @JsonProperty("ids")
   @NotNull
   public final List<String> getIds() {
      return this.ids;
   }

   @JsonProperty("after")
   @Nullable
   public final String getAfter() {
      return this.after;
   }

   @JsonProperty("position")
   @Nullable
   public final String getPosition() {
      return this.position;
   }

   @NotNull
   public final List<String> component1() {
      return this.ids;
   }

   @Nullable
   public final String component2() {
      return this.after;
   }

   @Nullable
   public final String component3() {
      return this.position;
   }

   @NotNull
   public final ReorderIssueResolutionsRequest copy(@NotNull List<String> ids, @Nullable String after, @Nullable String position) {
      Intrinsics.checkNotNullParameter(ids, "");
      return new ReorderIssueResolutionsRequest(ids, after, position);
   }

   @NotNull
   public String toString() {
      return "ReorderIssueResolutionsRequest(ids=" + this.ids + ", after=" + this.after + ", position=" + this.position + ")";
   }

   public int hashCode() {
      int var1 = this.ids.hashCode();
      var1 = var1 * 31 + (this.after == null ? 0 : this.after.hashCode());
      var1 = var1 * 31 + (this.position == null ? 0 : this.position.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ReorderIssueResolutionsRequest)) {
         return false;
      } else {
         ReorderIssueResolutionsRequest var2 = (ReorderIssueResolutionsRequest)other;
         if (!Intrinsics.areEqual(this.ids, var2.ids)) {
            return false;
         } else if (!Intrinsics.areEqual(this.after, var2.after)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.position, var2.position);
         }
      }
   }
}
