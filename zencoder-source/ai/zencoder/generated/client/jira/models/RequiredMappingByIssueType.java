package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\"\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005H\u00c6\u0003J'\u0010\u000e\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"},
   d2 = {"Lai/zencoder/generated/client/jira/models/RequiredMappingByIssueType;", "", "issueTypeId", "", "statusIds", "", "<init>", "(Ljava/lang/String;Ljava/util/Set;)V", "getIssueTypeId", "()Ljava/lang/String;", "getStatusIds", "()Ljava/util/Set;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class RequiredMappingByIssueType {
   @Nullable
   private final String issueTypeId;
   @Nullable
   private final Set<String> statusIds;

   public RequiredMappingByIssueType(@Nullable String issueTypeId, @Nullable Set<String> statusIds) {
      this.issueTypeId = issueTypeId;
      this.statusIds = statusIds;
   }

   @JsonProperty("issueTypeId")
   @Nullable
   public final String getIssueTypeId() {
      return this.issueTypeId;
   }

   @JsonProperty("statusIds")
   @Nullable
   public final Set<String> getStatusIds() {
      return this.statusIds;
   }

   @Nullable
   public final String component1() {
      return this.issueTypeId;
   }

   @Nullable
   public final Set<String> component2() {
      return this.statusIds;
   }

   @NotNull
   public final RequiredMappingByIssueType copy(@Nullable String issueTypeId, @Nullable Set<String> statusIds) {
      return new RequiredMappingByIssueType(issueTypeId, statusIds);
   }

   @NotNull
   public String toString() {
      return "RequiredMappingByIssueType(issueTypeId=" + this.issueTypeId + ", statusIds=" + this.statusIds + ")";
   }

   public int hashCode() {
      int var1 = this.issueTypeId == null ? 0 : this.issueTypeId.hashCode();
      var1 = var1 * 31 + (this.statusIds == null ? 0 : this.statusIds.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof RequiredMappingByIssueType)) {
         return false;
      } else {
         RequiredMappingByIssueType var2 = (RequiredMappingByIssueType)other;
         if (!Intrinsics.areEqual(this.issueTypeId, var2.issueTypeId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.statusIds, var2.statusIds);
         }
      }
   }

   public RequiredMappingByIssueType() {
      this((String)null, (Set)null, 3, (DefaultConstructorMarker)null);
   }
}
