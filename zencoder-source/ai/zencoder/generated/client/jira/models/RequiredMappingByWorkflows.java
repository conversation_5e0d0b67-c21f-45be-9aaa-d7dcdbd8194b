package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\"\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J3\u0010\u0011\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\n\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/RequiredMappingByWorkflows;", "", "sourceWorkflowId", "", "statusIds", "", "targetWorkflowId", "<init>", "(Ljava/lang/String;Ljava/util/Set;Ljava/lang/String;)V", "getSourceWorkflowId", "()Ljava/lang/String;", "getStatusIds", "()Ljava/util/Set;", "getTargetWorkflowId", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class RequiredMappingByWorkflows {
   @Nullable
   private final String sourceWorkflowId;
   @Nullable
   private final Set<String> statusIds;
   @Nullable
   private final String targetWorkflowId;

   public RequiredMappingByWorkflows(@Nullable String sourceWorkflowId, @Nullable Set<String> statusIds, @Nullable String targetWorkflowId) {
      this.sourceWorkflowId = sourceWorkflowId;
      this.statusIds = statusIds;
      this.targetWorkflowId = targetWorkflowId;
   }

   @JsonProperty("sourceWorkflowId")
   @Nullable
   public final String getSourceWorkflowId() {
      return this.sourceWorkflowId;
   }

   @JsonProperty("statusIds")
   @Nullable
   public final Set<String> getStatusIds() {
      return this.statusIds;
   }

   @JsonProperty("targetWorkflowId")
   @Nullable
   public final String getTargetWorkflowId() {
      return this.targetWorkflowId;
   }

   @Nullable
   public final String component1() {
      return this.sourceWorkflowId;
   }

   @Nullable
   public final Set<String> component2() {
      return this.statusIds;
   }

   @Nullable
   public final String component3() {
      return this.targetWorkflowId;
   }

   @NotNull
   public final RequiredMappingByWorkflows copy(@Nullable String sourceWorkflowId, @Nullable Set<String> statusIds, @Nullable String targetWorkflowId) {
      return new RequiredMappingByWorkflows(sourceWorkflowId, statusIds, targetWorkflowId);
   }

   @NotNull
   public String toString() {
      return "RequiredMappingByWorkflows(sourceWorkflowId=" + this.sourceWorkflowId + ", statusIds=" + this.statusIds + ", targetWorkflowId=" + this.targetWorkflowId + ")";
   }

   public int hashCode() {
      int var1 = this.sourceWorkflowId == null ? 0 : this.sourceWorkflowId.hashCode();
      var1 = var1 * 31 + (this.statusIds == null ? 0 : this.statusIds.hashCode());
      var1 = var1 * 31 + (this.targetWorkflowId == null ? 0 : this.targetWorkflowId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof RequiredMappingByWorkflows)) {
         return false;
      } else {
         RequiredMappingByWorkflows var2 = (RequiredMappingByWorkflows)other;
         if (!Intrinsics.areEqual(this.sourceWorkflowId, var2.sourceWorkflowId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.statusIds, var2.statusIds)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.targetWorkflowId, var2.targetWorkflowId);
         }
      }
   }

   public RequiredMappingByWorkflows() {
      this((String)null, (Set)null, (String)null, 7, (DefaultConstructorMarker)null);
   }
}
