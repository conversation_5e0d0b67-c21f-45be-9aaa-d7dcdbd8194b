package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u001f\b\u0086\b\u0018\u00002\u00020\u0001By\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u000e\u0018\u00010\u0005\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010 \u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010!\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0016J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010#\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001aJ\u000b\u0010$\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010%\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010&\u001a\n\u0012\u0004\u0012\u00020\u000e\u0018\u00010\u0005H\u00c6\u0003J\u0080\u0001\u0010'\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00052\u0010\b\u0002\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u000e\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010(J\u0013\u0010)\u001a\u00020\u00072\b\u0010*\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010+\u001a\u00020\nH\u00d6\u0001J\t\u0010,\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0017\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u0017\u001a\u0004\b\u0015\u0010\u0016R\u0015\u0010\b\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0012R\u0017\u0010\t\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\n\n\u0002\u0010\u001b\u001a\u0004\b\u0019\u0010\u001aR\u0015\u0010\u000b\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0012R\u001b\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0014R\u001b\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u000e\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0014\u00a8\u0006-"},
   d2 = {"Lai/zencoder/generated/client/jira/models/SearchAndReconcileRequestBean;", "", "expand", "", "fields", "", "fieldsByKeys", "", "jql", "maxResults", "", "nextPageToken", "properties", "reconcileIssues", "", "<init>", "(Ljava/lang/String;Ljava/util/List;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V", "getExpand", "()Ljava/lang/String;", "getFields", "()Ljava/util/List;", "getFieldsByKeys", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getJql", "getMaxResults", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getNextPageToken", "getProperties", "getReconcileIssues", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "(Ljava/lang/String;Ljava/util/List;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)Lai/zencoder/generated/client/jira/models/SearchAndReconcileRequestBean;", "equals", "other", "hashCode", "toString", "zencoder"}
)
public final class SearchAndReconcileRequestBean {
   @Nullable
   private final String expand;
   @Nullable
   private final List<String> fields;
   @Nullable
   private final Boolean fieldsByKeys;
   @Nullable
   private final String jql;
   @Nullable
   private final Integer maxResults;
   @Nullable
   private final String nextPageToken;
   @Nullable
   private final List<String> properties;
   @Nullable
   private final List<Long> reconcileIssues;

   public SearchAndReconcileRequestBean(@Nullable String expand, @Nullable List<String> fields, @Nullable Boolean fieldsByKeys, @Nullable String jql, @Nullable Integer maxResults, @Nullable String nextPageToken, @Nullable List<String> properties, @Nullable List<Long> reconcileIssues) {
      this.expand = expand;
      this.fields = fields;
      this.fieldsByKeys = fieldsByKeys;
      this.jql = jql;
      this.maxResults = maxResults;
      this.nextPageToken = nextPageToken;
      this.properties = properties;
      this.reconcileIssues = reconcileIssues;
   }

   @JsonProperty("expand")
   @Nullable
   public final String getExpand() {
      return this.expand;
   }

   @JsonProperty("fields")
   @Nullable
   public final List<String> getFields() {
      return this.fields;
   }

   @JsonProperty("fieldsByKeys")
   @Nullable
   public final Boolean getFieldsByKeys() {
      return this.fieldsByKeys;
   }

   @JsonProperty("jql")
   @Nullable
   public final String getJql() {
      return this.jql;
   }

   @JsonProperty("maxResults")
   @Nullable
   public final Integer getMaxResults() {
      return this.maxResults;
   }

   @JsonProperty("nextPageToken")
   @Nullable
   public final String getNextPageToken() {
      return this.nextPageToken;
   }

   @JsonProperty("properties")
   @Nullable
   public final List<String> getProperties() {
      return this.properties;
   }

   @JsonProperty("reconcileIssues")
   @Nullable
   public final List<Long> getReconcileIssues() {
      return this.reconcileIssues;
   }

   @Nullable
   public final String component1() {
      return this.expand;
   }

   @Nullable
   public final List<String> component2() {
      return this.fields;
   }

   @Nullable
   public final Boolean component3() {
      return this.fieldsByKeys;
   }

   @Nullable
   public final String component4() {
      return this.jql;
   }

   @Nullable
   public final Integer component5() {
      return this.maxResults;
   }

   @Nullable
   public final String component6() {
      return this.nextPageToken;
   }

   @Nullable
   public final List<String> component7() {
      return this.properties;
   }

   @Nullable
   public final List<Long> component8() {
      return this.reconcileIssues;
   }

   @NotNull
   public final SearchAndReconcileRequestBean copy(@Nullable String expand, @Nullable List<String> fields, @Nullable Boolean fieldsByKeys, @Nullable String jql, @Nullable Integer maxResults, @Nullable String nextPageToken, @Nullable List<String> properties, @Nullable List<Long> reconcileIssues) {
      return new SearchAndReconcileRequestBean(expand, fields, fieldsByKeys, jql, maxResults, nextPageToken, properties, reconcileIssues);
   }

   @NotNull
   public String toString() {
      return "SearchAndReconcileRequestBean(expand=" + this.expand + ", fields=" + this.fields + ", fieldsByKeys=" + this.fieldsByKeys + ", jql=" + this.jql + ", maxResults=" + this.maxResults + ", nextPageToken=" + this.nextPageToken + ", properties=" + this.properties + ", reconcileIssues=" + this.reconcileIssues + ")";
   }

   public int hashCode() {
      int var1 = this.expand == null ? 0 : this.expand.hashCode();
      var1 = var1 * 31 + (this.fields == null ? 0 : this.fields.hashCode());
      var1 = var1 * 31 + (this.fieldsByKeys == null ? 0 : this.fieldsByKeys.hashCode());
      var1 = var1 * 31 + (this.jql == null ? 0 : this.jql.hashCode());
      var1 = var1 * 31 + (this.maxResults == null ? 0 : this.maxResults.hashCode());
      var1 = var1 * 31 + (this.nextPageToken == null ? 0 : this.nextPageToken.hashCode());
      var1 = var1 * 31 + (this.properties == null ? 0 : this.properties.hashCode());
      var1 = var1 * 31 + (this.reconcileIssues == null ? 0 : this.reconcileIssues.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof SearchAndReconcileRequestBean)) {
         return false;
      } else {
         SearchAndReconcileRequestBean var2 = (SearchAndReconcileRequestBean)other;
         if (!Intrinsics.areEqual(this.expand, var2.expand)) {
            return false;
         } else if (!Intrinsics.areEqual(this.fields, var2.fields)) {
            return false;
         } else if (!Intrinsics.areEqual(this.fieldsByKeys, var2.fieldsByKeys)) {
            return false;
         } else if (!Intrinsics.areEqual(this.jql, var2.jql)) {
            return false;
         } else if (!Intrinsics.areEqual(this.maxResults, var2.maxResults)) {
            return false;
         } else if (!Intrinsics.areEqual(this.nextPageToken, var2.nextPageToken)) {
            return false;
         } else if (!Intrinsics.areEqual(this.properties, var2.properties)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.reconcileIssues, var2.reconcileIssues);
         }
      }
   }

   public SearchAndReconcileRequestBean() {
      this((String)null, (List)null, (Boolean)null, (String)null, (Integer)null, (String)null, (List)null, (List)null, 255, (DefaultConstructorMarker)null);
   }
}
