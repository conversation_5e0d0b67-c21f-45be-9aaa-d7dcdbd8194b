package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BU\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\u0016\b\u0002\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0007\u0012\u0016\b\u0002\u0010\t\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\n\u0018\u00010\u0006\u00a2\u0006\u0004\b\u000b\u0010\fJ\u0011\u0010\u0014\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0017\u0010\u0015\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u0017\u0010\u0017\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\n\u0018\u00010\u0006H\u00c6\u0003JW\u0010\u0018\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\u0016\b\u0002\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00062\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00072\u0016\b\u0002\u0010\t\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\n\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u0007H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR!\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0015\u0010\b\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R!\u0010\t\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\n\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010\u00a8\u0006\u001f"},
   d2 = {"Lai/zencoder/generated/client/jira/models/SearchAndReconcileResults;", "", "issues", "", "Lai/zencoder/generated/client/jira/models/IssueBean;", "names", "", "", "nextPageToken", "schema", "Lai/zencoder/generated/client/jira/models/JsonTypeBean;", "<init>", "(Ljava/util/List;Ljava/util/Map;Ljava/lang/String;Ljava/util/Map;)V", "getIssues", "()Ljava/util/List;", "getNames", "()Ljava/util/Map;", "getNextPageToken", "()Ljava/lang/String;", "getSchema", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class SearchAndReconcileResults {
   @Nullable
   private final List<IssueBean> issues;
   @Nullable
   private final Map<String, String> names;
   @Nullable
   private final String nextPageToken;
   @Nullable
   private final Map<String, JsonTypeBean> schema;

   public SearchAndReconcileResults(@Nullable List<IssueBean> issues, @Nullable Map<String, String> names, @Nullable String nextPageToken, @Nullable Map<String, JsonTypeBean> schema) {
      this.issues = issues;
      this.names = names;
      this.nextPageToken = nextPageToken;
      this.schema = schema;
   }

   @JsonProperty("issues")
   @Nullable
   public final List<IssueBean> getIssues() {
      return this.issues;
   }

   @JsonProperty("names")
   @Nullable
   public final Map<String, String> getNames() {
      return this.names;
   }

   @JsonProperty("nextPageToken")
   @Nullable
   public final String getNextPageToken() {
      return this.nextPageToken;
   }

   @JsonProperty("schema")
   @Nullable
   public final Map<String, JsonTypeBean> getSchema() {
      return this.schema;
   }

   @Nullable
   public final List<IssueBean> component1() {
      return this.issues;
   }

   @Nullable
   public final Map<String, String> component2() {
      return this.names;
   }

   @Nullable
   public final String component3() {
      return this.nextPageToken;
   }

   @Nullable
   public final Map<String, JsonTypeBean> component4() {
      return this.schema;
   }

   @NotNull
   public final SearchAndReconcileResults copy(@Nullable List<IssueBean> issues, @Nullable Map<String, String> names, @Nullable String nextPageToken, @Nullable Map<String, JsonTypeBean> schema) {
      return new SearchAndReconcileResults(issues, names, nextPageToken, schema);
   }

   @NotNull
   public String toString() {
      return "SearchAndReconcileResults(issues=" + this.issues + ", names=" + this.names + ", nextPageToken=" + this.nextPageToken + ", schema=" + this.schema + ")";
   }

   public int hashCode() {
      int var1 = this.issues == null ? 0 : this.issues.hashCode();
      var1 = var1 * 31 + (this.names == null ? 0 : this.names.hashCode());
      var1 = var1 * 31 + (this.nextPageToken == null ? 0 : this.nextPageToken.hashCode());
      var1 = var1 * 31 + (this.schema == null ? 0 : this.schema.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof SearchAndReconcileResults)) {
         return false;
      } else {
         SearchAndReconcileResults var2 = (SearchAndReconcileResults)other;
         if (!Intrinsics.areEqual(this.issues, var2.issues)) {
            return false;
         } else if (!Intrinsics.areEqual(this.names, var2.names)) {
            return false;
         } else if (!Intrinsics.areEqual(this.nextPageToken, var2.nextPageToken)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.schema, var2.schema);
         }
      }
   }

   public SearchAndReconcileResults() {
      this((List)null, (Map)null, (String)null, (Map)null, 15, (DefaultConstructorMarker)null);
   }
}
