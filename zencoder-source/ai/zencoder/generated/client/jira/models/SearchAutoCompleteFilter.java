package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0010\t\n\u0002\b\u000e\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\nJ\u0011\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J,\u0010\u0010\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0011J\u0013\u0010\u0012\u001a\u00020\u00032\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000b\u001a\u0004\b\t\u0010\nR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/SearchAutoCompleteFilter;", "", "includeCollapsedFields", "", "projectIds", "", "", "<init>", "(Ljava/lang/Boolean;Ljava/util/List;)V", "getIncludeCollapsedFields", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getProjectIds", "()Ljava/util/List;", "component1", "component2", "copy", "(Ljava/lang/Boolean;Ljava/util/List;)Lai/zencoder/generated/client/jira/models/SearchAutoCompleteFilter;", "equals", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class SearchAutoCompleteFilter {
   @Nullable
   private final Boolean includeCollapsedFields;
   @Nullable
   private final List<Long> projectIds;

   public SearchAutoCompleteFilter(@Nullable Boolean includeCollapsedFields, @Nullable List<Long> projectIds) {
      this.includeCollapsedFields = includeCollapsedFields;
      this.projectIds = projectIds;
   }

   @JsonProperty("includeCollapsedFields")
   @Nullable
   public final Boolean getIncludeCollapsedFields() {
      return this.includeCollapsedFields;
   }

   @JsonProperty("projectIds")
   @Nullable
   public final List<Long> getProjectIds() {
      return this.projectIds;
   }

   @Nullable
   public final Boolean component1() {
      return this.includeCollapsedFields;
   }

   @Nullable
   public final List<Long> component2() {
      return this.projectIds;
   }

   @NotNull
   public final SearchAutoCompleteFilter copy(@Nullable Boolean includeCollapsedFields, @Nullable List<Long> projectIds) {
      return new SearchAutoCompleteFilter(includeCollapsedFields, projectIds);
   }

   @NotNull
   public String toString() {
      return "SearchAutoCompleteFilter(includeCollapsedFields=" + this.includeCollapsedFields + ", projectIds=" + this.projectIds + ")";
   }

   public int hashCode() {
      int var1 = this.includeCollapsedFields == null ? 0 : this.includeCollapsedFields.hashCode();
      var1 = var1 * 31 + (this.projectIds == null ? 0 : this.projectIds.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof SearchAutoCompleteFilter)) {
         return false;
      } else {
         SearchAutoCompleteFilter var2 = (SearchAutoCompleteFilter)other;
         if (!Intrinsics.areEqual(this.includeCollapsedFields, var2.includeCollapsedFields)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.projectIds, var2.projectIds);
         }
      }
   }

   public SearchAutoCompleteFilter() {
      this((Boolean)null, (List)null, 3, (DefaultConstructorMarker)null);
   }
}
