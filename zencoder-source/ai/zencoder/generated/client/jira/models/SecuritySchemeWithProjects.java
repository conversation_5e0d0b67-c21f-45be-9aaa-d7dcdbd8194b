package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0005\n\u0002\u0010\"\n\u0002\b\u0016\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004BM\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0002\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0002\u0012\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\f\u00a2\u0006\u0004\b\r\u0010\u000eJ\t\u0010\u001a\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0002H\u00c6\u0003J\u0010\u0010\u001c\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0014J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u0011\u0010\u001f\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\fH\u00c6\u0003JX\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00022\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00022\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\fH\u00c6\u0001\u00a2\u0006\u0002\u0010!J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010'\u001a\u00020\u0002H\u00d6\u0001R\u0013\u0010\u0005\u001a\u00020\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\u0007\u001a\u00020\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0017\u0010\b\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\n\n\u0002\u0010\u0015\u001a\u0004\b\u0013\u0010\u0014R\u0015\u0010\t\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0012R\u0015\u0010\n\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0012R\u001b\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\f8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019\u00a8\u0006("},
   d2 = {"Lai/zencoder/generated/client/jira/models/SecuritySchemeWithProjects;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "id", "", "self", "defaultLevel", "description", "NAME", "projectIds", "", "<init>", "(JLjava/lang/String;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;Ljava/util/Set;)V", "getId", "()J", "getSelf", "()Ljava/lang/String;", "getDefaultLevel", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getDescription", "getNAME", "getProjectIds", "()Ljava/util/Set;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "(JLjava/lang/String;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;Ljava/util/Set;)Lai/zencoder/generated/client/jira/models/SecuritySchemeWithProjects;", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class SecuritySchemeWithProjects extends HashMap<String, Object> {
   private final long id;
   @NotNull
   private final String self;
   @Nullable
   private final Long defaultLevel;
   @Nullable
   private final String description;
   @Nullable
   private final String NAME;
   @Nullable
   private final Set<Long> projectIds;

   public SecuritySchemeWithProjects(long id, @NotNull String self, @Nullable Long defaultLevel, @Nullable String description, @Nullable String NAME, @Nullable Set<Long> projectIds) {
      Intrinsics.checkNotNullParameter(self, "");
      super();
      this.id = id;
      this.self = self;
      this.defaultLevel = defaultLevel;
      this.description = description;
      this.NAME = NAME;
      this.projectIds = projectIds;
   }

   @JsonProperty("id")
   public final long getId() {
      return this.id;
   }

   @JsonProperty("self")
   @NotNull
   public final String getSelf() {
      return this.self;
   }

   @JsonProperty("defaultLevel")
   @Nullable
   public final Long getDefaultLevel() {
      return this.defaultLevel;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("projectIds")
   @Nullable
   public final Set<Long> getProjectIds() {
      return this.projectIds;
   }

   public final long component1() {
      return this.id;
   }

   @NotNull
   public final String component2() {
      return this.self;
   }

   @Nullable
   public final Long component3() {
      return this.defaultLevel;
   }

   @Nullable
   public final String component4() {
      return this.description;
   }

   @Nullable
   public final String component5() {
      return this.NAME;
   }

   @Nullable
   public final Set<Long> component6() {
      return this.projectIds;
   }

   @NotNull
   public final SecuritySchemeWithProjects copy(long id, @NotNull String self, @Nullable Long defaultLevel, @Nullable String description, @Nullable String NAME, @Nullable Set<Long> projectIds) {
      Intrinsics.checkNotNullParameter(self, "");
      return new SecuritySchemeWithProjects(id, self, defaultLevel, description, NAME, projectIds);
   }

   @NotNull
   public String toString() {
      return "SecuritySchemeWithProjects(id=" + this.id + ", self=" + this.self + ", defaultLevel=" + this.defaultLevel + ", description=" + this.description + ", NAME=" + this.NAME + ", projectIds=" + this.projectIds + ")";
   }

   public int hashCode() {
      int var1 = Long.hashCode(this.id);
      var1 = var1 * 31 + this.self.hashCode();
      var1 = var1 * 31 + (this.defaultLevel == null ? 0 : this.defaultLevel.hashCode());
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.projectIds == null ? 0 : this.projectIds.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof SecuritySchemeWithProjects)) {
         return false;
      } else {
         SecuritySchemeWithProjects var2 = (SecuritySchemeWithProjects)other;
         if (this.id != var2.id) {
            return false;
         } else if (!Intrinsics.areEqual(this.self, var2.self)) {
            return false;
         } else if (!Intrinsics.areEqual(this.defaultLevel, var2.defaultLevel)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.projectIds, var2.projectIds);
         }
      }
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
