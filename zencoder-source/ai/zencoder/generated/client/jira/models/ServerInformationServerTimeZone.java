package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\b\u0010\tJ\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ>\u0010\u0015\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0016J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\f\u0010\rR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000bR\u0017\u0010\u0007\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\u0010\u0010\r\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ServerInformationServerTimeZone;", "", "displayName", "", "dstsavings", "", "id", "rawOffset", "<init>", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;)V", "getDisplayName", "()Ljava/lang/String;", "getDstsavings", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getId", "getRawOffset", "component1", "component2", "component3", "component4", "copy", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;)Lai/zencoder/generated/client/jira/models/ServerInformationServerTimeZone;", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class ServerInformationServerTimeZone {
   @Nullable
   private final String displayName;
   @Nullable
   private final Integer dstsavings;
   @Nullable
   private final String id;
   @Nullable
   private final Integer rawOffset;

   public ServerInformationServerTimeZone(@Nullable String displayName, @Nullable Integer dstsavings, @Nullable String id, @Nullable Integer rawOffset) {
      this.displayName = displayName;
      this.dstsavings = dstsavings;
      this.id = id;
      this.rawOffset = rawOffset;
   }

   @JsonProperty("displayName")
   @Nullable
   public final String getDisplayName() {
      return this.displayName;
   }

   @JsonProperty("dstsavings")
   @Nullable
   public final Integer getDstsavings() {
      return this.dstsavings;
   }

   @JsonProperty("id")
   @Nullable
   public final String getId() {
      return this.id;
   }

   @JsonProperty("rawOffset")
   @Nullable
   public final Integer getRawOffset() {
      return this.rawOffset;
   }

   @Nullable
   public final String component1() {
      return this.displayName;
   }

   @Nullable
   public final Integer component2() {
      return this.dstsavings;
   }

   @Nullable
   public final String component3() {
      return this.id;
   }

   @Nullable
   public final Integer component4() {
      return this.rawOffset;
   }

   @NotNull
   public final ServerInformationServerTimeZone copy(@Nullable String displayName, @Nullable Integer dstsavings, @Nullable String id, @Nullable Integer rawOffset) {
      return new ServerInformationServerTimeZone(displayName, dstsavings, id, rawOffset);
   }

   @NotNull
   public String toString() {
      return "ServerInformationServerTimeZone(displayName=" + this.displayName + ", dstsavings=" + this.dstsavings + ", id=" + this.id + ", rawOffset=" + this.rawOffset + ")";
   }

   public int hashCode() {
      int var1 = this.displayName == null ? 0 : this.displayName.hashCode();
      var1 = var1 * 31 + (this.dstsavings == null ? 0 : this.dstsavings.hashCode());
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      var1 = var1 * 31 + (this.rawOffset == null ? 0 : this.rawOffset.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ServerInformationServerTimeZone)) {
         return false;
      } else {
         ServerInformationServerTimeZone var2 = (ServerInformationServerTimeZone)other;
         if (!Intrinsics.areEqual(this.displayName, var2.displayName)) {
            return false;
         } else if (!Intrinsics.areEqual(this.dstsavings, var2.dstsavings)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.rawOffset, var2.rawOffset);
         }
      }
   }

   public ServerInformationServerTimeZone() {
      this((String)null, (Integer)null, (String)null, (Integer)null, 15, (DefaultConstructorMarker)null);
   }
}
