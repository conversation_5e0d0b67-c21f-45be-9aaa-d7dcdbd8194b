package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\fJ\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J2\u0010\u0012\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0013J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\r\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\n\u00a8\u0006\u001a"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ServiceManagementNavigationInfo;", "", "queueCategory", "", "queueId", "", "queueName", "<init>", "(Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;)V", "getQueueCategory", "()Ljava/lang/String;", "getQueueId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getQueueName", "component1", "component2", "component3", "copy", "(Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/ServiceManagementNavigationInfo;", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class ServiceManagementNavigationInfo {
   @Nullable
   private final String queueCategory;
   @Nullable
   private final Long queueId;
   @Nullable
   private final String queueName;

   public ServiceManagementNavigationInfo(@Nullable String queueCategory, @Nullable Long queueId, @Nullable String queueName) {
      this.queueCategory = queueCategory;
      this.queueId = queueId;
      this.queueName = queueName;
   }

   @JsonProperty("queueCategory")
   @Nullable
   public final String getQueueCategory() {
      return this.queueCategory;
   }

   @JsonProperty("queueId")
   @Nullable
   public final Long getQueueId() {
      return this.queueId;
   }

   @JsonProperty("queueName")
   @Nullable
   public final String getQueueName() {
      return this.queueName;
   }

   @Nullable
   public final String component1() {
      return this.queueCategory;
   }

   @Nullable
   public final Long component2() {
      return this.queueId;
   }

   @Nullable
   public final String component3() {
      return this.queueName;
   }

   @NotNull
   public final ServiceManagementNavigationInfo copy(@Nullable String queueCategory, @Nullable Long queueId, @Nullable String queueName) {
      return new ServiceManagementNavigationInfo(queueCategory, queueId, queueName);
   }

   @NotNull
   public String toString() {
      return "ServiceManagementNavigationInfo(queueCategory=" + this.queueCategory + ", queueId=" + this.queueId + ", queueName=" + this.queueName + ")";
   }

   public int hashCode() {
      int var1 = this.queueCategory == null ? 0 : this.queueCategory.hashCode();
      var1 = var1 * 31 + (this.queueId == null ? 0 : this.queueId.hashCode());
      var1 = var1 * 31 + (this.queueName == null ? 0 : this.queueName.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ServiceManagementNavigationInfo)) {
         return false;
      } else {
         ServiceManagementNavigationInfo var2 = (ServiceManagementNavigationInfo)other;
         if (!Intrinsics.areEqual(this.queueCategory, var2.queueCategory)) {
            return false;
         } else if (!Intrinsics.areEqual(this.queueId, var2.queueId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.queueName, var2.queueName);
         }
      }
   }

   public ServiceManagementNavigationInfo() {
      this((String)null, (Long)null, (String)null, 7, (DefaultConstructorMarker)null);
   }
}
