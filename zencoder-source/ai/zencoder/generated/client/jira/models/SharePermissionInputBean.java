package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0017\n\u0002\u0010\u000b\n\u0002\b\u0005\b\u0086\b\u0018\u00002\u00020\u0001:\u0001'BW\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\b\f\u0010\rJ\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010\u001f\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0017J`\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000bH\u00c6\u0001\u00a2\u0006\u0002\u0010!J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020\u000bH\u00d6\u0001J\t\u0010&\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0015\u0010\b\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u0015\u0010\t\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0011R\u0017\u0010\n\u001a\u0004\u0018\u00010\u000b8G\u00a2\u0006\n\n\u0002\u0010\u0018\u001a\u0004\b\u0016\u0010\u0017\u00a8\u0006("},
   d2 = {"Lai/zencoder/generated/client/jira/models/SharePermissionInputBean;", "", "type", "Lai/zencoder/generated/client/jira/models/SharePermissionInputBean$Type;", "accountId", "", "groupId", "groupname", "projectId", "projectRoleId", "rights", "", "<init>", "(Lai/zencoder/generated/client/jira/models/SharePermissionInputBean$Type;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)V", "getType", "()Lai/zencoder/generated/client/jira/models/SharePermissionInputBean$Type;", "getAccountId", "()Ljava/lang/String;", "getGroupId", "getGroupname", "getProjectId", "getProjectRoleId", "getRights", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "(Lai/zencoder/generated/client/jira/models/SharePermissionInputBean$Type;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Lai/zencoder/generated/client/jira/models/SharePermissionInputBean;", "equals", "", "other", "hashCode", "toString", "Type", "zencoder"}
)
public final class SharePermissionInputBean {
   @NotNull
   private final Type type;
   @Nullable
   private final String accountId;
   @Nullable
   private final String groupId;
   @Nullable
   private final String groupname;
   @Nullable
   private final String projectId;
   @Nullable
   private final String projectRoleId;
   @Nullable
   private final Integer rights;

   public SharePermissionInputBean(@NotNull Type type, @Nullable String accountId, @Nullable String groupId, @Nullable String groupname, @Nullable String projectId, @Nullable String projectRoleId, @Nullable Integer rights) {
      Intrinsics.checkNotNullParameter(type, "");
      super();
      this.type = type;
      this.accountId = accountId;
      this.groupId = groupId;
      this.groupname = groupname;
      this.projectId = projectId;
      this.projectRoleId = projectRoleId;
      this.rights = rights;
   }

   @JsonProperty("type")
   @NotNull
   public final Type getType() {
      return this.type;
   }

   @JsonProperty("accountId")
   @Nullable
   public final String getAccountId() {
      return this.accountId;
   }

   @JsonProperty("groupId")
   @Nullable
   public final String getGroupId() {
      return this.groupId;
   }

   @JsonProperty("groupname")
   @Nullable
   public final String getGroupname() {
      return this.groupname;
   }

   @JsonProperty("projectId")
   @Nullable
   public final String getProjectId() {
      return this.projectId;
   }

   @JsonProperty("projectRoleId")
   @Nullable
   public final String getProjectRoleId() {
      return this.projectRoleId;
   }

   @JsonProperty("rights")
   @Nullable
   public final Integer getRights() {
      return this.rights;
   }

   @NotNull
   public final Type component1() {
      return this.type;
   }

   @Nullable
   public final String component2() {
      return this.accountId;
   }

   @Nullable
   public final String component3() {
      return this.groupId;
   }

   @Nullable
   public final String component4() {
      return this.groupname;
   }

   @Nullable
   public final String component5() {
      return this.projectId;
   }

   @Nullable
   public final String component6() {
      return this.projectRoleId;
   }

   @Nullable
   public final Integer component7() {
      return this.rights;
   }

   @NotNull
   public final SharePermissionInputBean copy(@NotNull Type type, @Nullable String accountId, @Nullable String groupId, @Nullable String groupname, @Nullable String projectId, @Nullable String projectRoleId, @Nullable Integer rights) {
      Intrinsics.checkNotNullParameter(type, "");
      return new SharePermissionInputBean(type, accountId, groupId, groupname, projectId, projectRoleId, rights);
   }

   @NotNull
   public String toString() {
      return "SharePermissionInputBean(type=" + this.type + ", accountId=" + this.accountId + ", groupId=" + this.groupId + ", groupname=" + this.groupname + ", projectId=" + this.projectId + ", projectRoleId=" + this.projectRoleId + ", rights=" + this.rights + ")";
   }

   public int hashCode() {
      int var1 = this.type.hashCode();
      var1 = var1 * 31 + (this.accountId == null ? 0 : this.accountId.hashCode());
      var1 = var1 * 31 + (this.groupId == null ? 0 : this.groupId.hashCode());
      var1 = var1 * 31 + (this.groupname == null ? 0 : this.groupname.hashCode());
      var1 = var1 * 31 + (this.projectId == null ? 0 : this.projectId.hashCode());
      var1 = var1 * 31 + (this.projectRoleId == null ? 0 : this.projectRoleId.hashCode());
      var1 = var1 * 31 + (this.rights == null ? 0 : this.rights.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof SharePermissionInputBean)) {
         return false;
      } else {
         SharePermissionInputBean var2 = (SharePermissionInputBean)other;
         if (this.type != var2.type) {
            return false;
         } else if (!Intrinsics.areEqual(this.accountId, var2.accountId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.groupId, var2.groupId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.groupname, var2.groupname)) {
            return false;
         } else if (!Intrinsics.areEqual(this.projectId, var2.projectId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.projectRoleId, var2.projectRoleId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.rights, var2.rights);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\r\u00a8\u0006\u000e"},
      d2 = {"Lai/zencoder/generated/client/jira/models/SharePermissionInputBean$Type;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "user", "project", "group", "projectRole", "global", "authenticated", "zencoder"}
   )
   public static enum Type {
      @NotNull
      private final String value;
      @JsonProperty("user")
      user("user"),
      @JsonProperty("project")
      project("project"),
      @JsonProperty("group")
      group("group"),
      @JsonProperty("projectRole")
      projectRole("projectRole"),
      @JsonProperty("global")
      global("global"),
      @JsonProperty("authenticated")
      authenticated("authenticated");

      private Type(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Type> getEntries() {
         return $ENTRIES;
      }
   }
}
