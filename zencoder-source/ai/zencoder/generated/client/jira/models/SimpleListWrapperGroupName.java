package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BI\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0001\u0012\u0010\b\u0002\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0004\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0001\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\n\u0010\u000bJ\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J\u0011\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u0004H\u00c6\u0003J\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0011J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0011JP\u0010\u001a\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00012\u0010\b\u0002\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00042\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010\u001bJ\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020\u0007H\u00d6\u0001J\t\u0010 \u001a\u00020!H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001b\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0010\u0010\u0011R\u0015\u0010\b\u001a\u0004\u0018\u00010\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\rR\u0017\u0010\t\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0014\u0010\u0011\u00a8\u0006\""},
   d2 = {"Lai/zencoder/generated/client/jira/models/SimpleListWrapperGroupName;", "", "callback", "items", "", "Lai/zencoder/generated/client/jira/models/GroupName;", "maxResults", "", "pagingCallback", "propertySize", "<init>", "(Ljava/lang/Object;Ljava/util/List;Ljava/lang/Integer;Ljava/lang/Object;Ljava/lang/Integer;)V", "getCallback", "()Ljava/lang/Object;", "getItems", "()Ljava/util/List;", "getMaxResults", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getPagingCallback", "getPropertySize", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/lang/Object;Ljava/util/List;Ljava/lang/Integer;Ljava/lang/Object;Ljava/lang/Integer;)Lai/zencoder/generated/client/jira/models/SimpleListWrapperGroupName;", "equals", "", "other", "hashCode", "toString", "", "zencoder"}
)
public final class SimpleListWrapperGroupName {
   @Nullable
   private final Object callback;
   @Nullable
   private final List<GroupName> items;
   @Nullable
   private final Integer maxResults;
   @Nullable
   private final Object pagingCallback;
   @Nullable
   private final Integer propertySize;

   public SimpleListWrapperGroupName(@Nullable Object callback, @Nullable List<GroupName> items, @Nullable Integer maxResults, @Nullable Object pagingCallback, @Nullable Integer propertySize) {
      this.callback = callback;
      this.items = items;
      this.maxResults = maxResults;
      this.pagingCallback = pagingCallback;
      this.propertySize = propertySize;
   }

   @JsonProperty("callback")
   @Nullable
   public final Object getCallback() {
      return this.callback;
   }

   @JsonProperty("items")
   @Nullable
   public final List<GroupName> getItems() {
      return this.items;
   }

   @JsonProperty("max-results")
   @Nullable
   public final Integer getMaxResults() {
      return this.maxResults;
   }

   @JsonProperty("pagingCallback")
   @Nullable
   public final Object getPagingCallback() {
      return this.pagingCallback;
   }

   @JsonProperty("size")
   @Nullable
   public final Integer getPropertySize() {
      return this.propertySize;
   }

   @Nullable
   public final Object component1() {
      return this.callback;
   }

   @Nullable
   public final List<GroupName> component2() {
      return this.items;
   }

   @Nullable
   public final Integer component3() {
      return this.maxResults;
   }

   @Nullable
   public final Object component4() {
      return this.pagingCallback;
   }

   @Nullable
   public final Integer component5() {
      return this.propertySize;
   }

   @NotNull
   public final SimpleListWrapperGroupName copy(@Nullable Object callback, @Nullable List<GroupName> items, @Nullable Integer maxResults, @Nullable Object pagingCallback, @Nullable Integer propertySize) {
      return new SimpleListWrapperGroupName(callback, items, maxResults, pagingCallback, propertySize);
   }

   @NotNull
   public String toString() {
      return "SimpleListWrapperGroupName(callback=" + this.callback + ", items=" + this.items + ", maxResults=" + this.maxResults + ", pagingCallback=" + this.pagingCallback + ", propertySize=" + this.propertySize + ")";
   }

   public int hashCode() {
      int var1 = this.callback == null ? 0 : this.callback.hashCode();
      var1 = var1 * 31 + (this.items == null ? 0 : this.items.hashCode());
      var1 = var1 * 31 + (this.maxResults == null ? 0 : this.maxResults.hashCode());
      var1 = var1 * 31 + (this.pagingCallback == null ? 0 : this.pagingCallback.hashCode());
      var1 = var1 * 31 + (this.propertySize == null ? 0 : this.propertySize.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof SimpleListWrapperGroupName)) {
         return false;
      } else {
         SimpleListWrapperGroupName var2 = (SimpleListWrapperGroupName)other;
         if (!Intrinsics.areEqual(this.callback, var2.callback)) {
            return false;
         } else if (!Intrinsics.areEqual(this.items, var2.items)) {
            return false;
         } else if (!Intrinsics.areEqual(this.maxResults, var2.maxResults)) {
            return false;
         } else if (!Intrinsics.areEqual(this.pagingCallback, var2.pagingCallback)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.propertySize, var2.propertySize);
         }
      }
   }

   public SimpleListWrapperGroupName() {
      this((Object)null, (List)null, (Integer)null, (Object)null, (Integer)null, 31, (DefaultConstructorMarker)null);
   }
}
