package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u001f\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001By\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0010\u0010\u0011J\u0010\u0010\"\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010#\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u000b\u0010$\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u0010\u0010%\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0019J\u0010\u0010&\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u0011\u0010'\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u000bH\u00c6\u0003J\u0010\u0010(\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0019J\u000b\u0010)\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\u0010\u0010*\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u0080\u0001\u0010+\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010,J\u0013\u0010-\u001a\u00020.2\b\u0010/\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00100\u001a\u00020\bH\u00d6\u0001J\t\u00101\u001a\u00020\u000eH\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b\u0012\u0010\u0013R\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b\u0015\u0010\u0013R\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0017\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u0018\u0010\u0019R\u0017\u0010\t\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b\u001b\u0010\u0013R\u001b\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u000b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0017\u0010\f\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u001e\u0010\u0019R\u0015\u0010\r\u001a\u0004\u0018\u00010\u000e8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0017\u0010\u000f\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b!\u0010\u0013\u00a8\u00062"},
   d2 = {"Lai/zencoder/generated/client/jira/models/SimplifiedHierarchyLevel;", "", "aboveLevelId", "", "belowLevelId", "externalUuid", "Ljava/util/UUID;", "hierarchyLevelNumber", "", "id", "issueTypeIds", "", "level", "NAME", "", "projectConfigurationId", "<init>", "(Ljava/lang/Long;Ljava/lang/Long;Ljava/util/UUID;Ljava/lang/Integer;Ljava/lang/Long;Ljava/util/List;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Long;)V", "getAboveLevelId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getBelowLevelId", "getExternalUuid", "()Ljava/util/UUID;", "getHierarchyLevelNumber", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getId", "getIssueTypeIds", "()Ljava/util/List;", "getLevel", "getNAME", "()Ljava/lang/String;", "getProjectConfigurationId", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/Long;Ljava/lang/Long;Ljava/util/UUID;Ljava/lang/Integer;Ljava/lang/Long;Ljava/util/List;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/SimplifiedHierarchyLevel;", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class SimplifiedHierarchyLevel {
   @Nullable
   private final Long aboveLevelId;
   @Nullable
   private final Long belowLevelId;
   @Nullable
   private final UUID externalUuid;
   @Nullable
   private final Integer hierarchyLevelNumber;
   @Nullable
   private final Long id;
   @Nullable
   private final List<Long> issueTypeIds;
   @Nullable
   private final Integer level;
   @Nullable
   private final String NAME;
   @Nullable
   private final Long projectConfigurationId;

   public SimplifiedHierarchyLevel(@Nullable Long aboveLevelId, @Nullable Long belowLevelId, @Nullable UUID externalUuid, @Nullable Integer hierarchyLevelNumber, @Nullable Long id, @Nullable List<Long> issueTypeIds, @Nullable Integer level, @Nullable String NAME, @Nullable Long projectConfigurationId) {
      this.aboveLevelId = aboveLevelId;
      this.belowLevelId = belowLevelId;
      this.externalUuid = externalUuid;
      this.hierarchyLevelNumber = hierarchyLevelNumber;
      this.id = id;
      this.issueTypeIds = issueTypeIds;
      this.level = level;
      this.NAME = NAME;
      this.projectConfigurationId = projectConfigurationId;
   }

   @JsonProperty("aboveLevelId")
   @Nullable
   public final Long getAboveLevelId() {
      return this.aboveLevelId;
   }

   @JsonProperty("belowLevelId")
   @Nullable
   public final Long getBelowLevelId() {
      return this.belowLevelId;
   }

   @JsonProperty("externalUuid")
   @Nullable
   public final UUID getExternalUuid() {
      return this.externalUuid;
   }

   @JsonProperty("hierarchyLevelNumber")
   @Nullable
   public final Integer getHierarchyLevelNumber() {
      return this.hierarchyLevelNumber;
   }

   @JsonProperty("id")
   @Nullable
   public final Long getId() {
      return this.id;
   }

   @JsonProperty("issueTypeIds")
   @Nullable
   public final List<Long> getIssueTypeIds() {
      return this.issueTypeIds;
   }

   @JsonProperty("level")
   @Nullable
   public final Integer getLevel() {
      return this.level;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("projectConfigurationId")
   @Nullable
   public final Long getProjectConfigurationId() {
      return this.projectConfigurationId;
   }

   @Nullable
   public final Long component1() {
      return this.aboveLevelId;
   }

   @Nullable
   public final Long component2() {
      return this.belowLevelId;
   }

   @Nullable
   public final UUID component3() {
      return this.externalUuid;
   }

   @Nullable
   public final Integer component4() {
      return this.hierarchyLevelNumber;
   }

   @Nullable
   public final Long component5() {
      return this.id;
   }

   @Nullable
   public final List<Long> component6() {
      return this.issueTypeIds;
   }

   @Nullable
   public final Integer component7() {
      return this.level;
   }

   @Nullable
   public final String component8() {
      return this.NAME;
   }

   @Nullable
   public final Long component9() {
      return this.projectConfigurationId;
   }

   @NotNull
   public final SimplifiedHierarchyLevel copy(@Nullable Long aboveLevelId, @Nullable Long belowLevelId, @Nullable UUID externalUuid, @Nullable Integer hierarchyLevelNumber, @Nullable Long id, @Nullable List<Long> issueTypeIds, @Nullable Integer level, @Nullable String NAME, @Nullable Long projectConfigurationId) {
      return new SimplifiedHierarchyLevel(aboveLevelId, belowLevelId, externalUuid, hierarchyLevelNumber, id, issueTypeIds, level, NAME, projectConfigurationId);
   }

   @NotNull
   public String toString() {
      return "SimplifiedHierarchyLevel(aboveLevelId=" + this.aboveLevelId + ", belowLevelId=" + this.belowLevelId + ", externalUuid=" + this.externalUuid + ", hierarchyLevelNumber=" + this.hierarchyLevelNumber + ", id=" + this.id + ", issueTypeIds=" + this.issueTypeIds + ", level=" + this.level + ", NAME=" + this.NAME + ", projectConfigurationId=" + this.projectConfigurationId + ")";
   }

   public int hashCode() {
      int var1 = this.aboveLevelId == null ? 0 : this.aboveLevelId.hashCode();
      var1 = var1 * 31 + (this.belowLevelId == null ? 0 : this.belowLevelId.hashCode());
      var1 = var1 * 31 + (this.externalUuid == null ? 0 : this.externalUuid.hashCode());
      var1 = var1 * 31 + (this.hierarchyLevelNumber == null ? 0 : this.hierarchyLevelNumber.hashCode());
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      var1 = var1 * 31 + (this.issueTypeIds == null ? 0 : this.issueTypeIds.hashCode());
      var1 = var1 * 31 + (this.level == null ? 0 : this.level.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.projectConfigurationId == null ? 0 : this.projectConfigurationId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof SimplifiedHierarchyLevel)) {
         return false;
      } else {
         SimplifiedHierarchyLevel var2 = (SimplifiedHierarchyLevel)other;
         if (!Intrinsics.areEqual(this.aboveLevelId, var2.aboveLevelId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.belowLevelId, var2.belowLevelId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.externalUuid, var2.externalUuid)) {
            return false;
         } else if (!Intrinsics.areEqual(this.hierarchyLevelNumber, var2.hierarchyLevelNumber)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueTypeIds, var2.issueTypeIds)) {
            return false;
         } else if (!Intrinsics.areEqual(this.level, var2.level)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.projectConfigurationId, var2.projectConfigurationId);
         }
      }
   }

   public SimplifiedHierarchyLevel() {
      this((Long)null, (Long)null, (UUID)null, (Integer)null, (Long)null, (List)null, (Integer)null, (String)null, (Long)null, 511, (DefaultConstructorMarker)null);
   }
}
