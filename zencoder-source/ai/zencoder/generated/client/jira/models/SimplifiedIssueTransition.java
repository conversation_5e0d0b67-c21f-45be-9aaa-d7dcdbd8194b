package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\rJ\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J2\u0010\u0014\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0015J\u0013\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0007H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\f\u0010\rR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u001b"},
   d2 = {"Lai/zencoder/generated/client/jira/models/SimplifiedIssueTransition;", "", "to", "Lai/zencoder/generated/client/jira/models/IssueTransitionStatus;", "transitionId", "", "transitionName", "", "<init>", "(Lai/zencoder/generated/client/jira/models/IssueTransitionStatus;Ljava/lang/Integer;Ljava/lang/String;)V", "getTo", "()Lai/zencoder/generated/client/jira/models/IssueTransitionStatus;", "getTransitionId", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getTransitionName", "()Ljava/lang/String;", "component1", "component2", "component3", "copy", "(Lai/zencoder/generated/client/jira/models/IssueTransitionStatus;Ljava/lang/Integer;Ljava/lang/String;)Lai/zencoder/generated/client/jira/models/SimplifiedIssueTransition;", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class SimplifiedIssueTransition {
   @Nullable
   private final IssueTransitionStatus to;
   @Nullable
   private final Integer transitionId;
   @Nullable
   private final String transitionName;

   public SimplifiedIssueTransition(@Nullable IssueTransitionStatus to, @Nullable Integer transitionId, @Nullable String transitionName) {
      this.to = to;
      this.transitionId = transitionId;
      this.transitionName = transitionName;
   }

   @JsonProperty("to")
   @Nullable
   public final IssueTransitionStatus getTo() {
      return this.to;
   }

   @JsonProperty("transitionId")
   @Nullable
   public final Integer getTransitionId() {
      return this.transitionId;
   }

   @JsonProperty("transitionName")
   @Nullable
   public final String getTransitionName() {
      return this.transitionName;
   }

   @Nullable
   public final IssueTransitionStatus component1() {
      return this.to;
   }

   @Nullable
   public final Integer component2() {
      return this.transitionId;
   }

   @Nullable
   public final String component3() {
      return this.transitionName;
   }

   @NotNull
   public final SimplifiedIssueTransition copy(@Nullable IssueTransitionStatus to, @Nullable Integer transitionId, @Nullable String transitionName) {
      return new SimplifiedIssueTransition(to, transitionId, transitionName);
   }

   @NotNull
   public String toString() {
      return "SimplifiedIssueTransition(to=" + this.to + ", transitionId=" + this.transitionId + ", transitionName=" + this.transitionName + ")";
   }

   public int hashCode() {
      int var1 = this.to == null ? 0 : this.to.hashCode();
      var1 = var1 * 31 + (this.transitionId == null ? 0 : this.transitionId.hashCode());
      var1 = var1 * 31 + (this.transitionName == null ? 0 : this.transitionName.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof SimplifiedIssueTransition)) {
         return false;
      } else {
         SimplifiedIssueTransition var2 = (SimplifiedIssueTransition)other;
         if (!Intrinsics.areEqual(this.to, var2.to)) {
            return false;
         } else if (!Intrinsics.areEqual(this.transitionId, var2.transitionId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.transitionName, var2.transitionName);
         }
      }
   }

   public SimplifiedIssueTransition() {
      this((IssueTransitionStatus)null, (Integer)null, (String)null, 7, (DefaultConstructorMarker)null);
   }
}
