package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\t\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0011\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J-\u0010\r\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\t\u00a8\u0006\u0015"},
   d2 = {"Lai/zencoder/generated/client/jira/models/SuggestedMappingsForPrioritiesRequestBean;", "", "add", "", "", "remove", "<init>", "(Ljava/util/List;Ljava/util/List;)V", "getAdd", "()Ljava/util/List;", "getRemove", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class SuggestedMappingsForPrioritiesRequestBean {
   @Nullable
   private final List<Long> add;
   @Nullable
   private final List<Long> remove;

   public SuggestedMappingsForPrioritiesRequestBean(@Nullable List<Long> add, @Nullable List<Long> remove) {
      this.add = add;
      this.remove = remove;
   }

   @JsonProperty("add")
   @Nullable
   public final List<Long> getAdd() {
      return this.add;
   }

   @JsonProperty("remove")
   @Nullable
   public final List<Long> getRemove() {
      return this.remove;
   }

   @Nullable
   public final List<Long> component1() {
      return this.add;
   }

   @Nullable
   public final List<Long> component2() {
      return this.remove;
   }

   @NotNull
   public final SuggestedMappingsForPrioritiesRequestBean copy(@Nullable List<Long> add, @Nullable List<Long> remove) {
      return new SuggestedMappingsForPrioritiesRequestBean(add, remove);
   }

   @NotNull
   public String toString() {
      return "SuggestedMappingsForPrioritiesRequestBean(add=" + this.add + ", remove=" + this.remove + ")";
   }

   public int hashCode() {
      int var1 = this.add == null ? 0 : this.add.hashCode();
      var1 = var1 * 31 + (this.remove == null ? 0 : this.remove.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof SuggestedMappingsForPrioritiesRequestBean)) {
         return false;
      } else {
         SuggestedMappingsForPrioritiesRequestBean var2 = (SuggestedMappingsForPrioritiesRequestBean)other;
         if (!Intrinsics.areEqual(this.add, var2.add)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.remove, var2.remove);
         }
      }
   }

   public SuggestedMappingsForPrioritiesRequestBean() {
      this((List)null, (List)null, 3, (DefaultConstructorMarker)null);
   }
}
