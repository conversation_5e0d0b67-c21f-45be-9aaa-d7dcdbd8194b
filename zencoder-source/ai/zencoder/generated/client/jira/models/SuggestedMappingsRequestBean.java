package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0016\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BC\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0004\b\u000b\u0010\fJ\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000eJ\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u0010\u0010\u001b\u001a\u0004\u0018\u00010\tH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0015J\u0010\u0010\u001c\u001a\u0004\u0018\u00010\tH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0015JJ\u0010\u001d\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\tH\u00c6\u0001\u00a2\u0006\u0002\u0010\u001eJ\u0013\u0010\u001f\u001a\u00020 2\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\"\u001a\u00020\u0003H\u00d6\u0001J\t\u0010#\u001a\u00020$H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000f\u001a\u0004\b\r\u0010\u000eR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0017\u0010\b\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\n\n\u0002\u0010\u0016\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\n\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\n\n\u0002\u0010\u0016\u001a\u0004\b\u0017\u0010\u0015\u00a8\u0006%"},
   d2 = {"Lai/zencoder/generated/client/jira/models/SuggestedMappingsRequestBean;", "", "maxResults", "", "priorities", "Lai/zencoder/generated/client/jira/models/SuggestedMappingsForPrioritiesRequestBean;", "projects", "Lai/zencoder/generated/client/jira/models/SuggestedMappingsForProjectsRequestBean;", "schemeId", "", "startAt", "<init>", "(Ljava/lang/Integer;Lai/zencoder/generated/client/jira/models/SuggestedMappingsForPrioritiesRequestBean;Lai/zencoder/generated/client/jira/models/SuggestedMappingsForProjectsRequestBean;Ljava/lang/Long;Ljava/lang/Long;)V", "getMaxResults", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getPriorities", "()Lai/zencoder/generated/client/jira/models/SuggestedMappingsForPrioritiesRequestBean;", "getProjects", "()Lai/zencoder/generated/client/jira/models/SuggestedMappingsForProjectsRequestBean;", "getSchemeId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getStartAt", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/lang/Integer;Lai/zencoder/generated/client/jira/models/SuggestedMappingsForPrioritiesRequestBean;Lai/zencoder/generated/client/jira/models/SuggestedMappingsForProjectsRequestBean;Ljava/lang/Long;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/SuggestedMappingsRequestBean;", "equals", "", "other", "hashCode", "toString", "", "zencoder"}
)
public final class SuggestedMappingsRequestBean {
   @Nullable
   private final Integer maxResults;
   @Nullable
   private final SuggestedMappingsForPrioritiesRequestBean priorities;
   @Nullable
   private final SuggestedMappingsForProjectsRequestBean projects;
   @Nullable
   private final Long schemeId;
   @Nullable
   private final Long startAt;

   public SuggestedMappingsRequestBean(@Nullable Integer maxResults, @Nullable SuggestedMappingsForPrioritiesRequestBean priorities, @Nullable SuggestedMappingsForProjectsRequestBean projects, @Nullable Long schemeId, @Nullable Long startAt) {
      this.maxResults = maxResults;
      this.priorities = priorities;
      this.projects = projects;
      this.schemeId = schemeId;
      this.startAt = startAt;
   }

   @JsonProperty("maxResults")
   @Nullable
   public final Integer getMaxResults() {
      return this.maxResults;
   }

   @JsonProperty("priorities")
   @Nullable
   public final SuggestedMappingsForPrioritiesRequestBean getPriorities() {
      return this.priorities;
   }

   @JsonProperty("projects")
   @Nullable
   public final SuggestedMappingsForProjectsRequestBean getProjects() {
      return this.projects;
   }

   @JsonProperty("schemeId")
   @Nullable
   public final Long getSchemeId() {
      return this.schemeId;
   }

   @JsonProperty("startAt")
   @Nullable
   public final Long getStartAt() {
      return this.startAt;
   }

   @Nullable
   public final Integer component1() {
      return this.maxResults;
   }

   @Nullable
   public final SuggestedMappingsForPrioritiesRequestBean component2() {
      return this.priorities;
   }

   @Nullable
   public final SuggestedMappingsForProjectsRequestBean component3() {
      return this.projects;
   }

   @Nullable
   public final Long component4() {
      return this.schemeId;
   }

   @Nullable
   public final Long component5() {
      return this.startAt;
   }

   @NotNull
   public final SuggestedMappingsRequestBean copy(@Nullable Integer maxResults, @Nullable SuggestedMappingsForPrioritiesRequestBean priorities, @Nullable SuggestedMappingsForProjectsRequestBean projects, @Nullable Long schemeId, @Nullable Long startAt) {
      return new SuggestedMappingsRequestBean(maxResults, priorities, projects, schemeId, startAt);
   }

   @NotNull
   public String toString() {
      return "SuggestedMappingsRequestBean(maxResults=" + this.maxResults + ", priorities=" + this.priorities + ", projects=" + this.projects + ", schemeId=" + this.schemeId + ", startAt=" + this.startAt + ")";
   }

   public int hashCode() {
      int var1 = this.maxResults == null ? 0 : this.maxResults.hashCode();
      var1 = var1 * 31 + (this.priorities == null ? 0 : this.priorities.hashCode());
      var1 = var1 * 31 + (this.projects == null ? 0 : this.projects.hashCode());
      var1 = var1 * 31 + (this.schemeId == null ? 0 : this.schemeId.hashCode());
      var1 = var1 * 31 + (this.startAt == null ? 0 : this.startAt.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof SuggestedMappingsRequestBean)) {
         return false;
      } else {
         SuggestedMappingsRequestBean var2 = (SuggestedMappingsRequestBean)other;
         if (!Intrinsics.areEqual(this.maxResults, var2.maxResults)) {
            return false;
         } else if (!Intrinsics.areEqual(this.priorities, var2.priorities)) {
            return false;
         } else if (!Intrinsics.areEqual(this.projects, var2.projects)) {
            return false;
         } else if (!Intrinsics.areEqual(this.schemeId, var2.schemeId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.startAt, var2.startAt);
         }
      }
   }

   public SuggestedMappingsRequestBean() {
      this((Integer)null, (SuggestedMappingsForPrioritiesRequestBean)null, (SuggestedMappingsForProjectsRequestBean)null, (Long)null, (Long)null, 31, (DefaultConstructorMarker)null);
   }
}
