package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.net.URI;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b'\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001?B\u0083\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\u0003\u0012\u0006\u0010\r\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0012\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0014\u0010\u0015J\t\u0010*\u001a\u00020\u0003H\u00c6\u0003J\t\u0010+\u001a\u00020\u0005H\u00c6\u0003J\t\u0010,\u001a\u00020\u0003H\u00c6\u0003J\t\u0010-\u001a\u00020\u0003H\u00c6\u0003J\t\u0010.\u001a\u00020\tH\u00c6\u0003J\t\u0010/\u001a\u00020\u000bH\u00c6\u0003J\t\u00100\u001a\u00020\u0003H\u00c6\u0003J\t\u00101\u001a\u00020\u0003H\u00c6\u0003J\u000b\u00102\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u00103\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010$J\u000b\u00104\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u00105\u001a\u0004\u0018\u00010\u0012H\u00c6\u0003J\u0010\u00106\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010$J\u009a\u0001\u00107\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u00032\b\b\u0002\u0010\r\u001a\u00020\u00032\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00122\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u00108J\u0013\u00109\u001a\u00020:2\b\u0010;\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010<\u001a\u00020=H\u00d6\u0001J\t\u0010>\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0013\u0010\u0006\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0017R\u0013\u0010\u0007\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0017R\u0013\u0010\b\u001a\u00020\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0013\u0010\n\u001a\u00020\u000b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0013\u0010\f\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0017R\u0013\u0010\r\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0017R\u0015\u0010\u000e\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0019R\u0017\u0010\u000f\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010%\u001a\u0004\b#\u0010$R\u0015\u0010\u0010\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u0019R\u0015\u0010\u0011\u001a\u0004\u0018\u00010\u00128G\u00a2\u0006\b\n\u0000\u001a\u0004\b'\u0010(R\u0017\u0010\u0013\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010%\u001a\u0004\b)\u0010$\u00a8\u0006@"},
   d2 = {"Lai/zencoder/generated/client/jira/models/TaskProgressBeanRemoveOptionFromIssuesResult;", "", "elapsedRuntime", "", "id", "", "lastUpdate", "progress", "self", "Ljava/net/URI;", "status", "Lai/zencoder/generated/client/jira/models/TaskProgressBeanRemoveOptionFromIssuesResult$Status;", "submitted", "submittedBy", "description", "finished", "message", "result", "Lai/zencoder/generated/client/jira/models/RemoveOptionFromIssuesResult;", "started", "<init>", "(JLjava/lang/String;JJLjava/net/URI;Lai/zencoder/generated/client/jira/models/TaskProgressBeanRemoveOptionFromIssuesResult$Status;JJLjava/lang/String;Ljava/lang/Long;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/RemoveOptionFromIssuesResult;Ljava/lang/Long;)V", "getElapsedRuntime", "()J", "getId", "()Ljava/lang/String;", "getLastUpdate", "getProgress", "getSelf", "()Ljava/net/URI;", "getStatus", "()Lai/zencoder/generated/client/jira/models/TaskProgressBeanRemoveOptionFromIssuesResult$Status;", "getSubmitted", "getSubmittedBy", "getDescription", "getFinished", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getMessage", "getResult", "()Lai/zencoder/generated/client/jira/models/RemoveOptionFromIssuesResult;", "getStarted", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "copy", "(JLjava/lang/String;JJLjava/net/URI;Lai/zencoder/generated/client/jira/models/TaskProgressBeanRemoveOptionFromIssuesResult$Status;JJLjava/lang/String;Ljava/lang/Long;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/RemoveOptionFromIssuesResult;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/TaskProgressBeanRemoveOptionFromIssuesResult;", "equals", "", "other", "hashCode", "", "toString", "Status", "zencoder"}
)
public final class TaskProgressBeanRemoveOptionFromIssuesResult {
   private final long elapsedRuntime;
   @NotNull
   private final String id;
   private final long lastUpdate;
   private final long progress;
   @NotNull
   private final URI self;
   @NotNull
   private final Status status;
   private final long submitted;
   private final long submittedBy;
   @Nullable
   private final String description;
   @Nullable
   private final Long finished;
   @Nullable
   private final String message;
   @Nullable
   private final RemoveOptionFromIssuesResult result;
   @Nullable
   private final Long started;

   public TaskProgressBeanRemoveOptionFromIssuesResult(long elapsedRuntime, @NotNull String id, long lastUpdate, long progress, @NotNull URI self, @NotNull Status status, long submitted, long submittedBy, @Nullable String description, @Nullable Long finished, @Nullable String message, @Nullable RemoveOptionFromIssuesResult result, @Nullable Long started) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(self, "");
      Intrinsics.checkNotNullParameter(status, "");
      super();
      this.elapsedRuntime = elapsedRuntime;
      this.id = id;
      this.lastUpdate = lastUpdate;
      this.progress = progress;
      this.self = self;
      this.status = status;
      this.submitted = submitted;
      this.submittedBy = submittedBy;
      this.description = description;
      this.finished = finished;
      this.message = message;
      this.result = result;
      this.started = started;
   }

   @JsonProperty("elapsedRuntime")
   public final long getElapsedRuntime() {
      return this.elapsedRuntime;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("lastUpdate")
   public final long getLastUpdate() {
      return this.lastUpdate;
   }

   @JsonProperty("progress")
   public final long getProgress() {
      return this.progress;
   }

   @JsonProperty("self")
   @NotNull
   public final URI getSelf() {
      return this.self;
   }

   @JsonProperty("status")
   @NotNull
   public final Status getStatus() {
      return this.status;
   }

   @JsonProperty("submitted")
   public final long getSubmitted() {
      return this.submitted;
   }

   @JsonProperty("submittedBy")
   public final long getSubmittedBy() {
      return this.submittedBy;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("finished")
   @Nullable
   public final Long getFinished() {
      return this.finished;
   }

   @JsonProperty("message")
   @Nullable
   public final String getMessage() {
      return this.message;
   }

   @JsonProperty("result")
   @Nullable
   public final RemoveOptionFromIssuesResult getResult() {
      return this.result;
   }

   @JsonProperty("started")
   @Nullable
   public final Long getStarted() {
      return this.started;
   }

   public final long component1() {
      return this.elapsedRuntime;
   }

   @NotNull
   public final String component2() {
      return this.id;
   }

   public final long component3() {
      return this.lastUpdate;
   }

   public final long component4() {
      return this.progress;
   }

   @NotNull
   public final URI component5() {
      return this.self;
   }

   @NotNull
   public final Status component6() {
      return this.status;
   }

   public final long component7() {
      return this.submitted;
   }

   public final long component8() {
      return this.submittedBy;
   }

   @Nullable
   public final String component9() {
      return this.description;
   }

   @Nullable
   public final Long component10() {
      return this.finished;
   }

   @Nullable
   public final String component11() {
      return this.message;
   }

   @Nullable
   public final RemoveOptionFromIssuesResult component12() {
      return this.result;
   }

   @Nullable
   public final Long component13() {
      return this.started;
   }

   @NotNull
   public final TaskProgressBeanRemoveOptionFromIssuesResult copy(long elapsedRuntime, @NotNull String id, long lastUpdate, long progress, @NotNull URI self, @NotNull Status status, long submitted, long submittedBy, @Nullable String description, @Nullable Long finished, @Nullable String message, @Nullable RemoveOptionFromIssuesResult result, @Nullable Long started) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(self, "");
      Intrinsics.checkNotNullParameter(status, "");
      return new TaskProgressBeanRemoveOptionFromIssuesResult(elapsedRuntime, id, lastUpdate, progress, self, status, submitted, submittedBy, description, finished, message, result, started);
   }

   @NotNull
   public String toString() {
      return "TaskProgressBeanRemoveOptionFromIssuesResult(elapsedRuntime=" + this.elapsedRuntime + ", id=" + this.id + ", lastUpdate=" + this.lastUpdate + ", progress=" + this.progress + ", self=" + this.self + ", status=" + this.status + ", submitted=" + this.submitted + ", submittedBy=" + this.submittedBy + ", description=" + this.description + ", finished=" + this.finished + ", message=" + this.message + ", result=" + this.result + ", started=" + this.started + ")";
   }

   public int hashCode() {
      int var1 = Long.hashCode(this.elapsedRuntime);
      var1 = var1 * 31 + this.id.hashCode();
      var1 = var1 * 31 + Long.hashCode(this.lastUpdate);
      var1 = var1 * 31 + Long.hashCode(this.progress);
      var1 = var1 * 31 + this.self.hashCode();
      var1 = var1 * 31 + this.status.hashCode();
      var1 = var1 * 31 + Long.hashCode(this.submitted);
      var1 = var1 * 31 + Long.hashCode(this.submittedBy);
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.finished == null ? 0 : this.finished.hashCode());
      var1 = var1 * 31 + (this.message == null ? 0 : this.message.hashCode());
      var1 = var1 * 31 + (this.result == null ? 0 : this.result.hashCode());
      var1 = var1 * 31 + (this.started == null ? 0 : this.started.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof TaskProgressBeanRemoveOptionFromIssuesResult)) {
         return false;
      } else {
         TaskProgressBeanRemoveOptionFromIssuesResult var2 = (TaskProgressBeanRemoveOptionFromIssuesResult)other;
         if (this.elapsedRuntime != var2.elapsedRuntime) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (this.lastUpdate != var2.lastUpdate) {
            return false;
         } else if (this.progress != var2.progress) {
            return false;
         } else if (!Intrinsics.areEqual(this.self, var2.self)) {
            return false;
         } else if (this.status != var2.status) {
            return false;
         } else if (this.submitted != var2.submitted) {
            return false;
         } else if (this.submittedBy != var2.submittedBy) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.finished, var2.finished)) {
            return false;
         } else if (!Intrinsics.areEqual(this.message, var2.message)) {
            return false;
         } else if (!Intrinsics.areEqual(this.result, var2.result)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.started, var2.started);
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000e\u00a8\u0006\u000f"},
      d2 = {"Lai/zencoder/generated/client/jira/models/TaskProgressBeanRemoveOptionFromIssuesResult$Status;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "ENQUEUED", "RUNNING", "COMPLETE", "FAILED", "CANCEL_REQUESTED", "CANCELLED", "DEAD", "zencoder"}
   )
   public static enum Status {
      @NotNull
      private final String value;
      @JsonProperty("ENQUEUED")
      ENQUEUED("ENQUEUED"),
      @JsonProperty("RUNNING")
      RUNNING("RUNNING"),
      @JsonProperty("COMPLETE")
      COMPLETE("COMPLETE"),
      @JsonProperty("FAILED")
      FAILED("FAILED"),
      @JsonProperty("CANCEL_REQUESTED")
      CANCEL_REQUESTED("CANCEL_REQUESTED"),
      @JsonProperty("CANCELLED")
      CANCELLED("CANCELLED"),
      @JsonProperty("DEAD")
      DEAD("DEAD");

      private Status(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Status> getEntries() {
         return $ENTRIES;
      }
   }
}
