package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0002\u001e\u001fB'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u00a2\u0006\u0004\b\t\u0010\nJ\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0007H\u00c6\u0003J1\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0006\u001a\u00020\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\b\u001a\u00020\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010\u00a8\u0006 "},
   d2 = {"Lai/zencoder/generated/client/jira/models/TimeTrackingConfiguration;", "", "defaultUnit", "Lai/zencoder/generated/client/jira/models/TimeTrackingConfiguration$DefaultUnit;", "timeFormat", "Lai/zencoder/generated/client/jira/models/TimeTrackingConfiguration$TimeFormat;", "workingDaysPerWeek", "", "workingHoursPerDay", "<init>", "(Lai/zencoder/generated/client/jira/models/TimeTrackingConfiguration$DefaultUnit;Lai/zencoder/generated/client/jira/models/TimeTrackingConfiguration$TimeFormat;DD)V", "getDefaultUnit", "()Lai/zencoder/generated/client/jira/models/TimeTrackingConfiguration$DefaultUnit;", "getTimeFormat", "()Lai/zencoder/generated/client/jira/models/TimeTrackingConfiguration$TimeFormat;", "getWorkingDaysPerWeek", "()D", "getWorkingHoursPerDay", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "", "DefaultUnit", "TimeFormat", "zencoder"}
)
public final class TimeTrackingConfiguration {
   @NotNull
   private final DefaultUnit defaultUnit;
   @NotNull
   private final TimeFormat timeFormat;
   private final double workingDaysPerWeek;
   private final double workingHoursPerDay;

   public TimeTrackingConfiguration(@NotNull DefaultUnit defaultUnit, @NotNull TimeFormat timeFormat, double workingDaysPerWeek, double workingHoursPerDay) {
      Intrinsics.checkNotNullParameter(defaultUnit, "");
      Intrinsics.checkNotNullParameter(timeFormat, "");
      super();
      this.defaultUnit = defaultUnit;
      this.timeFormat = timeFormat;
      this.workingDaysPerWeek = workingDaysPerWeek;
      this.workingHoursPerDay = workingHoursPerDay;
   }

   @JsonProperty("defaultUnit")
   @NotNull
   public final DefaultUnit getDefaultUnit() {
      return this.defaultUnit;
   }

   @JsonProperty("timeFormat")
   @NotNull
   public final TimeFormat getTimeFormat() {
      return this.timeFormat;
   }

   @JsonProperty("workingDaysPerWeek")
   public final double getWorkingDaysPerWeek() {
      return this.workingDaysPerWeek;
   }

   @JsonProperty("workingHoursPerDay")
   public final double getWorkingHoursPerDay() {
      return this.workingHoursPerDay;
   }

   @NotNull
   public final DefaultUnit component1() {
      return this.defaultUnit;
   }

   @NotNull
   public final TimeFormat component2() {
      return this.timeFormat;
   }

   public final double component3() {
      return this.workingDaysPerWeek;
   }

   public final double component4() {
      return this.workingHoursPerDay;
   }

   @NotNull
   public final TimeTrackingConfiguration copy(@NotNull DefaultUnit defaultUnit, @NotNull TimeFormat timeFormat, double workingDaysPerWeek, double workingHoursPerDay) {
      Intrinsics.checkNotNullParameter(defaultUnit, "");
      Intrinsics.checkNotNullParameter(timeFormat, "");
      return new TimeTrackingConfiguration(defaultUnit, timeFormat, workingDaysPerWeek, workingHoursPerDay);
   }

   @NotNull
   public String toString() {
      return "TimeTrackingConfiguration(defaultUnit=" + this.defaultUnit + ", timeFormat=" + this.timeFormat + ", workingDaysPerWeek=" + this.workingDaysPerWeek + ", workingHoursPerDay=" + this.workingHoursPerDay + ")";
   }

   public int hashCode() {
      int var1 = this.defaultUnit.hashCode();
      var1 = var1 * 31 + this.timeFormat.hashCode();
      var1 = var1 * 31 + Double.hashCode(this.workingDaysPerWeek);
      var1 = var1 * 31 + Double.hashCode(this.workingHoursPerDay);
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof TimeTrackingConfiguration)) {
         return false;
      } else {
         TimeTrackingConfiguration var2 = (TimeTrackingConfiguration)other;
         if (this.defaultUnit != var2.defaultUnit) {
            return false;
         } else if (this.timeFormat != var2.timeFormat) {
            return false;
         } else if (Double.compare(this.workingDaysPerWeek, var2.workingDaysPerWeek) != 0) {
            return false;
         } else {
            return Double.compare(this.workingHoursPerDay, var2.workingHoursPerDay) == 0;
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"},
      d2 = {"Lai/zencoder/generated/client/jira/models/TimeTrackingConfiguration$DefaultUnit;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "minute", "hour", "day", "week", "zencoder"}
   )
   public static enum DefaultUnit {
      @NotNull
      private final String value;
      @JsonProperty("minute")
      minute("minute"),
      @JsonProperty("hour")
      hour("hour"),
      @JsonProperty("day")
      day("day"),
      @JsonProperty("week")
      week("week");

      private DefaultUnit(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<DefaultUnit> getEntries() {
         return $ENTRIES;
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"},
      d2 = {"Lai/zencoder/generated/client/jira/models/TimeTrackingConfiguration$TimeFormat;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "pretty", "days", "hours", "zencoder"}
   )
   public static enum TimeFormat {
      @NotNull
      private final String value;
      @JsonProperty("pretty")
      pretty("pretty"),
      @JsonProperty("days")
      days("days"),
      @JsonProperty("hours")
      hours("hours");

      private TimeFormat(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<TimeFormat> getEntries() {
         return $ENTRIES;
      }
   }
}
