package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001 BC\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0004\b\n\u0010\u000bJ\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000eJ\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\tH\u00c6\u0003JJ\u0010\u0019\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\tH\u00c6\u0001\u00a2\u0006\u0002\u0010\u001aJ\u0013\u0010\u001b\u001a\u00020\u00052\b\u0010\u001c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001d\u001a\u00020\u001eH\u00d6\u0001J\t\u0010\u001f\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\n\n\u0002\u0010\u000f\u001a\u0004\b\u0004\u0010\u000eR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\rR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\rR\u0015\u0010\b\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013\u00a8\u0006!"},
   d2 = {"Lai/zencoder/generated/client/jira/models/UiModificationContextDetails;", "", "id", "", "isAvailable", "", "issueTypeId", "projectId", "viewType", "Lai/zencoder/generated/client/jira/models/UiModificationContextDetails$ViewType;", "<init>", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/UiModificationContextDetails$ViewType;)V", "getId", "()Ljava/lang/String;", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getIssueTypeId", "getProjectId", "getViewType", "()Lai/zencoder/generated/client/jira/models/UiModificationContextDetails$ViewType;", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/UiModificationContextDetails$ViewType;)Lai/zencoder/generated/client/jira/models/UiModificationContextDetails;", "equals", "other", "hashCode", "", "toString", "ViewType", "zencoder"}
)
public final class UiModificationContextDetails {
   @Nullable
   private final String id;
   @Nullable
   private final Boolean isAvailable;
   @Nullable
   private final String issueTypeId;
   @Nullable
   private final String projectId;
   @Nullable
   private final ViewType viewType;

   public UiModificationContextDetails(@Nullable String id, @Nullable Boolean isAvailable, @Nullable String issueTypeId, @Nullable String projectId, @Nullable ViewType viewType) {
      this.id = id;
      this.isAvailable = isAvailable;
      this.issueTypeId = issueTypeId;
      this.projectId = projectId;
      this.viewType = viewType;
   }

   @JsonProperty("id")
   @Nullable
   public final String getId() {
      return this.id;
   }

   @JsonProperty("isAvailable")
   @Nullable
   public final Boolean isAvailable() {
      return this.isAvailable;
   }

   @JsonProperty("issueTypeId")
   @Nullable
   public final String getIssueTypeId() {
      return this.issueTypeId;
   }

   @JsonProperty("projectId")
   @Nullable
   public final String getProjectId() {
      return this.projectId;
   }

   @JsonProperty("viewType")
   @Nullable
   public final ViewType getViewType() {
      return this.viewType;
   }

   @Nullable
   public final String component1() {
      return this.id;
   }

   @Nullable
   public final Boolean component2() {
      return this.isAvailable;
   }

   @Nullable
   public final String component3() {
      return this.issueTypeId;
   }

   @Nullable
   public final String component4() {
      return this.projectId;
   }

   @Nullable
   public final ViewType component5() {
      return this.viewType;
   }

   @NotNull
   public final UiModificationContextDetails copy(@Nullable String id, @Nullable Boolean isAvailable, @Nullable String issueTypeId, @Nullable String projectId, @Nullable ViewType viewType) {
      return new UiModificationContextDetails(id, isAvailable, issueTypeId, projectId, viewType);
   }

   @NotNull
   public String toString() {
      return "UiModificationContextDetails(id=" + this.id + ", isAvailable=" + this.isAvailable + ", issueTypeId=" + this.issueTypeId + ", projectId=" + this.projectId + ", viewType=" + this.viewType + ")";
   }

   public int hashCode() {
      int var1 = this.id == null ? 0 : this.id.hashCode();
      var1 = var1 * 31 + (this.isAvailable == null ? 0 : this.isAvailable.hashCode());
      var1 = var1 * 31 + (this.issueTypeId == null ? 0 : this.issueTypeId.hashCode());
      var1 = var1 * 31 + (this.projectId == null ? 0 : this.projectId.hashCode());
      var1 = var1 * 31 + (this.viewType == null ? 0 : this.viewType.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof UiModificationContextDetails)) {
         return false;
      } else {
         UiModificationContextDetails var2 = (UiModificationContextDetails)other;
         if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.isAvailable, var2.isAvailable)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issueTypeId, var2.issueTypeId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.projectId, var2.projectId)) {
            return false;
         } else {
            return this.viewType == var2.viewType;
         }
      }
   }

   public UiModificationContextDetails() {
      this((String)null, (Boolean)null, (String)null, (String)null, (ViewType)null, 31, (DefaultConstructorMarker)null);
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"},
      d2 = {"Lai/zencoder/generated/client/jira/models/UiModificationContextDetails$ViewType;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "GIC", "IssueView", "IssueTransition", "zencoder"}
   )
   public static enum ViewType {
      @NotNull
      private final String value;
      @JsonProperty("GIC")
      GIC("GIC"),
      @JsonProperty("IssueView")
      IssueView("IssueView"),
      @JsonProperty("IssueTransition")
      IssueTransition("IssueTransition");

      private ViewType(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<ViewType> getEntries() {
         return $ENTRIES;
      }
   }
}
