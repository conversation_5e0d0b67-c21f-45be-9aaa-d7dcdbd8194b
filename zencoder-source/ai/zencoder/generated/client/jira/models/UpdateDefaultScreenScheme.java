package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\t\u0010\b\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\t\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\n\u001a\u00020\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0010"},
   d2 = {"Lai/zencoder/generated/client/jira/models/UpdateDefaultScreenScheme;", "", "screenSchemeId", "", "<init>", "(Ljava/lang/String;)V", "getScreenSchemeId", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class UpdateDefaultScreenScheme {
   @NotNull
   private final String screenSchemeId;

   public UpdateDefaultScreenScheme(@NotNull String screenSchemeId) {
      Intrinsics.checkNotNullParameter(screenSchemeId, "");
      super();
      this.screenSchemeId = screenSchemeId;
   }

   @JsonProperty("screenSchemeId")
   @NotNull
   public final String getScreenSchemeId() {
      return this.screenSchemeId;
   }

   @NotNull
   public final String component1() {
      return this.screenSchemeId;
   }

   @NotNull
   public final UpdateDefaultScreenScheme copy(@NotNull String screenSchemeId) {
      Intrinsics.checkNotNullParameter(screenSchemeId, "");
      return new UpdateDefaultScreenScheme(screenSchemeId);
   }

   @NotNull
   public String toString() {
      return "UpdateDefaultScreenScheme(screenSchemeId=" + this.screenSchemeId + ")";
   }

   public int hashCode() {
      return this.screenSchemeId.hashCode();
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof UpdateDefaultScreenScheme)) {
         return false;
      } else {
         UpdateDefaultScreenScheme var2 = (UpdateDefaultScreenScheme)other;
         return Intrinsics.areEqual(this.screenSchemeId, var2.screenSchemeId);
      }
   }
}
