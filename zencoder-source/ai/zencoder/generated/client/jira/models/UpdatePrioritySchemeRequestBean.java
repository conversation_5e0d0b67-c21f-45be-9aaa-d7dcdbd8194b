package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BO\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\u0004\b\r\u0010\u000eJ\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0010J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\fH\u00c6\u0003JV\u0010!\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00c6\u0001\u00a2\u0006\u0002\u0010\"J\u0013\u0010#\u001a\u00020$2\b\u0010%\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010&\u001a\u00020'H\u00d6\u0001J\t\u0010(\u001a\u00020\u0005H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u0011\u001a\u0004\b\u000f\u0010\u0010R\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0015\u0010\b\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013R\u0015\u0010\t\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0015\u0010\u000b\u001a\u0004\u0018\u00010\f8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006)"},
   d2 = {"Lai/zencoder/generated/client/jira/models/UpdatePrioritySchemeRequestBean;", "", "defaultPriorityId", "", "description", "", "mappings", "Lai/zencoder/generated/client/jira/models/PriorityMapping;", "NAME", "priorities", "Lai/zencoder/generated/client/jira/models/UpdatePrioritiesInSchemeRequestBean;", "projects", "Lai/zencoder/generated/client/jira/models/UpdateProjectsInSchemeRequestBean;", "<init>", "(Ljava/lang/Long;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/PriorityMapping;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/UpdatePrioritiesInSchemeRequestBean;Lai/zencoder/generated/client/jira/models/UpdateProjectsInSchemeRequestBean;)V", "getDefaultPriorityId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getDescription", "()Ljava/lang/String;", "getMappings", "()Lai/zencoder/generated/client/jira/models/PriorityMapping;", "getNAME", "getPriorities", "()Lai/zencoder/generated/client/jira/models/UpdatePrioritiesInSchemeRequestBean;", "getProjects", "()Lai/zencoder/generated/client/jira/models/UpdateProjectsInSchemeRequestBean;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "(Ljava/lang/Long;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/PriorityMapping;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/UpdatePrioritiesInSchemeRequestBean;Lai/zencoder/generated/client/jira/models/UpdateProjectsInSchemeRequestBean;)Lai/zencoder/generated/client/jira/models/UpdatePrioritySchemeRequestBean;", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class UpdatePrioritySchemeRequestBean {
   @Nullable
   private final Long defaultPriorityId;
   @Nullable
   private final String description;
   @Nullable
   private final PriorityMapping mappings;
   @Nullable
   private final String NAME;
   @Nullable
   private final UpdatePrioritiesInSchemeRequestBean priorities;
   @Nullable
   private final UpdateProjectsInSchemeRequestBean projects;

   public UpdatePrioritySchemeRequestBean(@Nullable Long defaultPriorityId, @Nullable String description, @Nullable PriorityMapping mappings, @Nullable String NAME, @Nullable UpdatePrioritiesInSchemeRequestBean priorities, @Nullable UpdateProjectsInSchemeRequestBean projects) {
      this.defaultPriorityId = defaultPriorityId;
      this.description = description;
      this.mappings = mappings;
      this.NAME = NAME;
      this.priorities = priorities;
      this.projects = projects;
   }

   @JsonProperty("defaultPriorityId")
   @Nullable
   public final Long getDefaultPriorityId() {
      return this.defaultPriorityId;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("mappings")
   @Nullable
   public final PriorityMapping getMappings() {
      return this.mappings;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("priorities")
   @Nullable
   public final UpdatePrioritiesInSchemeRequestBean getPriorities() {
      return this.priorities;
   }

   @JsonProperty("projects")
   @Nullable
   public final UpdateProjectsInSchemeRequestBean getProjects() {
      return this.projects;
   }

   @Nullable
   public final Long component1() {
      return this.defaultPriorityId;
   }

   @Nullable
   public final String component2() {
      return this.description;
   }

   @Nullable
   public final PriorityMapping component3() {
      return this.mappings;
   }

   @Nullable
   public final String component4() {
      return this.NAME;
   }

   @Nullable
   public final UpdatePrioritiesInSchemeRequestBean component5() {
      return this.priorities;
   }

   @Nullable
   public final UpdateProjectsInSchemeRequestBean component6() {
      return this.projects;
   }

   @NotNull
   public final UpdatePrioritySchemeRequestBean copy(@Nullable Long defaultPriorityId, @Nullable String description, @Nullable PriorityMapping mappings, @Nullable String NAME, @Nullable UpdatePrioritiesInSchemeRequestBean priorities, @Nullable UpdateProjectsInSchemeRequestBean projects) {
      return new UpdatePrioritySchemeRequestBean(defaultPriorityId, description, mappings, NAME, priorities, projects);
   }

   @NotNull
   public String toString() {
      return "UpdatePrioritySchemeRequestBean(defaultPriorityId=" + this.defaultPriorityId + ", description=" + this.description + ", mappings=" + this.mappings + ", NAME=" + this.NAME + ", priorities=" + this.priorities + ", projects=" + this.projects + ")";
   }

   public int hashCode() {
      int var1 = this.defaultPriorityId == null ? 0 : this.defaultPriorityId.hashCode();
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.mappings == null ? 0 : this.mappings.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.priorities == null ? 0 : this.priorities.hashCode());
      var1 = var1 * 31 + (this.projects == null ? 0 : this.projects.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof UpdatePrioritySchemeRequestBean)) {
         return false;
      } else {
         UpdatePrioritySchemeRequestBean var2 = (UpdatePrioritySchemeRequestBean)other;
         if (!Intrinsics.areEqual(this.defaultPriorityId, var2.defaultPriorityId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.mappings, var2.mappings)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else if (!Intrinsics.areEqual(this.priorities, var2.priorities)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.projects, var2.projects);
         }
      }
   }

   public UpdatePrioritySchemeRequestBean() {
      this((Long)null, (String)null, (PriorityMapping)null, (String)null, (UpdatePrioritiesInSchemeRequestBean)null, (UpdateProjectsInSchemeRequestBean)null, 63, (DefaultConstructorMarker)null);
   }
}
