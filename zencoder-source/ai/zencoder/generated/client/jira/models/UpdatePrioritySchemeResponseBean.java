package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004B\u001f\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\u0004\b\t\u0010\nJ\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\bH\u00c6\u0003J!\u0010\u0011\u001a\u00020\u00002\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\bH\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0002H\u00d6\u0001R\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/UpdatePrioritySchemeResponseBean;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "priorityScheme", "Lai/zencoder/generated/client/jira/models/PrioritySchemeWithPaginatedPrioritiesAndProjects;", "task", "Lai/zencoder/generated/client/jira/models/TaskProgressBeanJsonNode;", "<init>", "(Lai/zencoder/generated/client/jira/models/PrioritySchemeWithPaginatedPrioritiesAndProjects;Lai/zencoder/generated/client/jira/models/TaskProgressBeanJsonNode;)V", "getPriorityScheme", "()Lai/zencoder/generated/client/jira/models/PrioritySchemeWithPaginatedPrioritiesAndProjects;", "getTask", "()Lai/zencoder/generated/client/jira/models/TaskProgressBeanJsonNode;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class UpdatePrioritySchemeResponseBean extends HashMap<String, Object> {
   @Nullable
   private final PrioritySchemeWithPaginatedPrioritiesAndProjects priorityScheme;
   @Nullable
   private final TaskProgressBeanJsonNode task;

   public UpdatePrioritySchemeResponseBean(@Nullable PrioritySchemeWithPaginatedPrioritiesAndProjects priorityScheme, @Nullable TaskProgressBeanJsonNode task) {
      this.priorityScheme = priorityScheme;
      this.task = task;
   }

   @JsonProperty("priorityScheme")
   @Nullable
   public final PrioritySchemeWithPaginatedPrioritiesAndProjects getPriorityScheme() {
      return this.priorityScheme;
   }

   @JsonProperty("task")
   @Nullable
   public final TaskProgressBeanJsonNode getTask() {
      return this.task;
   }

   @Nullable
   public final PrioritySchemeWithPaginatedPrioritiesAndProjects component1() {
      return this.priorityScheme;
   }

   @Nullable
   public final TaskProgressBeanJsonNode component2() {
      return this.task;
   }

   @NotNull
   public final UpdatePrioritySchemeResponseBean copy(@Nullable PrioritySchemeWithPaginatedPrioritiesAndProjects priorityScheme, @Nullable TaskProgressBeanJsonNode task) {
      return new UpdatePrioritySchemeResponseBean(priorityScheme, task);
   }

   @NotNull
   public String toString() {
      return "UpdatePrioritySchemeResponseBean(priorityScheme=" + this.priorityScheme + ", task=" + this.task + ")";
   }

   public int hashCode() {
      int var1 = this.priorityScheme == null ? 0 : this.priorityScheme.hashCode();
      var1 = var1 * 31 + (this.task == null ? 0 : this.task.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof UpdatePrioritySchemeResponseBean)) {
         return false;
      } else {
         UpdatePrioritySchemeResponseBean var2 = (UpdatePrioritySchemeResponseBean)other;
         if (!Intrinsics.areEqual(this.priorityScheme, var2.priorityScheme)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.task, var2.task);
         }
      }
   }

   public UpdatePrioritySchemeResponseBean() {
      this((PrioritySchemeWithPaginatedPrioritiesAndProjects)null, (TaskProgressBeanJsonNode)null, 3, (DefaultConstructorMarker)null);
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
