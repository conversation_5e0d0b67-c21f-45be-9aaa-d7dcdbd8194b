package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004B\u001f\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\b\u0010\tJ\u000b\u0010\r\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J!\u0010\u000f\u001a\u00020\u00002\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0002H\u00d6\u0001R\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000b\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/UpdateProjectsInSchemeRequestBean;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "add", "Lai/zencoder/generated/client/jira/models/PrioritySchemeChangesWithoutMappings;", "remove", "<init>", "(Lai/zencoder/generated/client/jira/models/PrioritySchemeChangesWithoutMappings;Lai/zencoder/generated/client/jira/models/PrioritySchemeChangesWithoutMappings;)V", "getAdd", "()Lai/zencoder/generated/client/jira/models/PrioritySchemeChangesWithoutMappings;", "getRemove", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class UpdateProjectsInSchemeRequestBean extends HashMap<String, Object> {
   @Nullable
   private final PrioritySchemeChangesWithoutMappings add;
   @Nullable
   private final PrioritySchemeChangesWithoutMappings remove;

   public UpdateProjectsInSchemeRequestBean(@Nullable PrioritySchemeChangesWithoutMappings add, @Nullable PrioritySchemeChangesWithoutMappings remove) {
      this.add = add;
      this.remove = remove;
   }

   @JsonProperty("add")
   @Nullable
   public final PrioritySchemeChangesWithoutMappings getAdd() {
      return this.add;
   }

   @JsonProperty("remove")
   @Nullable
   public final PrioritySchemeChangesWithoutMappings getRemove() {
      return this.remove;
   }

   @Nullable
   public final PrioritySchemeChangesWithoutMappings component1() {
      return this.add;
   }

   @Nullable
   public final PrioritySchemeChangesWithoutMappings component2() {
      return this.remove;
   }

   @NotNull
   public final UpdateProjectsInSchemeRequestBean copy(@Nullable PrioritySchemeChangesWithoutMappings add, @Nullable PrioritySchemeChangesWithoutMappings remove) {
      return new UpdateProjectsInSchemeRequestBean(add, remove);
   }

   @NotNull
   public String toString() {
      return "UpdateProjectsInSchemeRequestBean(add=" + this.add + ", remove=" + this.remove + ")";
   }

   public int hashCode() {
      int var1 = this.add == null ? 0 : this.add.hashCode();
      var1 = var1 * 31 + (this.remove == null ? 0 : this.remove.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof UpdateProjectsInSchemeRequestBean)) {
         return false;
      } else {
         UpdateProjectsInSchemeRequestBean var2 = (UpdateProjectsInSchemeRequestBean)other;
         if (!Intrinsics.areEqual(this.add, var2.add)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.remove, var2.remove);
         }
      }
   }

   public UpdateProjectsInSchemeRequestBean() {
      this((PrioritySchemeChangesWithoutMappings)null, (PrioritySchemeChangesWithoutMappings)null, 3, (DefaultConstructorMarker)null);
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
