package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J-\u0010\u0011\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/UpdateScreenSchemeDetails;", "", "description", "", "NAME", "screens", "Lai/zencoder/generated/client/jira/models/UpdateScreenTypes;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/UpdateScreenTypes;)V", "getDescription", "()Ljava/lang/String;", "getNAME", "getScreens", "()Lai/zencoder/generated/client/jira/models/UpdateScreenTypes;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class UpdateScreenSchemeDetails {
   @Nullable
   private final String description;
   @Nullable
   private final String NAME;
   @Nullable
   private final UpdateScreenTypes screens;

   public UpdateScreenSchemeDetails(@Nullable String description, @Nullable String NAME, @Nullable UpdateScreenTypes screens) {
      this.description = description;
      this.NAME = NAME;
      this.screens = screens;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("screens")
   @Nullable
   public final UpdateScreenTypes getScreens() {
      return this.screens;
   }

   @Nullable
   public final String component1() {
      return this.description;
   }

   @Nullable
   public final String component2() {
      return this.NAME;
   }

   @Nullable
   public final UpdateScreenTypes component3() {
      return this.screens;
   }

   @NotNull
   public final UpdateScreenSchemeDetails copy(@Nullable String description, @Nullable String NAME, @Nullable UpdateScreenTypes screens) {
      return new UpdateScreenSchemeDetails(description, NAME, screens);
   }

   @NotNull
   public String toString() {
      return "UpdateScreenSchemeDetails(description=" + this.description + ", NAME=" + this.NAME + ", screens=" + this.screens + ")";
   }

   public int hashCode() {
      int var1 = this.description == null ? 0 : this.description.hashCode();
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.screens == null ? 0 : this.screens.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof UpdateScreenSchemeDetails)) {
         return false;
      } else {
         UpdateScreenSchemeDetails var2 = (UpdateScreenSchemeDetails)other;
         if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.screens, var2.screens);
         }
      }
   }

   public UpdateScreenSchemeDetails() {
      this((String)null, (String)null, (UpdateScreenTypes)null, 7, (DefaultConstructorMarker)null);
   }
}
