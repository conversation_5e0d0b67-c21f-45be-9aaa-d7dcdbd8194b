package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\t\u0010\nJ\u0011\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J?\u0010\u0015\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u0006H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0015\u0010\b\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000e\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/jira/models/UpdateUiModificationDetails;", "", "contexts", "", "Lai/zencoder/generated/client/jira/models/UiModificationContextDetails;", "data", "", "description", "NAME", "<init>", "(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getContexts", "()Ljava/util/List;", "getData", "()Ljava/lang/String;", "getDescription", "getNAME", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class UpdateUiModificationDetails {
   @Nullable
   private final List<UiModificationContextDetails> contexts;
   @Nullable
   private final String data;
   @Nullable
   private final String description;
   @Nullable
   private final String NAME;

   public UpdateUiModificationDetails(@Nullable List<UiModificationContextDetails> contexts, @Nullable String data, @Nullable String description, @Nullable String NAME) {
      this.contexts = contexts;
      this.data = data;
      this.description = description;
      this.NAME = NAME;
   }

   @JsonProperty("contexts")
   @Nullable
   public final List<UiModificationContextDetails> getContexts() {
      return this.contexts;
   }

   @JsonProperty("data")
   @Nullable
   public final String getData() {
      return this.data;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @Nullable
   public final List<UiModificationContextDetails> component1() {
      return this.contexts;
   }

   @Nullable
   public final String component2() {
      return this.data;
   }

   @Nullable
   public final String component3() {
      return this.description;
   }

   @Nullable
   public final String component4() {
      return this.NAME;
   }

   @NotNull
   public final UpdateUiModificationDetails copy(@Nullable List<UiModificationContextDetails> contexts, @Nullable String data, @Nullable String description, @Nullable String NAME) {
      return new UpdateUiModificationDetails(contexts, data, description, NAME);
   }

   @NotNull
   public String toString() {
      return "UpdateUiModificationDetails(contexts=" + this.contexts + ", data=" + this.data + ", description=" + this.description + ", NAME=" + this.NAME + ")";
   }

   public int hashCode() {
      int var1 = this.contexts == null ? 0 : this.contexts.hashCode();
      var1 = var1 * 31 + (this.data == null ? 0 : this.data.hashCode());
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof UpdateUiModificationDetails)) {
         return false;
      } else {
         UpdateUiModificationDetails var2 = (UpdateUiModificationDetails)other;
         if (!Intrinsics.areEqual(this.contexts, var2.contexts)) {
            return false;
         } else if (!Intrinsics.areEqual(this.data, var2.data)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.NAME, var2.NAME);
         }
      }
   }

   public UpdateUiModificationDetails() {
      this((List)null, (String)null, (String)null, (String)null, 15, (DefaultConstructorMarker)null);
   }
}
