package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u0012B\u0019\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0005\u0010\u0006J\u0011\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u001b\u0010\n\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\b\u00a8\u0006\u0013"},
   d2 = {"Lai/zencoder/generated/client/jira/models/ValidationOptionsForUpdate;", "", "levels", "", "Lai/zencoder/generated/client/jira/models/ValidationOptionsForUpdate$Levels;", "<init>", "(Ljava/util/List;)V", "getLevels", "()Ljava/util/List;", "component1", "copy", "equals", "", "other", "hashCode", "", "toString", "", "Levels", "zencoder"}
)
public final class ValidationOptionsForUpdate {
   @Nullable
   private final List<Levels> levels;

   public ValidationOptionsForUpdate(@Nullable List<? extends Levels> levels) {
      this.levels = levels;
   }

   @JsonProperty("levels")
   @Nullable
   public final List<Levels> getLevels() {
      return this.levels;
   }

   @Nullable
   public final List<Levels> component1() {
      return this.levels;
   }

   @NotNull
   public final ValidationOptionsForUpdate copy(@Nullable List<? extends Levels> levels) {
      return new ValidationOptionsForUpdate(levels);
   }

   @NotNull
   public String toString() {
      return "ValidationOptionsForUpdate(levels=" + this.levels + ")";
   }

   public int hashCode() {
      return this.levels == null ? 0 : this.levels.hashCode();
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof ValidationOptionsForUpdate)) {
         return false;
      } else {
         ValidationOptionsForUpdate var2 = (ValidationOptionsForUpdate)other;
         return Intrinsics.areEqual(this.levels, var2.levels);
      }
   }

   public ValidationOptionsForUpdate() {
      this((List)null, 1, (DefaultConstructorMarker)null);
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/ValidationOptionsForUpdate$Levels;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "WARNING", "ERROR", "zencoder"}
   )
   public static enum Levels {
      @NotNull
      private final String value;
      @JsonProperty("WARNING")
      WARNING("WARNING"),
      @JsonProperty("ERROR")
      ERROR("ERROR");

      private Levels(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Levels> getEntries() {
         return $ENTRIES;
      }
   }
}
