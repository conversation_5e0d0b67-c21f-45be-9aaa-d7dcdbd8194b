package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.net.URI;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\nJ\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\nJ\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J2\u0010\u0012\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0013J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000b\u001a\u0004\b\t\u0010\nR\u0017\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000b\u001a\u0004\b\f\u0010\nR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u001b"},
   d2 = {"Lai/zencoder/generated/client/jira/models/VersionUnresolvedIssuesCount;", "", "issuesCount", "", "issuesUnresolvedCount", "self", "Ljava/net/URI;", "<init>", "(Ljava/lang/Long;Ljava/lang/Long;Ljava/net/URI;)V", "getIssuesCount", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getIssuesUnresolvedCount", "getSelf", "()Ljava/net/URI;", "component1", "component2", "component3", "copy", "(Ljava/lang/Long;Ljava/lang/Long;Ljava/net/URI;)Lai/zencoder/generated/client/jira/models/VersionUnresolvedIssuesCount;", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class VersionUnresolvedIssuesCount {
   @Nullable
   private final Long issuesCount;
   @Nullable
   private final Long issuesUnresolvedCount;
   @Nullable
   private final URI self;

   public VersionUnresolvedIssuesCount(@Nullable Long issuesCount, @Nullable Long issuesUnresolvedCount, @Nullable URI self) {
      this.issuesCount = issuesCount;
      this.issuesUnresolvedCount = issuesUnresolvedCount;
      this.self = self;
   }

   @JsonProperty("issuesCount")
   @Nullable
   public final Long getIssuesCount() {
      return this.issuesCount;
   }

   @JsonProperty("issuesUnresolvedCount")
   @Nullable
   public final Long getIssuesUnresolvedCount() {
      return this.issuesUnresolvedCount;
   }

   @JsonProperty("self")
   @Nullable
   public final URI getSelf() {
      return this.self;
   }

   @Nullable
   public final Long component1() {
      return this.issuesCount;
   }

   @Nullable
   public final Long component2() {
      return this.issuesUnresolvedCount;
   }

   @Nullable
   public final URI component3() {
      return this.self;
   }

   @NotNull
   public final VersionUnresolvedIssuesCount copy(@Nullable Long issuesCount, @Nullable Long issuesUnresolvedCount, @Nullable URI self) {
      return new VersionUnresolvedIssuesCount(issuesCount, issuesUnresolvedCount, self);
   }

   @NotNull
   public String toString() {
      return "VersionUnresolvedIssuesCount(issuesCount=" + this.issuesCount + ", issuesUnresolvedCount=" + this.issuesUnresolvedCount + ", self=" + this.self + ")";
   }

   public int hashCode() {
      int var1 = this.issuesCount == null ? 0 : this.issuesCount.hashCode();
      var1 = var1 * 31 + (this.issuesUnresolvedCount == null ? 0 : this.issuesUnresolvedCount.hashCode());
      var1 = var1 * 31 + (this.self == null ? 0 : this.self.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof VersionUnresolvedIssuesCount)) {
         return false;
      } else {
         VersionUnresolvedIssuesCount var2 = (VersionUnresolvedIssuesCount)other;
         if (!Intrinsics.areEqual(this.issuesCount, var2.issuesCount)) {
            return false;
         } else if (!Intrinsics.areEqual(this.issuesUnresolvedCount, var2.issuesUnresolvedCount)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.self, var2.self);
         }
      }
   }

   public VersionUnresolvedIssuesCount() {
      this((Long)null, (Long)null, (URI)null, 7, (DefaultConstructorMarker)null);
   }
}
