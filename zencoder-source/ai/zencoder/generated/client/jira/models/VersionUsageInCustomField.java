package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\nJ\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\nJ2\u0010\u0012\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0013J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0005H\u00d6\u0001R\u0017\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000b\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0017\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\n\n\u0002\u0010\u000b\u001a\u0004\b\u000e\u0010\n\u00a8\u0006\u001a"},
   d2 = {"Lai/zencoder/generated/client/jira/models/VersionUsageInCustomField;", "", "customFieldId", "", "fieldName", "", "issueCountWithVersionInCustomField", "<init>", "(Ljava/lang/Long;Ljava/lang/String;Ljava/lang/Long;)V", "getCustomFieldId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getFieldName", "()Ljava/lang/String;", "getIssueCountWithVersionInCustomField", "component1", "component2", "component3", "copy", "(Ljava/lang/Long;Ljava/lang/String;Ljava/lang/Long;)Lai/zencoder/generated/client/jira/models/VersionUsageInCustomField;", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class VersionUsageInCustomField {
   @Nullable
   private final Long customFieldId;
   @Nullable
   private final String fieldName;
   @Nullable
   private final Long issueCountWithVersionInCustomField;

   public VersionUsageInCustomField(@Nullable Long customFieldId, @Nullable String fieldName, @Nullable Long issueCountWithVersionInCustomField) {
      this.customFieldId = customFieldId;
      this.fieldName = fieldName;
      this.issueCountWithVersionInCustomField = issueCountWithVersionInCustomField;
   }

   @JsonProperty("customFieldId")
   @Nullable
   public final Long getCustomFieldId() {
      return this.customFieldId;
   }

   @JsonProperty("fieldName")
   @Nullable
   public final String getFieldName() {
      return this.fieldName;
   }

   @JsonProperty("issueCountWithVersionInCustomField")
   @Nullable
   public final Long getIssueCountWithVersionInCustomField() {
      return this.issueCountWithVersionInCustomField;
   }

   @Nullable
   public final Long component1() {
      return this.customFieldId;
   }

   @Nullable
   public final String component2() {
      return this.fieldName;
   }

   @Nullable
   public final Long component3() {
      return this.issueCountWithVersionInCustomField;
   }

   @NotNull
   public final VersionUsageInCustomField copy(@Nullable Long customFieldId, @Nullable String fieldName, @Nullable Long issueCountWithVersionInCustomField) {
      return new VersionUsageInCustomField(customFieldId, fieldName, issueCountWithVersionInCustomField);
   }

   @NotNull
   public String toString() {
      return "VersionUsageInCustomField(customFieldId=" + this.customFieldId + ", fieldName=" + this.fieldName + ", issueCountWithVersionInCustomField=" + this.issueCountWithVersionInCustomField + ")";
   }

   public int hashCode() {
      int var1 = this.customFieldId == null ? 0 : this.customFieldId.hashCode();
      var1 = var1 * 31 + (this.fieldName == null ? 0 : this.fieldName.hashCode());
      var1 = var1 * 31 + (this.issueCountWithVersionInCustomField == null ? 0 : this.issueCountWithVersionInCustomField.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof VersionUsageInCustomField)) {
         return false;
      } else {
         VersionUsageInCustomField var2 = (VersionUsageInCustomField)other;
         if (!Intrinsics.areEqual(this.customFieldId, var2.customFieldId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.fieldName, var2.fieldName)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.issueCountWithVersionInCustomField, var2.issueCountWithVersionInCustomField);
         }
      }
   }

   public VersionUsageInCustomField() {
      this((Long)null, (String)null, (Long)null, 7, (DefaultConstructorMarker)null);
   }
}
