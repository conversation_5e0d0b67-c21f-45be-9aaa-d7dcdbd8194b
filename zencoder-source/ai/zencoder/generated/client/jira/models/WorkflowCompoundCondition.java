package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u001bB%\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0004\b\t\u0010\nJ\u000f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\bH\u00c6\u0003J-\u0010\u0014\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0006H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0005\u001a\u00020\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0007\u001a\u00020\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u001c"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowCompoundCondition;", "", "conditions", "", "Lai/zencoder/generated/client/jira/models/WorkflowCondition;", "nodeType", "", "operator", "Lai/zencoder/generated/client/jira/models/WorkflowCompoundCondition$Operator;", "<init>", "(Ljava/util/List;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/WorkflowCompoundCondition$Operator;)V", "getConditions", "()Ljava/util/List;", "getNodeType", "()Ljava/lang/String;", "getOperator", "()Lai/zencoder/generated/client/jira/models/WorkflowCompoundCondition$Operator;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "Operator", "zencoder"}
)
public final class WorkflowCompoundCondition {
   @NotNull
   private final List<WorkflowCondition> conditions;
   @NotNull
   private final String nodeType;
   @NotNull
   private final Operator operator;

   public WorkflowCompoundCondition(@NotNull List<? extends WorkflowCondition> conditions, @NotNull String nodeType, @NotNull Operator operator) {
      Intrinsics.checkNotNullParameter(conditions, "");
      Intrinsics.checkNotNullParameter(nodeType, "");
      Intrinsics.checkNotNullParameter(operator, "");
      super();
      this.conditions = conditions;
      this.nodeType = nodeType;
      this.operator = operator;
   }

   @JsonProperty("conditions")
   @NotNull
   public final List<WorkflowCondition> getConditions() {
      return this.conditions;
   }

   @JsonProperty("nodeType")
   @NotNull
   public final String getNodeType() {
      return this.nodeType;
   }

   @JsonProperty("operator")
   @NotNull
   public final Operator getOperator() {
      return this.operator;
   }

   @NotNull
   public final List<WorkflowCondition> component1() {
      return this.conditions;
   }

   @NotNull
   public final String component2() {
      return this.nodeType;
   }

   @NotNull
   public final Operator component3() {
      return this.operator;
   }

   @NotNull
   public final WorkflowCompoundCondition copy(@NotNull List<? extends WorkflowCondition> conditions, @NotNull String nodeType, @NotNull Operator operator) {
      Intrinsics.checkNotNullParameter(conditions, "");
      Intrinsics.checkNotNullParameter(nodeType, "");
      Intrinsics.checkNotNullParameter(operator, "");
      return new WorkflowCompoundCondition(conditions, nodeType, operator);
   }

   @NotNull
   public String toString() {
      return "WorkflowCompoundCondition(conditions=" + this.conditions + ", nodeType=" + this.nodeType + ", operator=" + this.operator + ")";
   }

   public int hashCode() {
      int var1 = this.conditions.hashCode();
      var1 = var1 * 31 + this.nodeType.hashCode();
      var1 = var1 * 31 + this.operator.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowCompoundCondition)) {
         return false;
      } else {
         WorkflowCompoundCondition var2 = (WorkflowCompoundCondition)other;
         if (!Intrinsics.areEqual(this.conditions, var2.conditions)) {
            return false;
         } else if (!Intrinsics.areEqual(this.nodeType, var2.nodeType)) {
            return false;
         } else {
            return this.operator == var2.operator;
         }
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowCompoundCondition$Operator;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "AND", "OR", "zencoder"}
   )
   public static enum Operator {
      @NotNull
      private final String value;
      @JsonProperty("AND")
      AND("AND"),
      @JsonProperty("OR")
      OR("OR");

      private Operator(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Operator> getEntries() {
         return $ENTRIES;
      }
   }
}
