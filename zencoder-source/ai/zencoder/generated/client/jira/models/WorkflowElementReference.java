package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BC\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\t\u0010\nJ\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003JE\u0010\u0017\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\fR\u0015\u0010\b\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\f\u00a8\u0006\u001e"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowElementReference;", "", "propertyKey", "", "ruleId", "statusMappingReference", "Lai/zencoder/generated/client/jira/models/ProjectAndIssueTypePair;", "statusReference", "transitionId", "<init>", "(Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/ProjectAndIssueTypePair;Ljava/lang/String;Ljava/lang/String;)V", "getPropertyKey", "()Ljava/lang/String;", "getRuleId", "getStatusMappingReference", "()Lai/zencoder/generated/client/jira/models/ProjectAndIssueTypePair;", "getStatusReference", "getTransitionId", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class WorkflowElementReference {
   @Nullable
   private final String propertyKey;
   @Nullable
   private final String ruleId;
   @Nullable
   private final ProjectAndIssueTypePair statusMappingReference;
   @Nullable
   private final String statusReference;
   @Nullable
   private final String transitionId;

   public WorkflowElementReference(@Nullable String propertyKey, @Nullable String ruleId, @Nullable ProjectAndIssueTypePair statusMappingReference, @Nullable String statusReference, @Nullable String transitionId) {
      this.propertyKey = propertyKey;
      this.ruleId = ruleId;
      this.statusMappingReference = statusMappingReference;
      this.statusReference = statusReference;
      this.transitionId = transitionId;
   }

   @JsonProperty("propertyKey")
   @Nullable
   public final String getPropertyKey() {
      return this.propertyKey;
   }

   @JsonProperty("ruleId")
   @Nullable
   public final String getRuleId() {
      return this.ruleId;
   }

   @JsonProperty("statusMappingReference")
   @Nullable
   public final ProjectAndIssueTypePair getStatusMappingReference() {
      return this.statusMappingReference;
   }

   @JsonProperty("statusReference")
   @Nullable
   public final String getStatusReference() {
      return this.statusReference;
   }

   @JsonProperty("transitionId")
   @Nullable
   public final String getTransitionId() {
      return this.transitionId;
   }

   @Nullable
   public final String component1() {
      return this.propertyKey;
   }

   @Nullable
   public final String component2() {
      return this.ruleId;
   }

   @Nullable
   public final ProjectAndIssueTypePair component3() {
      return this.statusMappingReference;
   }

   @Nullable
   public final String component4() {
      return this.statusReference;
   }

   @Nullable
   public final String component5() {
      return this.transitionId;
   }

   @NotNull
   public final WorkflowElementReference copy(@Nullable String propertyKey, @Nullable String ruleId, @Nullable ProjectAndIssueTypePair statusMappingReference, @Nullable String statusReference, @Nullable String transitionId) {
      return new WorkflowElementReference(propertyKey, ruleId, statusMappingReference, statusReference, transitionId);
   }

   @NotNull
   public String toString() {
      return "WorkflowElementReference(propertyKey=" + this.propertyKey + ", ruleId=" + this.ruleId + ", statusMappingReference=" + this.statusMappingReference + ", statusReference=" + this.statusReference + ", transitionId=" + this.transitionId + ")";
   }

   public int hashCode() {
      int var1 = this.propertyKey == null ? 0 : this.propertyKey.hashCode();
      var1 = var1 * 31 + (this.ruleId == null ? 0 : this.ruleId.hashCode());
      var1 = var1 * 31 + (this.statusMappingReference == null ? 0 : this.statusMappingReference.hashCode());
      var1 = var1 * 31 + (this.statusReference == null ? 0 : this.statusReference.hashCode());
      var1 = var1 * 31 + (this.transitionId == null ? 0 : this.transitionId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowElementReference)) {
         return false;
      } else {
         WorkflowElementReference var2 = (WorkflowElementReference)other;
         if (!Intrinsics.areEqual(this.propertyKey, var2.propertyKey)) {
            return false;
         } else if (!Intrinsics.areEqual(this.ruleId, var2.ruleId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.statusMappingReference, var2.statusMappingReference)) {
            return false;
         } else if (!Intrinsics.areEqual(this.statusReference, var2.statusReference)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.transitionId, var2.transitionId);
         }
      }
   }

   public WorkflowElementReference() {
      this((String)null, (String)null, (ProjectAndIssueTypePair)null, (String)null, (String)null, 31, (DefaultConstructorMarker)null);
   }
}
