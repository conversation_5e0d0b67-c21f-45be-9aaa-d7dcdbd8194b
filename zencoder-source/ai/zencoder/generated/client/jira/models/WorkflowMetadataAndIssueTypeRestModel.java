package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0006H\u00c6\u0003J#\u0010\u000f\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0004H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0005\u001a\u00020\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowMetadataAndIssueTypeRestModel;", "", "issueTypeIds", "", "", "workflow", "Lai/zencoder/generated/client/jira/models/WorkflowMetadataRestModel;", "<init>", "(Ljava/util/List;Lai/zencoder/generated/client/jira/models/WorkflowMetadataRestModel;)V", "getIssueTypeIds", "()Ljava/util/List;", "getWorkflow", "()Lai/zencoder/generated/client/jira/models/WorkflowMetadataRestModel;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class WorkflowMetadataAndIssueTypeRestModel {
   @NotNull
   private final List<String> issueTypeIds;
   @NotNull
   private final WorkflowMetadataRestModel workflow;

   public WorkflowMetadataAndIssueTypeRestModel(@NotNull List<String> issueTypeIds, @NotNull WorkflowMetadataRestModel workflow) {
      Intrinsics.checkNotNullParameter(issueTypeIds, "");
      Intrinsics.checkNotNullParameter(workflow, "");
      super();
      this.issueTypeIds = issueTypeIds;
      this.workflow = workflow;
   }

   @JsonProperty("issueTypeIds")
   @NotNull
   public final List<String> getIssueTypeIds() {
      return this.issueTypeIds;
   }

   @JsonProperty("workflow")
   @NotNull
   public final WorkflowMetadataRestModel getWorkflow() {
      return this.workflow;
   }

   @NotNull
   public final List<String> component1() {
      return this.issueTypeIds;
   }

   @NotNull
   public final WorkflowMetadataRestModel component2() {
      return this.workflow;
   }

   @NotNull
   public final WorkflowMetadataAndIssueTypeRestModel copy(@NotNull List<String> issueTypeIds, @NotNull WorkflowMetadataRestModel workflow) {
      Intrinsics.checkNotNullParameter(issueTypeIds, "");
      Intrinsics.checkNotNullParameter(workflow, "");
      return new WorkflowMetadataAndIssueTypeRestModel(issueTypeIds, workflow);
   }

   @NotNull
   public String toString() {
      return "WorkflowMetadataAndIssueTypeRestModel(issueTypeIds=" + this.issueTypeIds + ", workflow=" + this.workflow + ")";
   }

   public int hashCode() {
      int var1 = this.issueTypeIds.hashCode();
      var1 = var1 * 31 + this.workflow.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowMetadataAndIssueTypeRestModel)) {
         return false;
      } else {
         WorkflowMetadataAndIssueTypeRestModel var2 = (WorkflowMetadataAndIssueTypeRestModel)other;
         if (!Intrinsics.areEqual(this.issueTypeIds, var2.issueTypeIds)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.workflow, var2.workflow);
         }
      }
   }
}
