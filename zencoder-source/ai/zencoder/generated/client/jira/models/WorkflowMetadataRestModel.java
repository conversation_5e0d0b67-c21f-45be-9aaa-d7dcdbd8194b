package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B9\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u0012\u0006\u0010\b\u001a\u00020\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u000b\u0010\fJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\tH\u00c6\u0003J\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003JC\u0010\u001a\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\b\b\u0002\u0010\b\u001a\u00020\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u001b\u001a\u00020\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001J\t\u0010 \u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0019\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\b\u001a\u00020\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0015\u0010\n\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u000e\u00a8\u0006!"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowMetadataRestModel;", "", "description", "", "id", "usage", "", "Lai/zencoder/generated/client/jira/models/SimpleUsage;", "version", "Lai/zencoder/generated/client/jira/models/DocumentVersion;", "NAME", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Lai/zencoder/generated/client/jira/models/DocumentVersion;Ljava/lang/String;)V", "getDescription", "()Ljava/lang/String;", "getId", "getUsage", "()Ljava/util/List;", "getVersion", "()Lai/zencoder/generated/client/jira/models/DocumentVersion;", "getNAME", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class WorkflowMetadataRestModel {
   @NotNull
   private final String description;
   @NotNull
   private final String id;
   @NotNull
   private final List<SimpleUsage> usage;
   @NotNull
   private final DocumentVersion version;
   @Nullable
   private final String NAME;

   public WorkflowMetadataRestModel(@NotNull String description, @NotNull String id, @NotNull List<SimpleUsage> usage, @NotNull DocumentVersion version, @Nullable String NAME) {
      Intrinsics.checkNotNullParameter(description, "");
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(usage, "");
      Intrinsics.checkNotNullParameter(version, "");
      super();
      this.description = description;
      this.id = id;
      this.usage = usage;
      this.version = version;
      this.NAME = NAME;
   }

   @JsonProperty("description")
   @NotNull
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("usage")
   @NotNull
   public final List<SimpleUsage> getUsage() {
      return this.usage;
   }

   @JsonProperty("version")
   @NotNull
   public final DocumentVersion getVersion() {
      return this.version;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @NotNull
   public final String component1() {
      return this.description;
   }

   @NotNull
   public final String component2() {
      return this.id;
   }

   @NotNull
   public final List<SimpleUsage> component3() {
      return this.usage;
   }

   @NotNull
   public final DocumentVersion component4() {
      return this.version;
   }

   @Nullable
   public final String component5() {
      return this.NAME;
   }

   @NotNull
   public final WorkflowMetadataRestModel copy(@NotNull String description, @NotNull String id, @NotNull List<SimpleUsage> usage, @NotNull DocumentVersion version, @Nullable String NAME) {
      Intrinsics.checkNotNullParameter(description, "");
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(usage, "");
      Intrinsics.checkNotNullParameter(version, "");
      return new WorkflowMetadataRestModel(description, id, usage, version, NAME);
   }

   @NotNull
   public String toString() {
      return "WorkflowMetadataRestModel(description=" + this.description + ", id=" + this.id + ", usage=" + this.usage + ", version=" + this.version + ", NAME=" + this.NAME + ")";
   }

   public int hashCode() {
      int var1 = this.description.hashCode();
      var1 = var1 * 31 + this.id.hashCode();
      var1 = var1 * 31 + this.usage.hashCode();
      var1 = var1 * 31 + this.version.hashCode();
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowMetadataRestModel)) {
         return false;
      } else {
         WorkflowMetadataRestModel var2 = (WorkflowMetadataRestModel)other;
         if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.usage, var2.usage)) {
            return false;
         } else if (!Intrinsics.areEqual(this.version, var2.version)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.NAME, var2.NAME);
         }
      }
   }
}
