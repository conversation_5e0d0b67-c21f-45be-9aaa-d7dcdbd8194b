package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B3\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\u0016\b\u0002\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0017\u0010\u0010\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0006H\u00c6\u0003J7\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\u0016\b\u0002\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR!\u0010\u0005\u001a\u0010\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowRuleConfiguration;", "", "ruleKey", "", "id", "parameters", "", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V", "getRuleKey", "()Ljava/lang/String;", "getId", "getParameters", "()Ljava/util/Map;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class WorkflowRuleConfiguration {
   @NotNull
   private final String ruleKey;
   @Nullable
   private final String id;
   @Nullable
   private final Map<String, String> parameters;

   public WorkflowRuleConfiguration(@NotNull String ruleKey, @Nullable String id, @Nullable Map<String, String> parameters) {
      Intrinsics.checkNotNullParameter(ruleKey, "");
      super();
      this.ruleKey = ruleKey;
      this.id = id;
      this.parameters = parameters;
   }

   @JsonProperty("ruleKey")
   @NotNull
   public final String getRuleKey() {
      return this.ruleKey;
   }

   @JsonProperty("id")
   @Nullable
   public final String getId() {
      return this.id;
   }

   @JsonProperty("parameters")
   @Nullable
   public final Map<String, String> getParameters() {
      return this.parameters;
   }

   @NotNull
   public final String component1() {
      return this.ruleKey;
   }

   @Nullable
   public final String component2() {
      return this.id;
   }

   @Nullable
   public final Map<String, String> component3() {
      return this.parameters;
   }

   @NotNull
   public final WorkflowRuleConfiguration copy(@NotNull String ruleKey, @Nullable String id, @Nullable Map<String, String> parameters) {
      Intrinsics.checkNotNullParameter(ruleKey, "");
      return new WorkflowRuleConfiguration(ruleKey, id, parameters);
   }

   @NotNull
   public String toString() {
      return "WorkflowRuleConfiguration(ruleKey=" + this.ruleKey + ", id=" + this.id + ", parameters=" + this.parameters + ")";
   }

   public int hashCode() {
      int var1 = this.ruleKey.hashCode();
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      var1 = var1 * 31 + (this.parameters == null ? 0 : this.parameters.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowRuleConfiguration)) {
         return false;
      } else {
         WorkflowRuleConfiguration var2 = (WorkflowRuleConfiguration)other;
         if (!Intrinsics.areEqual(this.ruleKey, var2.ruleKey)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.parameters, var2.parameters);
         }
      }
   }
}
