package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\b\b\u0010\tJ\u0011\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0004H\u00c6\u0003J9\u0010\u0012\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0004H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u001a"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowRulesSearchDetails;", "", "invalidRules", "", "Ljava/util/UUID;", "validRules", "Lai/zencoder/generated/client/jira/models/WorkflowTransitionRules;", "workflowEntityId", "<init>", "(Ljava/util/List;Ljava/util/List;Ljava/util/UUID;)V", "getInvalidRules", "()Ljava/util/List;", "getValidRules", "getWorkflowEntityId", "()Ljava/util/UUID;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class WorkflowRulesSearchDetails {
   @Nullable
   private final List<UUID> invalidRules;
   @Nullable
   private final List<WorkflowTransitionRules> validRules;
   @Nullable
   private final UUID workflowEntityId;

   public WorkflowRulesSearchDetails(@Nullable List<UUID> invalidRules, @Nullable List<WorkflowTransitionRules> validRules, @Nullable UUID workflowEntityId) {
      this.invalidRules = invalidRules;
      this.validRules = validRules;
      this.workflowEntityId = workflowEntityId;
   }

   @JsonProperty("invalidRules")
   @Nullable
   public final List<UUID> getInvalidRules() {
      return this.invalidRules;
   }

   @JsonProperty("validRules")
   @Nullable
   public final List<WorkflowTransitionRules> getValidRules() {
      return this.validRules;
   }

   @JsonProperty("workflowEntityId")
   @Nullable
   public final UUID getWorkflowEntityId() {
      return this.workflowEntityId;
   }

   @Nullable
   public final List<UUID> component1() {
      return this.invalidRules;
   }

   @Nullable
   public final List<WorkflowTransitionRules> component2() {
      return this.validRules;
   }

   @Nullable
   public final UUID component3() {
      return this.workflowEntityId;
   }

   @NotNull
   public final WorkflowRulesSearchDetails copy(@Nullable List<UUID> invalidRules, @Nullable List<WorkflowTransitionRules> validRules, @Nullable UUID workflowEntityId) {
      return new WorkflowRulesSearchDetails(invalidRules, validRules, workflowEntityId);
   }

   @NotNull
   public String toString() {
      return "WorkflowRulesSearchDetails(invalidRules=" + this.invalidRules + ", validRules=" + this.validRules + ", workflowEntityId=" + this.workflowEntityId + ")";
   }

   public int hashCode() {
      int var1 = this.invalidRules == null ? 0 : this.invalidRules.hashCode();
      var1 = var1 * 31 + (this.validRules == null ? 0 : this.validRules.hashCode());
      var1 = var1 * 31 + (this.workflowEntityId == null ? 0 : this.workflowEntityId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowRulesSearchDetails)) {
         return false;
      } else {
         WorkflowRulesSearchDetails var2 = (WorkflowRulesSearchDetails)other;
         if (!Intrinsics.areEqual(this.invalidRules, var2.invalidRules)) {
            return false;
         } else if (!Intrinsics.areEqual(this.validRules, var2.validRules)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.workflowEntityId, var2.workflowEntityId);
         }
      }
   }

   public WorkflowRulesSearchDetails() {
      this((List)null, (List)null, (UUID)null, 7, (DefaultConstructorMarker)null);
   }
}
