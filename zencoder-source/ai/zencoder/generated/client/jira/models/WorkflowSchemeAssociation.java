package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0004H\u00c6\u0003J#\u0010\u000e\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0004H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0004H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0005\u001a\u00020\u00048G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowSchemeAssociation;", "", "issueTypeIds", "", "", "workflowId", "<init>", "(Ljava/util/Set;Ljava/lang/String;)V", "getIssueTypeIds", "()Ljava/util/Set;", "getWorkflowId", "()Ljava/lang/String;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class WorkflowSchemeAssociation {
   @NotNull
   private final Set<String> issueTypeIds;
   @NotNull
   private final String workflowId;

   public WorkflowSchemeAssociation(@NotNull Set<String> issueTypeIds, @NotNull String workflowId) {
      Intrinsics.checkNotNullParameter(issueTypeIds, "");
      Intrinsics.checkNotNullParameter(workflowId, "");
      super();
      this.issueTypeIds = issueTypeIds;
      this.workflowId = workflowId;
   }

   @JsonProperty("issueTypeIds")
   @NotNull
   public final Set<String> getIssueTypeIds() {
      return this.issueTypeIds;
   }

   @JsonProperty("workflowId")
   @NotNull
   public final String getWorkflowId() {
      return this.workflowId;
   }

   @NotNull
   public final Set<String> component1() {
      return this.issueTypeIds;
   }

   @NotNull
   public final String component2() {
      return this.workflowId;
   }

   @NotNull
   public final WorkflowSchemeAssociation copy(@NotNull Set<String> issueTypeIds, @NotNull String workflowId) {
      Intrinsics.checkNotNullParameter(issueTypeIds, "");
      Intrinsics.checkNotNullParameter(workflowId, "");
      return new WorkflowSchemeAssociation(issueTypeIds, workflowId);
   }

   @NotNull
   public String toString() {
      return "WorkflowSchemeAssociation(issueTypeIds=" + this.issueTypeIds + ", workflowId=" + this.workflowId + ")";
   }

   public int hashCode() {
      int var1 = this.issueTypeIds.hashCode();
      var1 = var1 * 31 + this.workflowId.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowSchemeAssociation)) {
         return false;
      } else {
         WorkflowSchemeAssociation var2 = (WorkflowSchemeAssociation)other;
         if (!Intrinsics.areEqual(this.issueTypeIds, var2.issueTypeIds)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.workflowId, var2.workflowId);
         }
      }
   }
}
