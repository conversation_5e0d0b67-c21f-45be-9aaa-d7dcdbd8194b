package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bk\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0005\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0011\u0010\u0012J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005H\u00c6\u0003J\t\u0010#\u001a\u00020\u0007H\u00c6\u0003J\t\u0010$\u001a\u00020\tH\u00c6\u0003J\u000f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0005H\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\u000b\u0010'\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010(\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010)\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003Jw\u0010*\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00052\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010+\u001a\u00020,2\b\u0010-\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010.\u001a\u00020/H\u00d6\u0001J\t\u00100\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\u0006\u001a\u00020\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0013\u0010\b\u001a\u00020\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0019\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0016R\u0015\u0010\f\u001a\u0004\u0018\u00010\r8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0015\u0010\u000e\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0014R\u0015\u0010\u000f\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0014R\u0015\u0010\u0010\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0014\u00a8\u00061"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowSchemeReadResponse;", "", "id", "", "projectIdsUsingScheme", "", "scope", "Lai/zencoder/generated/client/jira/models/WorkflowScope;", "version", "Lai/zencoder/generated/client/jira/models/DocumentVersion;", "workflowsForIssueTypes", "Lai/zencoder/generated/client/jira/models/WorkflowMetadataAndIssueTypeRestModel;", "defaultWorkflow", "Lai/zencoder/generated/client/jira/models/WorkflowMetadataRestModel;", "description", "NAME", "taskId", "<init>", "(Ljava/lang/String;Ljava/util/List;Lai/zencoder/generated/client/jira/models/WorkflowScope;Lai/zencoder/generated/client/jira/models/DocumentVersion;Ljava/util/List;Lai/zencoder/generated/client/jira/models/WorkflowMetadataRestModel;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getId", "()Ljava/lang/String;", "getProjectIdsUsingScheme", "()Ljava/util/List;", "getScope", "()Lai/zencoder/generated/client/jira/models/WorkflowScope;", "getVersion", "()Lai/zencoder/generated/client/jira/models/DocumentVersion;", "getWorkflowsForIssueTypes", "getDefaultWorkflow", "()Lai/zencoder/generated/client/jira/models/WorkflowMetadataRestModel;", "getDescription", "getNAME", "getTaskId", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class WorkflowSchemeReadResponse {
   @NotNull
   private final String id;
   @NotNull
   private final List<String> projectIdsUsingScheme;
   @NotNull
   private final WorkflowScope scope;
   @NotNull
   private final DocumentVersion version;
   @NotNull
   private final List<WorkflowMetadataAndIssueTypeRestModel> workflowsForIssueTypes;
   @Nullable
   private final WorkflowMetadataRestModel defaultWorkflow;
   @Nullable
   private final String description;
   @Nullable
   private final String NAME;
   @Nullable
   private final String taskId;

   public WorkflowSchemeReadResponse(@NotNull String id, @NotNull List<String> projectIdsUsingScheme, @NotNull WorkflowScope scope, @NotNull DocumentVersion version, @NotNull List<WorkflowMetadataAndIssueTypeRestModel> workflowsForIssueTypes, @Nullable WorkflowMetadataRestModel defaultWorkflow, @Nullable String description, @Nullable String NAME, @Nullable String taskId) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(projectIdsUsingScheme, "");
      Intrinsics.checkNotNullParameter(scope, "");
      Intrinsics.checkNotNullParameter(version, "");
      Intrinsics.checkNotNullParameter(workflowsForIssueTypes, "");
      super();
      this.id = id;
      this.projectIdsUsingScheme = projectIdsUsingScheme;
      this.scope = scope;
      this.version = version;
      this.workflowsForIssueTypes = workflowsForIssueTypes;
      this.defaultWorkflow = defaultWorkflow;
      this.description = description;
      this.NAME = NAME;
      this.taskId = taskId;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("projectIdsUsingScheme")
   @NotNull
   public final List<String> getProjectIdsUsingScheme() {
      return this.projectIdsUsingScheme;
   }

   @JsonProperty("scope")
   @NotNull
   public final WorkflowScope getScope() {
      return this.scope;
   }

   @JsonProperty("version")
   @NotNull
   public final DocumentVersion getVersion() {
      return this.version;
   }

   @JsonProperty("workflowsForIssueTypes")
   @NotNull
   public final List<WorkflowMetadataAndIssueTypeRestModel> getWorkflowsForIssueTypes() {
      return this.workflowsForIssueTypes;
   }

   @JsonProperty("defaultWorkflow")
   @Nullable
   public final WorkflowMetadataRestModel getDefaultWorkflow() {
      return this.defaultWorkflow;
   }

   @JsonProperty("description")
   @Nullable
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("taskId")
   @Nullable
   public final String getTaskId() {
      return this.taskId;
   }

   @NotNull
   public final String component1() {
      return this.id;
   }

   @NotNull
   public final List<String> component2() {
      return this.projectIdsUsingScheme;
   }

   @NotNull
   public final WorkflowScope component3() {
      return this.scope;
   }

   @NotNull
   public final DocumentVersion component4() {
      return this.version;
   }

   @NotNull
   public final List<WorkflowMetadataAndIssueTypeRestModel> component5() {
      return this.workflowsForIssueTypes;
   }

   @Nullable
   public final WorkflowMetadataRestModel component6() {
      return this.defaultWorkflow;
   }

   @Nullable
   public final String component7() {
      return this.description;
   }

   @Nullable
   public final String component8() {
      return this.NAME;
   }

   @Nullable
   public final String component9() {
      return this.taskId;
   }

   @NotNull
   public final WorkflowSchemeReadResponse copy(@NotNull String id, @NotNull List<String> projectIdsUsingScheme, @NotNull WorkflowScope scope, @NotNull DocumentVersion version, @NotNull List<WorkflowMetadataAndIssueTypeRestModel> workflowsForIssueTypes, @Nullable WorkflowMetadataRestModel defaultWorkflow, @Nullable String description, @Nullable String NAME, @Nullable String taskId) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(projectIdsUsingScheme, "");
      Intrinsics.checkNotNullParameter(scope, "");
      Intrinsics.checkNotNullParameter(version, "");
      Intrinsics.checkNotNullParameter(workflowsForIssueTypes, "");
      return new WorkflowSchemeReadResponse(id, projectIdsUsingScheme, scope, version, workflowsForIssueTypes, defaultWorkflow, description, NAME, taskId);
   }

   @NotNull
   public String toString() {
      return "WorkflowSchemeReadResponse(id=" + this.id + ", projectIdsUsingScheme=" + this.projectIdsUsingScheme + ", scope=" + this.scope + ", version=" + this.version + ", workflowsForIssueTypes=" + this.workflowsForIssueTypes + ", defaultWorkflow=" + this.defaultWorkflow + ", description=" + this.description + ", NAME=" + this.NAME + ", taskId=" + this.taskId + ")";
   }

   public int hashCode() {
      int var1 = this.id.hashCode();
      var1 = var1 * 31 + this.projectIdsUsingScheme.hashCode();
      var1 = var1 * 31 + this.scope.hashCode();
      var1 = var1 * 31 + this.version.hashCode();
      var1 = var1 * 31 + this.workflowsForIssueTypes.hashCode();
      var1 = var1 * 31 + (this.defaultWorkflow == null ? 0 : this.defaultWorkflow.hashCode());
      var1 = var1 * 31 + (this.description == null ? 0 : this.description.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.taskId == null ? 0 : this.taskId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowSchemeReadResponse)) {
         return false;
      } else {
         WorkflowSchemeReadResponse var2 = (WorkflowSchemeReadResponse)other;
         if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.projectIdsUsingScheme, var2.projectIdsUsingScheme)) {
            return false;
         } else if (!Intrinsics.areEqual(this.scope, var2.scope)) {
            return false;
         } else if (!Intrinsics.areEqual(this.version, var2.version)) {
            return false;
         } else if (!Intrinsics.areEqual(this.workflowsForIssueTypes, var2.workflowsForIssueTypes)) {
            return false;
         } else if (!Intrinsics.areEqual(this.defaultWorkflow, var2.defaultWorkflow)) {
            return false;
         } else if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.taskId, var2.taskId);
         }
      }
   }
}
