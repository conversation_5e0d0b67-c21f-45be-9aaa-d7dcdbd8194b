package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004Bm\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0002\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0002\u0012\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\r\u0018\u00010\f\u0012\u0010\b\u0002\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u000f\u0018\u00010\f\u0012\u0010\b\u0002\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\f\u00a2\u0006\u0004\b\u0012\u0010\u0013J\t\u0010\u001f\u001a\u00020\u0002H\u00c6\u0003J\t\u0010 \u001a\u00020\u0002H\u00c6\u0003J\t\u0010!\u001a\u00020\bH\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u0011\u0010$\u001a\n\u0012\u0004\u0012\u00020\r\u0018\u00010\fH\u00c6\u0003J\u0011\u0010%\u001a\n\u0012\u0004\u0012\u00020\u000f\u0018\u00010\fH\u00c6\u0003J\u0011\u0010&\u001a\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\fH\u00c6\u0003Ju\u0010'\u001a\u00020\u00002\b\b\u0002\u0010\u0005\u001a\u00020\u00022\b\b\u0002\u0010\u0006\u001a\u00020\u00022\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00022\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\r\u0018\u00010\f2\u0010\b\u0002\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u000f\u0018\u00010\f2\u0010\b\u0002\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\fH\u00c6\u0001J\u0013\u0010(\u001a\u00020)2\b\u0010*\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010+\u001a\u00020,H\u00d6\u0001J\t\u0010-\u001a\u00020\u0002H\u00d6\u0001R\u0013\u0010\u0005\u001a\u00020\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0013\u0010\u0006\u001a\u00020\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u0013\u0010\u0007\u001a\u00020\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0015\u0010\t\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0015R\u0015\u0010\n\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0015R\u001b\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\r\u0018\u00010\f8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u001b\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u000f\u0018\u00010\f8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001cR\u001b\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\f8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001c\u00a8\u0006."},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowSchemeUpdateRequest;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "description", "id", "version", "Lai/zencoder/generated/client/jira/models/DocumentVersion;", "defaultWorkflowId", "NAME", "statusMappingsByIssueTypeOverride", "", "Lai/zencoder/generated/client/jira/models/MappingsByIssueTypeOverride;", "statusMappingsByWorkflows", "Lai/zencoder/generated/client/jira/models/MappingsByWorkflow;", "workflowsForIssueTypes", "Lai/zencoder/generated/client/jira/models/WorkflowSchemeAssociation;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/DocumentVersion;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getDescription", "()Ljava/lang/String;", "getId", "getVersion", "()Lai/zencoder/generated/client/jira/models/DocumentVersion;", "getDefaultWorkflowId", "getNAME", "getStatusMappingsByIssueTypeOverride", "()Ljava/util/List;", "getStatusMappingsByWorkflows", "getWorkflowsForIssueTypes", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class WorkflowSchemeUpdateRequest extends HashMap<String, Object> {
   @NotNull
   private final String description;
   @NotNull
   private final String id;
   @NotNull
   private final DocumentVersion version;
   @Nullable
   private final String defaultWorkflowId;
   @Nullable
   private final String NAME;
   @Nullable
   private final List<MappingsByIssueTypeOverride> statusMappingsByIssueTypeOverride;
   @Nullable
   private final List<MappingsByWorkflow> statusMappingsByWorkflows;
   @Nullable
   private final List<WorkflowSchemeAssociation> workflowsForIssueTypes;

   public WorkflowSchemeUpdateRequest(@NotNull String description, @NotNull String id, @NotNull DocumentVersion version, @Nullable String defaultWorkflowId, @Nullable String NAME, @Nullable List<MappingsByIssueTypeOverride> statusMappingsByIssueTypeOverride, @Nullable List<MappingsByWorkflow> statusMappingsByWorkflows, @Nullable List<WorkflowSchemeAssociation> workflowsForIssueTypes) {
      Intrinsics.checkNotNullParameter(description, "");
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(version, "");
      super();
      this.description = description;
      this.id = id;
      this.version = version;
      this.defaultWorkflowId = defaultWorkflowId;
      this.NAME = NAME;
      this.statusMappingsByIssueTypeOverride = statusMappingsByIssueTypeOverride;
      this.statusMappingsByWorkflows = statusMappingsByWorkflows;
      this.workflowsForIssueTypes = workflowsForIssueTypes;
   }

   @JsonProperty("description")
   @NotNull
   public final String getDescription() {
      return this.description;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("version")
   @NotNull
   public final DocumentVersion getVersion() {
      return this.version;
   }

   @JsonProperty("defaultWorkflowId")
   @Nullable
   public final String getDefaultWorkflowId() {
      return this.defaultWorkflowId;
   }

   @JsonProperty("NAME")
   @Nullable
   public final String getNAME() {
      return this.NAME;
   }

   @JsonProperty("statusMappingsByIssueTypeOverride")
   @Nullable
   public final List<MappingsByIssueTypeOverride> getStatusMappingsByIssueTypeOverride() {
      return this.statusMappingsByIssueTypeOverride;
   }

   @JsonProperty("statusMappingsByWorkflows")
   @Nullable
   public final List<MappingsByWorkflow> getStatusMappingsByWorkflows() {
      return this.statusMappingsByWorkflows;
   }

   @JsonProperty("workflowsForIssueTypes")
   @Nullable
   public final List<WorkflowSchemeAssociation> getWorkflowsForIssueTypes() {
      return this.workflowsForIssueTypes;
   }

   @NotNull
   public final String component1() {
      return this.description;
   }

   @NotNull
   public final String component2() {
      return this.id;
   }

   @NotNull
   public final DocumentVersion component3() {
      return this.version;
   }

   @Nullable
   public final String component4() {
      return this.defaultWorkflowId;
   }

   @Nullable
   public final String component5() {
      return this.NAME;
   }

   @Nullable
   public final List<MappingsByIssueTypeOverride> component6() {
      return this.statusMappingsByIssueTypeOverride;
   }

   @Nullable
   public final List<MappingsByWorkflow> component7() {
      return this.statusMappingsByWorkflows;
   }

   @Nullable
   public final List<WorkflowSchemeAssociation> component8() {
      return this.workflowsForIssueTypes;
   }

   @NotNull
   public final WorkflowSchemeUpdateRequest copy(@NotNull String description, @NotNull String id, @NotNull DocumentVersion version, @Nullable String defaultWorkflowId, @Nullable String NAME, @Nullable List<MappingsByIssueTypeOverride> statusMappingsByIssueTypeOverride, @Nullable List<MappingsByWorkflow> statusMappingsByWorkflows, @Nullable List<WorkflowSchemeAssociation> workflowsForIssueTypes) {
      Intrinsics.checkNotNullParameter(description, "");
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(version, "");
      return new WorkflowSchemeUpdateRequest(description, id, version, defaultWorkflowId, NAME, statusMappingsByIssueTypeOverride, statusMappingsByWorkflows, workflowsForIssueTypes);
   }

   @NotNull
   public String toString() {
      return "WorkflowSchemeUpdateRequest(description=" + this.description + ", id=" + this.id + ", version=" + this.version + ", defaultWorkflowId=" + this.defaultWorkflowId + ", NAME=" + this.NAME + ", statusMappingsByIssueTypeOverride=" + this.statusMappingsByIssueTypeOverride + ", statusMappingsByWorkflows=" + this.statusMappingsByWorkflows + ", workflowsForIssueTypes=" + this.workflowsForIssueTypes + ")";
   }

   public int hashCode() {
      int var1 = this.description.hashCode();
      var1 = var1 * 31 + this.id.hashCode();
      var1 = var1 * 31 + this.version.hashCode();
      var1 = var1 * 31 + (this.defaultWorkflowId == null ? 0 : this.defaultWorkflowId.hashCode());
      var1 = var1 * 31 + (this.NAME == null ? 0 : this.NAME.hashCode());
      var1 = var1 * 31 + (this.statusMappingsByIssueTypeOverride == null ? 0 : this.statusMappingsByIssueTypeOverride.hashCode());
      var1 = var1 * 31 + (this.statusMappingsByWorkflows == null ? 0 : this.statusMappingsByWorkflows.hashCode());
      var1 = var1 * 31 + (this.workflowsForIssueTypes == null ? 0 : this.workflowsForIssueTypes.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowSchemeUpdateRequest)) {
         return false;
      } else {
         WorkflowSchemeUpdateRequest var2 = (WorkflowSchemeUpdateRequest)other;
         if (!Intrinsics.areEqual(this.description, var2.description)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.version, var2.version)) {
            return false;
         } else if (!Intrinsics.areEqual(this.defaultWorkflowId, var2.defaultWorkflowId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.NAME, var2.NAME)) {
            return false;
         } else if (!Intrinsics.areEqual(this.statusMappingsByIssueTypeOverride, var2.statusMappingsByIssueTypeOverride)) {
            return false;
         } else if (!Intrinsics.areEqual(this.statusMappingsByWorkflows, var2.statusMappingsByWorkflows)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.workflowsForIssueTypes, var2.workflowsForIssueTypes);
         }
      }
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
