package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J/\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000b\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowSchemeUpdateRequiredMappingsRequest;", "", "id", "", "workflowsForIssueTypes", "", "Lai/zencoder/generated/client/jira/models/WorkflowSchemeAssociation;", "defaultWorkflowId", "<init>", "(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;)V", "getId", "()Ljava/lang/String;", "getWorkflowsForIssueTypes", "()Ljava/util/List;", "getDefaultWorkflowId", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class WorkflowSchemeUpdateRequiredMappingsRequest {
   @NotNull
   private final String id;
   @NotNull
   private final List<WorkflowSchemeAssociation> workflowsForIssueTypes;
   @Nullable
   private final String defaultWorkflowId;

   public WorkflowSchemeUpdateRequiredMappingsRequest(@NotNull String id, @NotNull List<WorkflowSchemeAssociation> workflowsForIssueTypes, @Nullable String defaultWorkflowId) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(workflowsForIssueTypes, "");
      super();
      this.id = id;
      this.workflowsForIssueTypes = workflowsForIssueTypes;
      this.defaultWorkflowId = defaultWorkflowId;
   }

   @JsonProperty("id")
   @NotNull
   public final String getId() {
      return this.id;
   }

   @JsonProperty("workflowsForIssueTypes")
   @NotNull
   public final List<WorkflowSchemeAssociation> getWorkflowsForIssueTypes() {
      return this.workflowsForIssueTypes;
   }

   @JsonProperty("defaultWorkflowId")
   @Nullable
   public final String getDefaultWorkflowId() {
      return this.defaultWorkflowId;
   }

   @NotNull
   public final String component1() {
      return this.id;
   }

   @NotNull
   public final List<WorkflowSchemeAssociation> component2() {
      return this.workflowsForIssueTypes;
   }

   @Nullable
   public final String component3() {
      return this.defaultWorkflowId;
   }

   @NotNull
   public final WorkflowSchemeUpdateRequiredMappingsRequest copy(@NotNull String id, @NotNull List<WorkflowSchemeAssociation> workflowsForIssueTypes, @Nullable String defaultWorkflowId) {
      Intrinsics.checkNotNullParameter(id, "");
      Intrinsics.checkNotNullParameter(workflowsForIssueTypes, "");
      return new WorkflowSchemeUpdateRequiredMappingsRequest(id, workflowsForIssueTypes, defaultWorkflowId);
   }

   @NotNull
   public String toString() {
      return "WorkflowSchemeUpdateRequiredMappingsRequest(id=" + this.id + ", workflowsForIssueTypes=" + this.workflowsForIssueTypes + ", defaultWorkflowId=" + this.defaultWorkflowId + ")";
   }

   public int hashCode() {
      int var1 = this.id.hashCode();
      var1 = var1 * 31 + this.workflowsForIssueTypes.hashCode();
      var1 = var1 * 31 + (this.defaultWorkflowId == null ? 0 : this.defaultWorkflowId.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowSchemeUpdateRequiredMappingsRequest)) {
         return false;
      } else {
         WorkflowSchemeUpdateRequiredMappingsRequest var2 = (WorkflowSchemeUpdateRequiredMappingsRequest)other;
         if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else if (!Intrinsics.areEqual(this.workflowsForIssueTypes, var2.workflowsForIssueTypes)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.defaultWorkflowId, var2.defaultWorkflowId);
         }
      }
   }
}
