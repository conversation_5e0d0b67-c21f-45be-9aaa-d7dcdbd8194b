package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BO\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u0003\u00a2\u0006\u0004\b\u000b\u0010\fJ\u0011\u0010\u0012\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0013\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0014\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0015\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u0003H\u00c6\u0003JQ\u0010\u0016\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00032\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00032\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u001b\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u001b\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u001b\u0010\t\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000e\u00a8\u0006\u001e"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowSchemeUpdateRequiredMappingsResponse;", "", "statusMappingsByIssueTypes", "", "Lai/zencoder/generated/client/jira/models/RequiredMappingByIssueType;", "statusMappingsByWorkflows", "Lai/zencoder/generated/client/jira/models/RequiredMappingByWorkflows;", "statuses", "Lai/zencoder/generated/client/jira/models/StatusMetadata;", "statusesPerWorkflow", "Lai/zencoder/generated/client/jira/models/StatusesPerWorkflow;", "<init>", "(Ljava/util/Set;Ljava/util/Set;Ljava/util/Set;Ljava/util/Set;)V", "getStatusMappingsByIssueTypes", "()Ljava/util/Set;", "getStatusMappingsByWorkflows", "getStatuses", "getStatusesPerWorkflow", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class WorkflowSchemeUpdateRequiredMappingsResponse {
   @Nullable
   private final Set<RequiredMappingByIssueType> statusMappingsByIssueTypes;
   @Nullable
   private final Set<RequiredMappingByWorkflows> statusMappingsByWorkflows;
   @Nullable
   private final Set<StatusMetadata> statuses;
   @Nullable
   private final Set<StatusesPerWorkflow> statusesPerWorkflow;

   public WorkflowSchemeUpdateRequiredMappingsResponse(@Nullable Set<RequiredMappingByIssueType> statusMappingsByIssueTypes, @Nullable Set<RequiredMappingByWorkflows> statusMappingsByWorkflows, @Nullable Set<StatusMetadata> statuses, @Nullable Set<StatusesPerWorkflow> statusesPerWorkflow) {
      this.statusMappingsByIssueTypes = statusMappingsByIssueTypes;
      this.statusMappingsByWorkflows = statusMappingsByWorkflows;
      this.statuses = statuses;
      this.statusesPerWorkflow = statusesPerWorkflow;
   }

   @JsonProperty("statusMappingsByIssueTypes")
   @Nullable
   public final Set<RequiredMappingByIssueType> getStatusMappingsByIssueTypes() {
      return this.statusMappingsByIssueTypes;
   }

   @JsonProperty("statusMappingsByWorkflows")
   @Nullable
   public final Set<RequiredMappingByWorkflows> getStatusMappingsByWorkflows() {
      return this.statusMappingsByWorkflows;
   }

   @JsonProperty("statuses")
   @Nullable
   public final Set<StatusMetadata> getStatuses() {
      return this.statuses;
   }

   @JsonProperty("statusesPerWorkflow")
   @Nullable
   public final Set<StatusesPerWorkflow> getStatusesPerWorkflow() {
      return this.statusesPerWorkflow;
   }

   @Nullable
   public final Set<RequiredMappingByIssueType> component1() {
      return this.statusMappingsByIssueTypes;
   }

   @Nullable
   public final Set<RequiredMappingByWorkflows> component2() {
      return this.statusMappingsByWorkflows;
   }

   @Nullable
   public final Set<StatusMetadata> component3() {
      return this.statuses;
   }

   @Nullable
   public final Set<StatusesPerWorkflow> component4() {
      return this.statusesPerWorkflow;
   }

   @NotNull
   public final WorkflowSchemeUpdateRequiredMappingsResponse copy(@Nullable Set<RequiredMappingByIssueType> statusMappingsByIssueTypes, @Nullable Set<RequiredMappingByWorkflows> statusMappingsByWorkflows, @Nullable Set<StatusMetadata> statuses, @Nullable Set<StatusesPerWorkflow> statusesPerWorkflow) {
      return new WorkflowSchemeUpdateRequiredMappingsResponse(statusMappingsByIssueTypes, statusMappingsByWorkflows, statuses, statusesPerWorkflow);
   }

   @NotNull
   public String toString() {
      return "WorkflowSchemeUpdateRequiredMappingsResponse(statusMappingsByIssueTypes=" + this.statusMappingsByIssueTypes + ", statusMappingsByWorkflows=" + this.statusMappingsByWorkflows + ", statuses=" + this.statuses + ", statusesPerWorkflow=" + this.statusesPerWorkflow + ")";
   }

   public int hashCode() {
      int var1 = this.statusMappingsByIssueTypes == null ? 0 : this.statusMappingsByIssueTypes.hashCode();
      var1 = var1 * 31 + (this.statusMappingsByWorkflows == null ? 0 : this.statusMappingsByWorkflows.hashCode());
      var1 = var1 * 31 + (this.statuses == null ? 0 : this.statuses.hashCode());
      var1 = var1 * 31 + (this.statusesPerWorkflow == null ? 0 : this.statusesPerWorkflow.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowSchemeUpdateRequiredMappingsResponse)) {
         return false;
      } else {
         WorkflowSchemeUpdateRequiredMappingsResponse var2 = (WorkflowSchemeUpdateRequiredMappingsResponse)other;
         if (!Intrinsics.areEqual(this.statusMappingsByIssueTypes, var2.statusMappingsByIssueTypes)) {
            return false;
         } else if (!Intrinsics.areEqual(this.statusMappingsByWorkflows, var2.statusMappingsByWorkflows)) {
            return false;
         } else if (!Intrinsics.areEqual(this.statuses, var2.statuses)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.statusesPerWorkflow, var2.statusesPerWorkflow);
         }
      }
   }

   public WorkflowSchemeUpdateRequiredMappingsResponse() {
      this((Set)null, (Set)null, (Set)null, (Set)null, 15, (DefaultConstructorMarker)null);
   }
}
