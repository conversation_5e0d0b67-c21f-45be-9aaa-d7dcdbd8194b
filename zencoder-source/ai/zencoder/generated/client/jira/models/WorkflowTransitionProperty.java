package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u001e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001j\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003`\u0004B'\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0002\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0002\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u000e\u001a\u00020\u0002H\u00c6\u0003J\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0002H\u00c6\u0003J+\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0005\u001a\u00020\u00022\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0002H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0003H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0002H\u00d6\u0001R\u0013\u0010\u0005\u001a\u00020\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00028G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000b\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowTransitionProperty;", "Ljava/util/HashMap;", "", "", "Lkotlin/collections/HashMap;", "value", "id", "key", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getValue", "()Ljava/lang/String;", "getId", "getKey", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class WorkflowTransitionProperty extends HashMap<String, Object> {
   @NotNull
   private final String value;
   @Nullable
   private final String id;
   @Nullable
   private final String key;

   public WorkflowTransitionProperty(@NotNull String value, @Nullable String id, @Nullable String key) {
      Intrinsics.checkNotNullParameter(value, "");
      super();
      this.value = value;
      this.id = id;
      this.key = key;
   }

   @JsonProperty("value")
   @NotNull
   public final String getValue() {
      return this.value;
   }

   @JsonProperty("id")
   @Nullable
   public final String getId() {
      return this.id;
   }

   @JsonProperty("key")
   @Nullable
   public final String getKey() {
      return this.key;
   }

   @NotNull
   public final String component1() {
      return this.value;
   }

   @Nullable
   public final String component2() {
      return this.id;
   }

   @Nullable
   public final String component3() {
      return this.key;
   }

   @NotNull
   public final WorkflowTransitionProperty copy(@NotNull String value, @Nullable String id, @Nullable String key) {
      Intrinsics.checkNotNullParameter(value, "");
      return new WorkflowTransitionProperty(value, id, key);
   }

   @NotNull
   public String toString() {
      return "WorkflowTransitionProperty(value=" + this.value + ", id=" + this.id + ", key=" + this.key + ")";
   }

   public int hashCode() {
      int var1 = this.value.hashCode();
      var1 = var1 * 31 + (this.id == null ? 0 : this.id.hashCode());
      var1 = var1 * 31 + (this.key == null ? 0 : this.key.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowTransitionProperty)) {
         return false;
      } else {
         WorkflowTransitionProperty var2 = (WorkflowTransitionProperty)other;
         if (!Intrinsics.areEqual(this.value, var2.value)) {
            return false;
         } else if (!Intrinsics.areEqual(this.id, var2.id)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.key, var2.key);
         }
      }
   }

   public int getSize() {
      return super.size();
   }

   public final int size() {
      return this.getSize();
   }

   public Collection<Object> getValues() {
      return super.values();
   }

   public final Collection<Object> values() {
      return this.getValues();
   }

   public Set<Map.Entry<String, Object>> getEntries() {
      return super.entrySet();
   }

   public final Set<Map.Entry<String, Object>> entrySet() {
      return this.getEntries();
   }

   public Set<String> getKeys() {
      return super.keySet();
   }

   public final Set<String> keySet() {
      return this.getKeys();
   }
}
