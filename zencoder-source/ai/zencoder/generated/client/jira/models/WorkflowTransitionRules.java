package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BE\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u00a2\u0006\u0004\b\t\u0010\nJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\u0011\u0010\u0012\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u0013\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003J\u0011\u0010\u0014\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0003JI\u0010\u0015\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u001b\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u001b\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000e\u00a8\u0006\u001d"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowTransitionRules;", "", "workflowId", "Lai/zencoder/generated/client/jira/models/WorkflowId;", "conditions", "", "Lai/zencoder/generated/client/jira/models/AppWorkflowTransitionRule;", "postFunctions", "validators", "<init>", "(Lai/zencoder/generated/client/jira/models/WorkflowId;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getWorkflowId", "()Lai/zencoder/generated/client/jira/models/WorkflowId;", "getConditions", "()Ljava/util/List;", "getPostFunctions", "getValidators", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class WorkflowTransitionRules {
   @NotNull
   private final WorkflowId workflowId;
   @Nullable
   private final List<AppWorkflowTransitionRule> conditions;
   @Nullable
   private final List<AppWorkflowTransitionRule> postFunctions;
   @Nullable
   private final List<AppWorkflowTransitionRule> validators;

   public WorkflowTransitionRules(@NotNull WorkflowId workflowId, @Nullable List<AppWorkflowTransitionRule> conditions, @Nullable List<AppWorkflowTransitionRule> postFunctions, @Nullable List<AppWorkflowTransitionRule> validators) {
      Intrinsics.checkNotNullParameter(workflowId, "");
      super();
      this.workflowId = workflowId;
      this.conditions = conditions;
      this.postFunctions = postFunctions;
      this.validators = validators;
   }

   @JsonProperty("workflowId")
   @NotNull
   public final WorkflowId getWorkflowId() {
      return this.workflowId;
   }

   @JsonProperty("conditions")
   @Nullable
   public final List<AppWorkflowTransitionRule> getConditions() {
      return this.conditions;
   }

   @JsonProperty("postFunctions")
   @Nullable
   public final List<AppWorkflowTransitionRule> getPostFunctions() {
      return this.postFunctions;
   }

   @JsonProperty("validators")
   @Nullable
   public final List<AppWorkflowTransitionRule> getValidators() {
      return this.validators;
   }

   @NotNull
   public final WorkflowId component1() {
      return this.workflowId;
   }

   @Nullable
   public final List<AppWorkflowTransitionRule> component2() {
      return this.conditions;
   }

   @Nullable
   public final List<AppWorkflowTransitionRule> component3() {
      return this.postFunctions;
   }

   @Nullable
   public final List<AppWorkflowTransitionRule> component4() {
      return this.validators;
   }

   @NotNull
   public final WorkflowTransitionRules copy(@NotNull WorkflowId workflowId, @Nullable List<AppWorkflowTransitionRule> conditions, @Nullable List<AppWorkflowTransitionRule> postFunctions, @Nullable List<AppWorkflowTransitionRule> validators) {
      Intrinsics.checkNotNullParameter(workflowId, "");
      return new WorkflowTransitionRules(workflowId, conditions, postFunctions, validators);
   }

   @NotNull
   public String toString() {
      return "WorkflowTransitionRules(workflowId=" + this.workflowId + ", conditions=" + this.conditions + ", postFunctions=" + this.postFunctions + ", validators=" + this.validators + ")";
   }

   public int hashCode() {
      int var1 = this.workflowId.hashCode();
      var1 = var1 * 31 + (this.conditions == null ? 0 : this.conditions.hashCode());
      var1 = var1 * 31 + (this.postFunctions == null ? 0 : this.postFunctions.hashCode());
      var1 = var1 * 31 + (this.validators == null ? 0 : this.validators.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowTransitionRules)) {
         return false;
      } else {
         WorkflowTransitionRules var2 = (WorkflowTransitionRules)other;
         if (!Intrinsics.areEqual(this.workflowId, var2.workflowId)) {
            return false;
         } else if (!Intrinsics.areEqual(this.conditions, var2.conditions)) {
            return false;
         } else if (!Intrinsics.areEqual(this.postFunctions, var2.postFunctions)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.validators, var2.validators);
         }
      }
   }
}
