package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J#\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0014H\u00d6\u0001J\t\u0010\u0015\u001a\u00020\u0006H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0019\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowTransitionRulesDetails;", "", "workflowId", "Lai/zencoder/generated/client/jira/models/WorkflowId;", "workflowRuleIds", "", "", "<init>", "(Lai/zencoder/generated/client/jira/models/WorkflowId;Ljava/util/Set;)V", "getWorkflowId", "()Lai/zencoder/generated/client/jira/models/WorkflowId;", "getWorkflowRuleIds", "()Ljava/util/Set;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class WorkflowTransitionRulesDetails {
   @NotNull
   private final WorkflowId workflowId;
   @NotNull
   private final Set<String> workflowRuleIds;

   public WorkflowTransitionRulesDetails(@NotNull WorkflowId workflowId, @NotNull Set<String> workflowRuleIds) {
      Intrinsics.checkNotNullParameter(workflowId, "");
      Intrinsics.checkNotNullParameter(workflowRuleIds, "");
      super();
      this.workflowId = workflowId;
      this.workflowRuleIds = workflowRuleIds;
   }

   @JsonProperty("workflowId")
   @NotNull
   public final WorkflowId getWorkflowId() {
      return this.workflowId;
   }

   @JsonProperty("workflowRuleIds")
   @NotNull
   public final Set<String> getWorkflowRuleIds() {
      return this.workflowRuleIds;
   }

   @NotNull
   public final WorkflowId component1() {
      return this.workflowId;
   }

   @NotNull
   public final Set<String> component2() {
      return this.workflowRuleIds;
   }

   @NotNull
   public final WorkflowTransitionRulesDetails copy(@NotNull WorkflowId workflowId, @NotNull Set<String> workflowRuleIds) {
      Intrinsics.checkNotNullParameter(workflowId, "");
      Intrinsics.checkNotNullParameter(workflowRuleIds, "");
      return new WorkflowTransitionRulesDetails(workflowId, workflowRuleIds);
   }

   @NotNull
   public String toString() {
      return "WorkflowTransitionRulesDetails(workflowId=" + this.workflowId + ", workflowRuleIds=" + this.workflowRuleIds + ")";
   }

   public int hashCode() {
      int var1 = this.workflowId.hashCode();
      var1 = var1 * 31 + this.workflowRuleIds.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowTransitionRulesDetails)) {
         return false;
      } else {
         WorkflowTransitionRulesDetails var2 = (WorkflowTransitionRulesDetails)other;
         if (!Intrinsics.areEqual(this.workflowId, var2.workflowId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.workflowRuleIds, var2.workflowRuleIds);
         }
      }
   }
}
