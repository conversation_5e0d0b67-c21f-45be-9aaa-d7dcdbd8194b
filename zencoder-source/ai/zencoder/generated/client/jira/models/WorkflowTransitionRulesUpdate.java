package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0004\b\u0005\u0010\u0006J\u000f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u0019\u0010\n\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0001J\u0013\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001R\u0019\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\b\u00a8\u0006\u0012"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowTransitionRulesUpdate;", "", "workflows", "", "Lai/zencoder/generated/client/jira/models/WorkflowTransitionRules;", "<init>", "(Ljava/util/List;)V", "getWorkflows", "()Ljava/util/List;", "component1", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class WorkflowTransitionRulesUpdate {
   @NotNull
   private final List<WorkflowTransitionRules> workflows;

   public WorkflowTransitionRulesUpdate(@NotNull List<WorkflowTransitionRules> workflows) {
      Intrinsics.checkNotNullParameter(workflows, "");
      super();
      this.workflows = workflows;
   }

   @JsonProperty("workflows")
   @NotNull
   public final List<WorkflowTransitionRules> getWorkflows() {
      return this.workflows;
   }

   @NotNull
   public final List<WorkflowTransitionRules> component1() {
      return this.workflows;
   }

   @NotNull
   public final WorkflowTransitionRulesUpdate copy(@NotNull List<WorkflowTransitionRules> workflows) {
      Intrinsics.checkNotNullParameter(workflows, "");
      return new WorkflowTransitionRulesUpdate(workflows);
   }

   @NotNull
   public String toString() {
      return "WorkflowTransitionRulesUpdate(workflows=" + this.workflows + ")";
   }

   public int hashCode() {
      return this.workflows.hashCode();
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowTransitionRulesUpdate)) {
         return false;
      } else {
         WorkflowTransitionRulesUpdate var2 = (WorkflowTransitionRulesUpdate)other;
         return Intrinsics.areEqual(this.workflows, var2.workflows);
      }
   }
}
