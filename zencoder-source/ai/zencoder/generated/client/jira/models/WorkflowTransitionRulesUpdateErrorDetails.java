package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010\"\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\u0018\u0010\u0002\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00050\u0003\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00040\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0004\b\t\u0010\nJ\u001b\u0010\u0011\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00050\u0003H\u00c6\u0003J\u000f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u0005H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\bH\u00c6\u0003J?\u0010\u0014\u001a\u00020\u00002\u001a\b\u0002\u0010\u0002\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00050\u00032\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00040\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0004H\u00d6\u0001R%\u0010\u0002\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00050\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0019\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00040\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0007\u001a\u00020\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u001b"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowTransitionRulesUpdateErrorDetails;", "", "ruleUpdateErrors", "", "", "", "updateErrors", "workflowId", "Lai/zencoder/generated/client/jira/models/WorkflowId;", "<init>", "(Ljava/util/Map;Ljava/util/Set;Lai/zencoder/generated/client/jira/models/WorkflowId;)V", "getRuleUpdateErrors", "()Ljava/util/Map;", "getUpdateErrors", "()Ljava/util/Set;", "getWorkflowId", "()Lai/zencoder/generated/client/jira/models/WorkflowId;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class WorkflowTransitionRulesUpdateErrorDetails {
   @NotNull
   private final Map<String, Set<String>> ruleUpdateErrors;
   @NotNull
   private final Set<String> updateErrors;
   @NotNull
   private final WorkflowId workflowId;

   public WorkflowTransitionRulesUpdateErrorDetails(@NotNull Map<String, ? extends Set<String>> ruleUpdateErrors, @NotNull Set<String> updateErrors, @NotNull WorkflowId workflowId) {
      Intrinsics.checkNotNullParameter(ruleUpdateErrors, "");
      Intrinsics.checkNotNullParameter(updateErrors, "");
      Intrinsics.checkNotNullParameter(workflowId, "");
      super();
      this.ruleUpdateErrors = ruleUpdateErrors;
      this.updateErrors = updateErrors;
      this.workflowId = workflowId;
   }

   @JsonProperty("ruleUpdateErrors")
   @NotNull
   public final Map<String, Set<String>> getRuleUpdateErrors() {
      return this.ruleUpdateErrors;
   }

   @JsonProperty("updateErrors")
   @NotNull
   public final Set<String> getUpdateErrors() {
      return this.updateErrors;
   }

   @JsonProperty("workflowId")
   @NotNull
   public final WorkflowId getWorkflowId() {
      return this.workflowId;
   }

   @NotNull
   public final Map<String, Set<String>> component1() {
      return this.ruleUpdateErrors;
   }

   @NotNull
   public final Set<String> component2() {
      return this.updateErrors;
   }

   @NotNull
   public final WorkflowId component3() {
      return this.workflowId;
   }

   @NotNull
   public final WorkflowTransitionRulesUpdateErrorDetails copy(@NotNull Map<String, ? extends Set<String>> ruleUpdateErrors, @NotNull Set<String> updateErrors, @NotNull WorkflowId workflowId) {
      Intrinsics.checkNotNullParameter(ruleUpdateErrors, "");
      Intrinsics.checkNotNullParameter(updateErrors, "");
      Intrinsics.checkNotNullParameter(workflowId, "");
      return new WorkflowTransitionRulesUpdateErrorDetails(ruleUpdateErrors, updateErrors, workflowId);
   }

   @NotNull
   public String toString() {
      return "WorkflowTransitionRulesUpdateErrorDetails(ruleUpdateErrors=" + this.ruleUpdateErrors + ", updateErrors=" + this.updateErrors + ", workflowId=" + this.workflowId + ")";
   }

   public int hashCode() {
      int var1 = this.ruleUpdateErrors.hashCode();
      var1 = var1 * 31 + this.updateErrors.hashCode();
      var1 = var1 * 31 + this.workflowId.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowTransitionRulesUpdateErrorDetails)) {
         return false;
      } else {
         WorkflowTransitionRulesUpdateErrorDetails var2 = (WorkflowTransitionRulesUpdateErrorDetails)other;
         if (!Intrinsics.areEqual(this.ruleUpdateErrors, var2.ruleUpdateErrors)) {
            return false;
         } else if (!Intrinsics.areEqual(this.updateErrors, var2.updateErrors)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.workflowId, var2.workflowId);
         }
      }
   }
}
