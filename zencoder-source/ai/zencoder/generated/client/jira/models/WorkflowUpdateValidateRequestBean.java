package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001b\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\r\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u001f\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0016"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowUpdateValidateRequestBean;", "", "payload", "Lai/zencoder/generated/client/jira/models/WorkflowUpdateRequest;", "validationOptions", "Lai/zencoder/generated/client/jira/models/ValidationOptionsForUpdate;", "<init>", "(Lai/zencoder/generated/client/jira/models/WorkflowUpdateRequest;Lai/zencoder/generated/client/jira/models/ValidationOptionsForUpdate;)V", "getPayload", "()Lai/zencoder/generated/client/jira/models/WorkflowUpdateRequest;", "getValidationOptions", "()Lai/zencoder/generated/client/jira/models/ValidationOptionsForUpdate;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "zencoder"}
)
public final class WorkflowUpdateValidateRequestBean {
   @NotNull
   private final WorkflowUpdateRequest payload;
   @Nullable
   private final ValidationOptionsForUpdate validationOptions;

   public WorkflowUpdateValidateRequestBean(@NotNull WorkflowUpdateRequest payload, @Nullable ValidationOptionsForUpdate validationOptions) {
      Intrinsics.checkNotNullParameter(payload, "");
      super();
      this.payload = payload;
      this.validationOptions = validationOptions;
   }

   @JsonProperty("payload")
   @NotNull
   public final WorkflowUpdateRequest getPayload() {
      return this.payload;
   }

   @JsonProperty("validationOptions")
   @Nullable
   public final ValidationOptionsForUpdate getValidationOptions() {
      return this.validationOptions;
   }

   @NotNull
   public final WorkflowUpdateRequest component1() {
      return this.payload;
   }

   @Nullable
   public final ValidationOptionsForUpdate component2() {
      return this.validationOptions;
   }

   @NotNull
   public final WorkflowUpdateValidateRequestBean copy(@NotNull WorkflowUpdateRequest payload, @Nullable ValidationOptionsForUpdate validationOptions) {
      Intrinsics.checkNotNullParameter(payload, "");
      return new WorkflowUpdateValidateRequestBean(payload, validationOptions);
   }

   @NotNull
   public String toString() {
      return "WorkflowUpdateValidateRequestBean(payload=" + this.payload + ", validationOptions=" + this.validationOptions + ")";
   }

   public int hashCode() {
      int var1 = this.payload.hashCode();
      var1 = var1 * 31 + (this.validationOptions == null ? 0 : this.validationOptions.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowUpdateValidateRequestBean)) {
         return false;
      } else {
         WorkflowUpdateValidateRequestBean var2 = (WorkflowUpdateValidateRequestBean)other;
         if (!Intrinsics.areEqual(this.payload, var2.payload)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.validationOptions, var2.validationOptions);
         }
      }
   }
}
