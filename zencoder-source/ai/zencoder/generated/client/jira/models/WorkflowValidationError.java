package ai.zencoder.generated.client.jira.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001:\u0002\"#BC\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0004\b\u000b\u0010\fJ\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\nH\u00c6\u0003JE\u0010\u001b\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\nH\u00c6\u0001J\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001J\t\u0010!\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0015\u0010\b\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000eR\u0015\u0010\t\u001a\u0004\u0018\u00010\n8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015\u00a8\u0006$"},
   d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowValidationError;", "", "code", "", "elementReference", "Lai/zencoder/generated/client/jira/models/WorkflowElementReference;", "level", "Lai/zencoder/generated/client/jira/models/WorkflowValidationError$Level;", "message", "type", "Lai/zencoder/generated/client/jira/models/WorkflowValidationError$Type;", "<init>", "(Ljava/lang/String;Lai/zencoder/generated/client/jira/models/WorkflowElementReference;Lai/zencoder/generated/client/jira/models/WorkflowValidationError$Level;Ljava/lang/String;Lai/zencoder/generated/client/jira/models/WorkflowValidationError$Type;)V", "getCode", "()Ljava/lang/String;", "getElementReference", "()Lai/zencoder/generated/client/jira/models/WorkflowElementReference;", "getLevel", "()Lai/zencoder/generated/client/jira/models/WorkflowValidationError$Level;", "getMessage", "getType", "()Lai/zencoder/generated/client/jira/models/WorkflowValidationError$Type;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "Level", "Type", "zencoder"}
)
public final class WorkflowValidationError {
   @Nullable
   private final String code;
   @Nullable
   private final WorkflowElementReference elementReference;
   @Nullable
   private final Level level;
   @Nullable
   private final String message;
   @Nullable
   private final Type type;

   public WorkflowValidationError(@Nullable String code, @Nullable WorkflowElementReference elementReference, @Nullable Level level, @Nullable String message, @Nullable Type type) {
      this.code = code;
      this.elementReference = elementReference;
      this.level = level;
      this.message = message;
      this.type = type;
   }

   @JsonProperty("code")
   @Nullable
   public final String getCode() {
      return this.code;
   }

   @JsonProperty("elementReference")
   @Nullable
   public final WorkflowElementReference getElementReference() {
      return this.elementReference;
   }

   @JsonProperty("level")
   @Nullable
   public final Level getLevel() {
      return this.level;
   }

   @JsonProperty("message")
   @Nullable
   public final String getMessage() {
      return this.message;
   }

   @JsonProperty("type")
   @Nullable
   public final Type getType() {
      return this.type;
   }

   @Nullable
   public final String component1() {
      return this.code;
   }

   @Nullable
   public final WorkflowElementReference component2() {
      return this.elementReference;
   }

   @Nullable
   public final Level component3() {
      return this.level;
   }

   @Nullable
   public final String component4() {
      return this.message;
   }

   @Nullable
   public final Type component5() {
      return this.type;
   }

   @NotNull
   public final WorkflowValidationError copy(@Nullable String code, @Nullable WorkflowElementReference elementReference, @Nullable Level level, @Nullable String message, @Nullable Type type) {
      return new WorkflowValidationError(code, elementReference, level, message, type);
   }

   @NotNull
   public String toString() {
      return "WorkflowValidationError(code=" + this.code + ", elementReference=" + this.elementReference + ", level=" + this.level + ", message=" + this.message + ", type=" + this.type + ")";
   }

   public int hashCode() {
      int var1 = this.code == null ? 0 : this.code.hashCode();
      var1 = var1 * 31 + (this.elementReference == null ? 0 : this.elementReference.hashCode());
      var1 = var1 * 31 + (this.level == null ? 0 : this.level.hashCode());
      var1 = var1 * 31 + (this.message == null ? 0 : this.message.hashCode());
      var1 = var1 * 31 + (this.type == null ? 0 : this.type.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof WorkflowValidationError)) {
         return false;
      } else {
         WorkflowValidationError var2 = (WorkflowValidationError)other;
         if (!Intrinsics.areEqual(this.code, var2.code)) {
            return false;
         } else if (!Intrinsics.areEqual(this.elementReference, var2.elementReference)) {
            return false;
         } else if (this.level != var2.level) {
            return false;
         } else if (!Intrinsics.areEqual(this.message, var2.message)) {
            return false;
         } else {
            return this.type == var2.type;
         }
      }
   }

   public WorkflowValidationError() {
      this((String)null, (WorkflowElementReference)null, (Level)null, (String)null, (Type)null, 31, (DefaultConstructorMarker)null);
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"},
      d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowValidationError$Level;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "WARNING", "ERROR", "zencoder"}
   )
   public static enum Level {
      @NotNull
      private final String value;
      @JsonProperty("WARNING")
      WARNING("WARNING"),
      @JsonProperty("ERROR")
      ERROR("ERROR");

      private Level(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Level> getEntries() {
         return $ENTRIES;
      }
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011\u00a8\u0006\u0012"},
      d2 = {"Lai/zencoder/generated/client/jira/models/WorkflowValidationError$Type;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "RULE", "STATUS", "STATUS_LAYOUT", "STATUS_PROPERTY", "WORKFLOW", "TRANSITION", "TRANSITION_PROPERTY", "SCOPE", "STATUS_MAPPING", "TRIGGER", "zencoder"}
   )
   public static enum Type {
      @NotNull
      private final String value;
      @JsonProperty("RULE")
      RULE("RULE"),
      @JsonProperty("STATUS")
      STATUS("STATUS"),
      @JsonProperty("STATUS_LAYOUT")
      STATUS_LAYOUT("STATUS_LAYOUT"),
      @JsonProperty("STATUS_PROPERTY")
      STATUS_PROPERTY("STATUS_PROPERTY"),
      @JsonProperty("WORKFLOW")
      WORKFLOW("WORKFLOW"),
      @JsonProperty("TRANSITION")
      TRANSITION("TRANSITION"),
      @JsonProperty("TRANSITION_PROPERTY")
      TRANSITION_PROPERTY("TRANSITION_PROPERTY"),
      @JsonProperty("SCOPE")
      SCOPE("SCOPE"),
      @JsonProperty("STATUS_MAPPING")
      STATUS_MAPPING("STATUS_MAPPING"),
      @JsonProperty("TRIGGER")
      TRIGGER("TRIGGER");

      private Type(String value) {
         this.value = value;
      }

      @NotNull
      public final String getValue() {
         return this.value;
      }

      @NotNull
      public static EnumEntries<Type> getEntries() {
         return $ENTRIES;
      }
   }
}
