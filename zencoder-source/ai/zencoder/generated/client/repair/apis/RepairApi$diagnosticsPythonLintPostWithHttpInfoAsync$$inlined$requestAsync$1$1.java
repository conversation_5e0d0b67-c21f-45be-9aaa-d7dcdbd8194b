package ai.zencoder.generated.client.repair.apis;

import ai.zencoder.generated.client.repair.models.DiagnosticsResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import kotlin.Metadata;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\r\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002*\u0001\u0000\b\n\u0018\u00002\b\u0012\u0004\u0012\u00028\u00000\u0001\u00a8\u0006\u0002\u00b8\u0006\u0003"},
   d2 = {"ai/zencoder/generated/client/repair/infrastructure/ApiClient$responseBody$2", "Lcom/fasterxml/jackson/core/type/TypeReference;", "zencoder", "ai/zencoder/generated/client/repair/infrastructure/ApiClient$requestAsync$1$onResponse$$inlined$responseBody$1"}
)
public final class RepairApi$diagnosticsPythonLintPostWithHttpInfoAsync$$inlined$requestAsync$1$1 extends TypeReference<DiagnosticsResponse> {
}
