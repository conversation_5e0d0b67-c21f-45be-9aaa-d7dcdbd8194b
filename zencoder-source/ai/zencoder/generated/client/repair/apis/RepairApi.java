package ai.zencoder.generated.client.repair.apis;

import ai.zencoder.generated.client.repair.infrastructure.ApiClient;
import ai.zencoder.generated.client.repair.infrastructure.ApiResponse;
import ai.zencoder.generated.client.repair.infrastructure.ClientError;
import ai.zencoder.generated.client.repair.infrastructure.ClientException;
import ai.zencoder.generated.client.repair.infrastructure.RequestConfig;
import ai.zencoder.generated.client.repair.infrastructure.RequestMethod;
import ai.zencoder.generated.client.repair.infrastructure.ServerError;
import ai.zencoder.generated.client.repair.infrastructure.ServerException;
import ai.zencoder.generated.client.repair.infrastructure.Success;
import ai.zencoder.generated.client.repair.models.CompileRequest;
import ai.zencoder.generated.client.repair.models.CompileResponse;
import ai.zencoder.generated.client.repair.models.DiagnosticsRequest;
import ai.zencoder.generated.client.repair.models.DiagnosticsResponse;
import ai.zencoder.plugin.api.exception.RateLimitException;
import ai.zencoder.plugin.api.exception.RateLimitExceptionKt;
import ai.zencoder.plugin.api.exception.RequestDelayedException;
import java.io.IOException;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.Future;
import kotlin.Deprecated;
import kotlin.Lazy;
import kotlin.LazyKt;
import kotlin.Metadata;
import kotlin.NoWhenBranchMatchedException;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.Reflection;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.reflect.KProperty1;
import kotlin.reflect.full.KClasses;
import kotlin.text.StringsKt;
import okhttp3.Call;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\u0018\u0000 $2\u00020\u0001:\u0001$B\u001b\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u000e\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u000f2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\n\u001a\u00020\u000bJ\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00122\u0006\u0010\n\u001a\u00020\u000bJ\u000e\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0016J\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00140\r2\u0006\u0010\u0015\u001a\u00020\u0016J\u0016\u0010\u0018\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00140\u000f2\u0006\u0010\u0015\u001a\u00020\u0016J\u0014\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00140\r2\u0006\u0010\u0015\u001a\u00020\u0016J\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00160\u00122\u0006\u0010\u0015\u001a\u00020\u0016J\u0010\u0010\u001b\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0016H\u0007J\u0016\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00140\r2\u0006\u0010\u0015\u001a\u00020\u0016H\u0007J\u0018\u0010\u001d\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00140\u000f2\u0006\u0010\u0015\u001a\u00020\u0016H\u0007J\u0016\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00140\r2\u0006\u0010\u0015\u001a\u00020\u0016H\u0007J\u0016\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00160\u00122\u0006\u0010\u0015\u001a\u00020\u0016H\u0007J\u0010\u0010 \u001a\u00020\u00032\u0006\u0010!\u001a\u00020\u0003H\u0002J\u0010\u0010\"\u001a\u00020\u00002\b\u0010#\u001a\u0004\u0018\u00010\u0003\u00a8\u0006%"},
   d2 = {"Lai/zencoder/generated/client/repair/apis/RepairApi;", "Lai/zencoder/generated/client/repair/infrastructure/ApiClient;", "basePath", "", "client", "Lokhttp3/OkHttpClient;", "<init>", "(Ljava/lang/String;Lokhttp3/OkHttpClient;)V", "compileAnyCompilePost", "Lai/zencoder/generated/client/repair/models/CompileResponse;", "compileRequest", "Lai/zencoder/generated/client/repair/models/CompileRequest;", "compileAnyCompilePostAsync", "Ljava/util/concurrent/Future;", "compileAnyCompilePostWithHttpInfo", "Lai/zencoder/generated/client/repair/infrastructure/ApiResponse;", "compileAnyCompilePostWithHttpInfoAsync", "compileAnyCompilePostRequestConfig", "Lai/zencoder/generated/client/repair/infrastructure/RequestConfig;", "diagnosticsAnyLintPost", "Lai/zencoder/generated/client/repair/models/DiagnosticsResponse;", "diagnosticsRequest", "Lai/zencoder/generated/client/repair/models/DiagnosticsRequest;", "diagnosticsAnyLintPostAsync", "diagnosticsAnyLintPostWithHttpInfo", "diagnosticsAnyLintPostWithHttpInfoAsync", "diagnosticsAnyLintPostRequestConfig", "diagnosticsPythonLintPost", "diagnosticsPythonLintPostAsync", "diagnosticsPythonLintPostWithHttpInfo", "diagnosticsPythonLintPostWithHttpInfoAsync", "diagnosticsPythonLintPostRequestConfig", "encodeURIComponent", "uriComponent", "withAuth", "token", "Companion", "zencoder"}
)
@SourceDebugExtension({"SMAP\nRepairApi.kt\nKotlin\n*S Kotlin\n*F\n+ 1 RepairApi.kt\nai/zencoder/generated/client/repair/apis/RepairApi\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n+ 3 fake.kt\nkotlin/jvm/internal/FakeKt\n+ 4 ApiClient.kt\nai/zencoder/generated/client/repair/infrastructure/ApiClient\n+ 5 _Maps.kt\nkotlin/collections/MapsKt___MapsKt\n*L\n1#1,470:1\n288#2,2:471\n1855#2:485\n1856#2:488\n1855#2:831\n1856#2:834\n288#2,2:1070\n1855#2:1083\n1856#2:1086\n1855#2:1429\n1856#2:1432\n288#2,2:1668\n1855#2:1681\n1856#2:1684\n1855#2:2027\n1856#2:2030\n1#3:473\n227#4,9:474\n236#4:484\n237#4,2:486\n239#4:489\n240#4,24:491\n100#4,49:515\n264#4,3:564\n100#4,49:567\n267#4:616\n100#4,49:617\n268#4:666\n100#4,49:667\n269#4,24:716\n151#4,64:740\n293#4,16:804\n322#4,9:820\n331#4:830\n332#4,2:832\n334#4:835\n335#4,24:837\n100#4,49:861\n359#4,3:910\n100#4,49:913\n362#4:962\n100#4,49:963\n363#4:1012\n100#4,49:1013\n364#4,7:1062\n393#4:1069\n227#4,9:1072\n236#4:1082\n237#4,2:1084\n239#4:1087\n240#4,24:1089\n100#4,49:1113\n264#4,3:1162\n100#4,49:1165\n267#4:1214\n100#4,49:1215\n268#4:1264\n100#4,49:1265\n269#4,24:1314\n151#4,64:1338\n293#4,16:1402\n322#4,9:1418\n331#4:1428\n332#4,2:1430\n334#4:1433\n335#4,24:1435\n100#4,49:1459\n359#4,3:1508\n100#4,49:1511\n362#4:1560\n100#4,49:1561\n363#4:1610\n100#4,49:1611\n364#4,7:1660\n393#4:1667\n227#4,9:1670\n236#4:1680\n237#4,2:1682\n239#4:1685\n240#4,24:1687\n100#4,49:1711\n264#4,3:1760\n100#4,49:1763\n267#4:1812\n100#4,49:1813\n268#4:1862\n100#4,49:1863\n269#4,24:1912\n151#4,64:1936\n293#4,16:2000\n322#4,9:2016\n331#4:2026\n332#4,2:2028\n334#4:2031\n335#4,24:2033\n100#4,49:2057\n359#4,3:2106\n100#4,49:2109\n362#4:2158\n100#4,49:2159\n363#4:2208\n100#4,49:2209\n364#4,7:2258\n393#4:2265\n215#5:483\n216#5:490\n215#5:829\n216#5:836\n215#5:1081\n216#5:1088\n215#5:1427\n216#5:1434\n215#5:1679\n216#5:1686\n215#5:2025\n216#5:2032\n*S KotlinDebug\n*F\n+ 1 RepairApi.kt\nai/zencoder/generated/client/repair/apis/RepairApi\n*L\n74#1:471,2\n141#1:485\n141#1:488\n160#1:831\n160#1:834\n205#1:1070,2\n272#1:1083\n272#1:1086\n291#1:1429\n291#1:1432\n338#1:1668,2\n409#1:1681\n409#1:1684\n430#1:2027\n430#1:2030\n141#1:474,9\n141#1:484\n141#1:486,2\n141#1:489\n141#1:491,24\n141#1:515,49\n141#1:564,3\n141#1:567,49\n141#1:616\n141#1:617,49\n141#1:666\n141#1:667,49\n141#1:716,24\n141#1:740,64\n141#1:804,16\n160#1:820,9\n160#1:830\n160#1:832,2\n160#1:835\n160#1:837,24\n160#1:861,49\n160#1:910,3\n160#1:913,49\n160#1:962\n160#1:963,49\n160#1:1012\n160#1:1013,49\n160#1:1062,7\n160#1:1069\n272#1:1072,9\n272#1:1082\n272#1:1084,2\n272#1:1087\n272#1:1089,24\n272#1:1113,49\n272#1:1162,3\n272#1:1165,49\n272#1:1214\n272#1:1215,49\n272#1:1264\n272#1:1265,49\n272#1:1314,24\n272#1:1338,64\n272#1:1402,16\n291#1:1418,9\n291#1:1428\n291#1:1430,2\n291#1:1433\n291#1:1435,24\n291#1:1459,49\n291#1:1508,3\n291#1:1511,49\n291#1:1560\n291#1:1561,49\n291#1:1610\n291#1:1611,49\n291#1:1660,7\n291#1:1667\n409#1:1670,9\n409#1:1680\n409#1:1682,2\n409#1:1685\n409#1:1687,24\n409#1:1711,49\n409#1:1760,3\n409#1:1763,49\n409#1:1812\n409#1:1813,49\n409#1:1862\n409#1:1863,49\n409#1:1912,24\n409#1:1936,64\n409#1:2000,16\n430#1:2016,9\n430#1:2026\n430#1:2028,2\n430#1:2031\n430#1:2033,24\n430#1:2057,49\n430#1:2106,3\n430#1:2109,49\n430#1:2158\n430#1:2159,49\n430#1:2208\n430#1:2209,49\n430#1:2258,7\n430#1:2265\n141#1:483\n141#1:490\n160#1:829\n160#1:836\n272#1:1081\n272#1:1088\n291#1:1427\n291#1:1434\n409#1:1679\n409#1:1686\n430#1:2025\n430#1:2032\n*E\n"})
public final class RepairApi extends ApiClient {
   @NotNull
   public static final Companion Companion = new Companion((DefaultConstructorMarker)null);
   @NotNull
   private static final Lazy<String> defaultBasePath$delegate = LazyKt.lazy(RepairApi::f);

   public RepairApi(@NotNull String basePath, @NotNull OkHttpClient client) {
      Intrinsics.checkNotNullParameter(basePath, "");
      Intrinsics.checkNotNullParameter(client, "");
      super(basePath, (Call.Factory)client);
   }

   @NotNull
   public final CompileResponse a(@NotNull CompileRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      ApiResponse var2 = this.a(var1);
      Iterable var5 = (Iterable)KClasses.getDeclaredMemberProperties(Reflection.getOrCreateKotlinClass(var1.getClass()));
      boolean var6 = false;
      Iterator var7 = var5.iterator();

      Object var10000;
      while(true) {
         if (!var7.hasNext()) {
            var10000 = null;
            break;
         }

         Object var8 = var7.next();
         KProperty1 var9 = (KProperty1)var8;
         boolean var10 = false;
         if (Intrinsics.areEqual(var9.getName(), "requestId") || Intrinsics.areEqual(var9.getName(), "operationId")) {
            var10000 = var8;
            break;
         }
      }

      label80: {
         KProperty1 var22 = (KProperty1)var10000;
         if (var22 != null) {
            KProperty1.Getter var23 = var22.getGetter();
            if (var23 != null) {
               Object[] var14 = new Object[]{var1};
               var10000 = var23.call(var14);
               break label80;
            }
         }

         var10000 = null;
      }

      Object var4 = var10000;
      String var3 = var4 instanceof String ? (String)var4 : null;
      switch (RepairApi.WhenMappings.$EnumSwitchMapping$0[var2.a().ordinal()]) {
         case 1:
            Intrinsics.checkNotNull(var2);
            var10000 = ((Success)var2).a();
            Intrinsics.checkNotNull(var10000);
            return (CompileResponse)var10000;
         case 2:
            throw new UnsupportedOperationException("Client does not support Informational responses.");
         case 3:
            throw new UnsupportedOperationException("Client does not support Redirection responses.");
         case 4:
            Intrinsics.checkNotNull(var2);
            ClientError var13 = (ClientError)var2;
            if (var13.a() != 429) {
               StringBuilder var18 = new StringBuilder();
               boolean var21 = false;
               int var10001 = var13.a();
               String var34 = var13.a();
               if (var34 == null) {
                  var34 = "";
               }

               var18.append("Client error : " + var10001 + " " + var34);
               if (var3 != null) {
                  boolean var11 = false;
                  var18.append(" Request ID: " + var3);
               }

               String var32 = var18.toString();
               Intrinsics.checkNotNullExpressionValue(var32, "");
               String var16 = var32;
               throw new ClientException(var16, var13.a(), var2);
            } else {
               label72: {
                  List var26 = (List)var13.a().get("retry-after");
                  if (var26 != null) {
                     String var27 = (String)CollectionsKt.firstOrNull(var26);
                     if (var27 != null) {
                        var28 = StringsKt.toLongOrNull(var27);
                        break label72;
                     }
                  }

                  var28 = null;
               }

               Long var15 = var28;
               List var29 = (List)var13.a().get("quota-key");
               String var17 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-request-delayed");
               String var19 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-limit-reason");
               String var20 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               RateLimitExceptionKt.a(this.b(), var15, var17);
               if (Intrinsics.areEqual(var19, "1") && var15 != null) {
                  throw new RequestDelayedException(var15);
               }

               throw new RateLimitException(var15, var20, (String)null, (String)null, (Throwable)null, 28, (DefaultConstructorMarker)null);
            }
         case 5:
            Intrinsics.checkNotNull(var2);
            ServerError var12 = (ServerError)var2;
            ServerException var25 = new ServerException;
            int var10002 = var12.a();
            String var10003 = var12.a();
            if (var10003 == null) {
               var10003 = "";
            }

            var25.<init>("Server error : " + var10002 + " " + var10003 + " " + var12.a(), var12.a(), var2);
            throw var25;
         default:
            throw new NoWhenBranchMatchedException();
      }
   }

   @NotNull
   public final Future<CompileResponse> a(@NotNull CompileRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      Future var2 = this.b(var1);
      return var2;
   }

   @NotNull
   public final ApiResponse<CompileResponse> a(@NotNull CompileRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final Future<CompileResponse> b(@NotNull CompileRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final RequestConfig<CompileRequest> a(@NotNull CompileRequest var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      Map var3 = (Map)(new LinkedHashMap());
      Map var4 = (Map)(new LinkedHashMap());
      var4.put("Content-Type", "application/json");
      var4.put("Accept", "application/json");
      RequestMethod var5 = RequestMethod.POST;
      return new RequestConfig<CompileRequest>(var5, "/any/compile", var4, (Map)null, var3, false, var1, 8, (DefaultConstructorMarker)null);
   }

   @NotNull
   public final DiagnosticsResponse a(@NotNull DiagnosticsRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      ApiResponse var2 = this.a(var1);
      Iterable var5 = (Iterable)KClasses.getDeclaredMemberProperties(Reflection.getOrCreateKotlinClass(var1.getClass()));
      boolean var6 = false;
      Iterator var7 = var5.iterator();

      Object var10000;
      while(true) {
         if (!var7.hasNext()) {
            var10000 = null;
            break;
         }

         Object var8 = var7.next();
         KProperty1 var9 = (KProperty1)var8;
         boolean var10 = false;
         if (Intrinsics.areEqual(var9.getName(), "requestId") || Intrinsics.areEqual(var9.getName(), "operationId")) {
            var10000 = var8;
            break;
         }
      }

      label80: {
         KProperty1 var22 = (KProperty1)var10000;
         if (var22 != null) {
            KProperty1.Getter var23 = var22.getGetter();
            if (var23 != null) {
               Object[] var14 = new Object[]{var1};
               var10000 = var23.call(var14);
               break label80;
            }
         }

         var10000 = null;
      }

      Object var4 = var10000;
      String var3 = var4 instanceof String ? (String)var4 : null;
      switch (RepairApi.WhenMappings.$EnumSwitchMapping$0[var2.a().ordinal()]) {
         case 1:
            Intrinsics.checkNotNull(var2);
            var10000 = ((Success)var2).a();
            Intrinsics.checkNotNull(var10000);
            return (DiagnosticsResponse)var10000;
         case 2:
            throw new UnsupportedOperationException("Client does not support Informational responses.");
         case 3:
            throw new UnsupportedOperationException("Client does not support Redirection responses.");
         case 4:
            Intrinsics.checkNotNull(var2);
            ClientError var13 = (ClientError)var2;
            if (var13.a() != 429) {
               StringBuilder var18 = new StringBuilder();
               boolean var21 = false;
               int var10001 = var13.a();
               String var34 = var13.a();
               if (var34 == null) {
                  var34 = "";
               }

               var18.append("Client error : " + var10001 + " " + var34);
               if (var3 != null) {
                  boolean var11 = false;
                  var18.append(" Request ID: " + var3);
               }

               String var32 = var18.toString();
               Intrinsics.checkNotNullExpressionValue(var32, "");
               String var16 = var32;
               throw new ClientException(var16, var13.a(), var2);
            } else {
               label72: {
                  List var26 = (List)var13.a().get("retry-after");
                  if (var26 != null) {
                     String var27 = (String)CollectionsKt.firstOrNull(var26);
                     if (var27 != null) {
                        var28 = StringsKt.toLongOrNull(var27);
                        break label72;
                     }
                  }

                  var28 = null;
               }

               Long var15 = var28;
               List var29 = (List)var13.a().get("quota-key");
               String var17 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-request-delayed");
               String var19 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-limit-reason");
               String var20 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               RateLimitExceptionKt.a(this.b(), var15, var17);
               if (Intrinsics.areEqual(var19, "1") && var15 != null) {
                  throw new RequestDelayedException(var15);
               }

               throw new RateLimitException(var15, var20, (String)null, (String)null, (Throwable)null, 28, (DefaultConstructorMarker)null);
            }
         case 5:
            Intrinsics.checkNotNull(var2);
            ServerError var12 = (ServerError)var2;
            ServerException var25 = new ServerException;
            int var10002 = var12.a();
            String var10003 = var12.a();
            if (var10003 == null) {
               var10003 = "";
            }

            var25.<init>("Server error : " + var10002 + " " + var10003 + " " + var12.a(), var12.a(), var2);
            throw var25;
         default:
            throw new NoWhenBranchMatchedException();
      }
   }

   @NotNull
   public final Future<DiagnosticsResponse> a(@NotNull DiagnosticsRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      Future var2 = this.b(var1);
      return var2;
   }

   @NotNull
   public final ApiResponse<DiagnosticsResponse> a(@NotNull DiagnosticsRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final Future<DiagnosticsResponse> b(@NotNull DiagnosticsRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   @NotNull
   public final RequestConfig<DiagnosticsRequest> a(@NotNull DiagnosticsRequest var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      Map var3 = (Map)(new LinkedHashMap());
      Map var4 = (Map)(new LinkedHashMap());
      var4.put("Content-Type", "application/json");
      var4.put("Accept", "application/json");
      RequestMethod var5 = RequestMethod.POST;
      return new RequestConfig<DiagnosticsRequest>(var5, "/any/lint", var4, (Map)null, var3, false, var1, 8, (DefaultConstructorMarker)null);
   }

   /** @deprecated */
   @Deprecated(
      message = "This operation is deprecated."
   )
   @NotNull
   public final DiagnosticsResponse b(@NotNull DiagnosticsRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      ApiResponse var2 = this.b(var1);
      Iterable var5 = (Iterable)KClasses.getDeclaredMemberProperties(Reflection.getOrCreateKotlinClass(var1.getClass()));
      boolean var6 = false;
      Iterator var7 = var5.iterator();

      Object var10000;
      while(true) {
         if (!var7.hasNext()) {
            var10000 = null;
            break;
         }

         Object var8 = var7.next();
         KProperty1 var9 = (KProperty1)var8;
         boolean var10 = false;
         if (Intrinsics.areEqual(var9.getName(), "requestId") || Intrinsics.areEqual(var9.getName(), "operationId")) {
            var10000 = var8;
            break;
         }
      }

      label80: {
         KProperty1 var22 = (KProperty1)var10000;
         if (var22 != null) {
            KProperty1.Getter var23 = var22.getGetter();
            if (var23 != null) {
               Object[] var14 = new Object[]{var1};
               var10000 = var23.call(var14);
               break label80;
            }
         }

         var10000 = null;
      }

      Object var4 = var10000;
      String var3 = var4 instanceof String ? (String)var4 : null;
      switch (RepairApi.WhenMappings.$EnumSwitchMapping$0[var2.a().ordinal()]) {
         case 1:
            Intrinsics.checkNotNull(var2);
            var10000 = ((Success)var2).a();
            Intrinsics.checkNotNull(var10000);
            return (DiagnosticsResponse)var10000;
         case 2:
            throw new UnsupportedOperationException("Client does not support Informational responses.");
         case 3:
            throw new UnsupportedOperationException("Client does not support Redirection responses.");
         case 4:
            Intrinsics.checkNotNull(var2);
            ClientError var13 = (ClientError)var2;
            if (var13.a() != 429) {
               StringBuilder var18 = new StringBuilder();
               boolean var21 = false;
               int var10001 = var13.a();
               String var34 = var13.a();
               if (var34 == null) {
                  var34 = "";
               }

               var18.append("Client error : " + var10001 + " " + var34);
               if (var3 != null) {
                  boolean var11 = false;
                  var18.append(" Request ID: " + var3);
               }

               String var32 = var18.toString();
               Intrinsics.checkNotNullExpressionValue(var32, "");
               String var16 = var32;
               throw new ClientException(var16, var13.a(), var2);
            } else {
               label72: {
                  List var26 = (List)var13.a().get("retry-after");
                  if (var26 != null) {
                     String var27 = (String)CollectionsKt.firstOrNull(var26);
                     if (var27 != null) {
                        var28 = StringsKt.toLongOrNull(var27);
                        break label72;
                     }
                  }

                  var28 = null;
               }

               Long var15 = var28;
               List var29 = (List)var13.a().get("quota-key");
               String var17 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-request-delayed");
               String var19 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               var29 = (List)var13.a().get("x-limit-reason");
               String var20 = var29 != null ? (String)CollectionsKt.firstOrNull(var29) : null;
               RateLimitExceptionKt.a(this.b(), var15, var17);
               if (Intrinsics.areEqual(var19, "1") && var15 != null) {
                  throw new RequestDelayedException(var15);
               }

               throw new RateLimitException(var15, var20, (String)null, (String)null, (Throwable)null, 28, (DefaultConstructorMarker)null);
            }
         case 5:
            Intrinsics.checkNotNull(var2);
            ServerError var12 = (ServerError)var2;
            ServerException var25 = new ServerException;
            int var10002 = var12.a();
            String var10003 = var12.a();
            if (var10003 == null) {
               var10003 = "";
            }

            var25.<init>("Server error : " + var10002 + " " + var10003 + " " + var12.a(), var12.a(), var2);
            throw var25;
         default:
            throw new NoWhenBranchMatchedException();
      }
   }

   /** @deprecated */
   @Deprecated(
      message = "This operation is deprecated."
   )
   @NotNull
   public final Future<DiagnosticsResponse> c(@NotNull DiagnosticsRequest var1) throws IllegalStateException, IOException, UnsupportedOperationException, ClientException, RateLimitException, ServerException {
      Intrinsics.checkNotNullParameter(var1, "");
      Future var2 = this.d(var1);
      return var2;
   }

   /** @deprecated */
   @Deprecated(
      message = "This operation is deprecated."
   )
   @NotNull
   public final ApiResponse<DiagnosticsResponse> b(@NotNull DiagnosticsRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   /** @deprecated */
   @Deprecated(
      message = "This operation is deprecated."
   )
   @NotNull
   public final Future<DiagnosticsResponse> d(@NotNull DiagnosticsRequest var1) throws IllegalStateException, IOException {
      // $FF: Couldn't be decompiled
   }

   /** @deprecated */
   @Deprecated(
      message = "This operation is deprecated."
   )
   @NotNull
   public final RequestConfig<DiagnosticsRequest> b(@NotNull DiagnosticsRequest var1) {
      Intrinsics.checkNotNullParameter(var1, "");
      Map var3 = (Map)(new LinkedHashMap());
      Map var4 = (Map)(new LinkedHashMap());
      var4.put("Content-Type", "application/json");
      var4.put("Accept", "application/json");
      RequestMethod var5 = RequestMethod.POST;
      return new RequestConfig<DiagnosticsRequest>(var5, "/python/lint", var4, (Map)null, var3, false, var1, 8, (DefaultConstructorMarker)null);
   }

   private final String a(String var1) {
      return (String)(new HttpUrl.Builder()).scheme("http").host("localhost").addPathSegment(var1).build().encodedPathSegments().get(0);
   }

   @NotNull
   public final RepairApi a(@Nullable String var1) {
      ApiClient.Companion var10000 = ApiClient.Companion;
      String var10001 = var1;
      if (var1 == null) {
         var10001 = "";
      }

      var10000.c(var10001);
      return this;
   }

   private static final String f() {
      return System.getProperties().getProperty("ai.zencoder.generated.client.repair.baseUrl", "/repair");
   }

   public RepairApi() {
      this((String)null, (OkHttpClient)null, 3, (DefaultConstructorMarker)null);
   }

   @NotNull
   public static final String a() {
      return Companion.a();
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R!\u0010\u0004\u001a\u00020\u00058FX\u0087\u0084\u0002\u00a2\u0006\u0012\n\u0004\b\t\u0010\n\u0012\u0004\b\u0006\u0010\u0003\u001a\u0004\b\u0007\u0010\b\u00a8\u0006\u000b"},
      d2 = {"Lai/zencoder/generated/client/repair/apis/RepairApi$Companion;", "", "<init>", "()V", "defaultBasePath", "", "getDefaultBasePath$annotations", "getDefaultBasePath", "()Ljava/lang/String;", "defaultBasePath$delegate", "Lkotlin/Lazy;", "zencoder"}
   )
   public static final class Companion {
      private Companion() {
      }

      @NotNull
      public final String a() {
         Lazy var1 = RepairApi.defaultBasePath$delegate;
         Object var10000 = var1.getValue();
         Intrinsics.checkNotNullExpressionValue(var10000, "");
         return (String)var10000;
      }
   }
}
