package ai.zencoder.generated.client.repair.infrastructure;

import kotlin.Metadata;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.RequestBody.Companion;
import org.jetbrains.annotations.NotNull;

@Metadata(
   mv = {2, 1, 0},
   k = 2,
   xi = 48,
   d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\"\u0011\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"},
   d2 = {"EMPTY_REQUEST", "Lokhttp3/RequestBody;", "getEMPTY_REQUEST", "()Lokhttp3/RequestBody;", "zencoder"}
)
public final class ApiClientKt {
   @NotNull
   private static final RequestBody EMPTY_REQUEST;

   @NotNull
   public static final RequestBody a() {
      return EMPTY_REQUEST;
   }

   static {
      EMPTY_REQUEST = Companion.create$default(RequestBody.Companion, new byte[0], (MediaType)null, 0, 0, 7, (Object)null);
   }
}
