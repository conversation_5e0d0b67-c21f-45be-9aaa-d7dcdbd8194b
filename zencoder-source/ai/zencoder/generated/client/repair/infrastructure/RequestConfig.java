package ai.zencoder.generated.client.repair.infrastructure;

import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010%\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0002\b\u001c\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u0000*\u0004\b\u0000\u0010\u00012\u00020\u0002Bs\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0014\b\u0002\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\b\u0012\u0014\b\u0002\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00020\b\u0012\u001a\b\u0002\u0010\n\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u000b0\b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00018\u0000\u00a2\u0006\u0004\b\u000f\u0010\u0010J\t\u0010\u001e\u001a\u00020\u0004H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0006H\u00c6\u0003J\u0015\u0010 \u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\bH\u00c6\u0003J\u0015\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00020\bH\u00c6\u0003J\u001b\u0010\"\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u000b0\bH\u00c6\u0003J\t\u0010#\u001a\u00020\rH\u00c6\u0003J\u0010\u0010$\u001a\u0004\u0018\u00018\u0000H\u00c6\u0003\u00a2\u0006\u0002\u0010\u001cJ\u0086\u0001\u0010%\u001a\b\u0012\u0004\u0012\u00028\u00000\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00062\u0014\b\u0002\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\b2\u0014\b\u0002\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00020\b2\u001a\b\u0002\u0010\n\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u000b0\b2\b\b\u0002\u0010\f\u001a\u00020\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00018\u0000H\u00c6\u0001\u00a2\u0006\u0002\u0010&J\u0013\u0010'\u001a\u00020\r2\b\u0010(\u001a\u0004\u0018\u00010\u0002H\u00d6\u0003J\t\u0010)\u001a\u00020*H\u00d6\u0001J\t\u0010+\u001a\u00020\u0006H\u00d6\u0001R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u001d\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u001d\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0016R#\u0010\n\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u000b0\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0016R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0015\u0010\u000e\u001a\u0004\u0018\u00018\u0000\u00a2\u0006\n\n\u0002\u0010\u001d\u001a\u0004\b\u001b\u0010\u001c\u00a8\u0006,"},
   d2 = {"Lai/zencoder/generated/client/repair/infrastructure/RequestConfig;", "T", "", "method", "Lai/zencoder/generated/client/repair/infrastructure/RequestMethod;", "path", "", "headers", "", "params", "query", "", "requiresAuthentication", "", "body", "<init>", "(Lai/zencoder/generated/client/repair/infrastructure/RequestMethod;Ljava/lang/String;Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;ZLjava/lang/Object;)V", "getMethod", "()Lai/zencoder/generated/client/repair/infrastructure/RequestMethod;", "getPath", "()Ljava/lang/String;", "getHeaders", "()Ljava/util/Map;", "getParams", "getQuery", "getRequiresAuthentication", "()Z", "getBody", "()Ljava/lang/Object;", "Ljava/lang/Object;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "(Lai/zencoder/generated/client/repair/infrastructure/RequestMethod;Ljava/lang/String;Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;ZLjava/lang/Object;)Lai/zencoder/generated/client/repair/infrastructure/RequestConfig;", "equals", "other", "hashCode", "", "toString", "zencoder"}
)
public final class RequestConfig<T> {
   @NotNull
   private final RequestMethod method;
   @NotNull
   private final String path;
   @NotNull
   private final Map<String, String> headers;
   @NotNull
   private final Map<String, Object> params;
   @NotNull
   private final Map<String, List<String>> query;
   private final boolean requiresAuthentication;
   @Nullable
   private final T body;

   public RequestConfig(@NotNull RequestMethod method, @NotNull String path, @NotNull Map<String, String> headers, @NotNull Map<String, Object> params, @NotNull Map<String, List<String>> query, boolean requiresAuthentication, @Nullable T body) {
      Intrinsics.checkNotNullParameter(method, "");
      Intrinsics.checkNotNullParameter(path, "");
      Intrinsics.checkNotNullParameter(headers, "");
      Intrinsics.checkNotNullParameter(params, "");
      Intrinsics.checkNotNullParameter(query, "");
      super();
      this.method = method;
      this.path = path;
      this.headers = headers;
      this.params = params;
      this.query = query;
      this.requiresAuthentication = requiresAuthentication;
      this.body = (T)body;
   }

   @NotNull
   public final RequestMethod a() {
      return this.method;
   }

   @NotNull
   public final String a() {
      return this.path;
   }

   @NotNull
   public final Map<String, String> a() {
      return this.headers;
   }

   @NotNull
   public final Map<String, Object> b() {
      return this.params;
   }

   @NotNull
   public final Map<String, List<String>> c() {
      return this.query;
   }

   public final boolean a() {
      return this.requiresAuthentication;
   }

   @Nullable
   public final T a() {
      return this.body;
   }

   @NotNull
   public final RequestMethod b() {
      return this.method;
   }

   @NotNull
   public final String b() {
      return this.path;
   }

   @NotNull
   public final Map<String, String> d() {
      return this.headers;
   }

   @NotNull
   public final Map<String, Object> e() {
      return this.params;
   }

   @NotNull
   public final Map<String, List<String>> f() {
      return this.query;
   }

   public final boolean b() {
      return this.requiresAuthentication;
   }

   @Nullable
   public final T b() {
      return this.body;
   }

   @NotNull
   public final RequestConfig<T> a(@NotNull RequestMethod var1, @NotNull String var2, @NotNull Map<String, String> var3, @NotNull Map<String, Object> var4, @NotNull Map<String, List<String>> var5, boolean var6, @Nullable T var7) {
      Intrinsics.checkNotNullParameter(var1, "");
      Intrinsics.checkNotNullParameter(var2, "");
      Intrinsics.checkNotNullParameter(var3, "");
      Intrinsics.checkNotNullParameter(var4, "");
      Intrinsics.checkNotNullParameter(var5, "");
      return new RequestConfig<T>(var1, var2, var3, var4, var5, var6, var7);
   }

   @NotNull
   public String toString() {
      return "RequestConfig(method=" + this.method + ", path=" + this.path + ", headers=" + this.headers + ", params=" + this.params + ", query=" + this.query + ", requiresAuthentication=" + this.requiresAuthentication + ", body=" + this.body + ")";
   }

   public int hashCode() {
      int var1 = this.method.hashCode();
      var1 = var1 * 31 + this.path.hashCode();
      var1 = var1 * 31 + this.headers.hashCode();
      var1 = var1 * 31 + this.params.hashCode();
      var1 = var1 * 31 + this.query.hashCode();
      var1 = var1 * 31 + Boolean.hashCode(this.requiresAuthentication);
      var1 = var1 * 31 + (this.body == null ? 0 : this.body.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof RequestConfig)) {
         return false;
      } else {
         RequestConfig var2 = (RequestConfig)other;
         if (this.method != var2.method) {
            return false;
         } else if (!Intrinsics.areEqual(this.path, var2.path)) {
            return false;
         } else if (!Intrinsics.areEqual(this.headers, var2.headers)) {
            return false;
         } else if (!Intrinsics.areEqual(this.params, var2.params)) {
            return false;
         } else if (!Intrinsics.areEqual(this.query, var2.query)) {
            return false;
         } else if (this.requiresAuthentication != var2.requiresAuthentication) {
            return false;
         } else {
            return Intrinsics.areEqual(this.body, var2.body);
         }
      }
   }
}
