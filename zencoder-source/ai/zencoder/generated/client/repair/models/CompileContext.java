package ai.zencoder.generated.client.repair.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0011\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B[\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003\u00a2\u0006\u0004\b\n\u0010\u000bJ\u0011\u0010\u0013\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0014\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u0011\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0017\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003H\u00c6\u0003J]\u0010\u0018\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u0010\b\u0002\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00032\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u0006H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u001b\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\rR\u001b\u0010\t\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\r\u00a8\u0006\u001f"},
   d2 = {"Lai/zencoder/generated/client/repair/models/CompileContext;", "", "projectConfigurations", "", "externallyDefinedBodySymbols", "importsSection", "", "openedTabs", "Lai/zencoder/generated/client/repair/models/EditorTab;", "unitTestExamples", "<init>", "(Ljava/util/List;Ljava/util/List;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V", "getProjectConfigurations", "()Ljava/util/List;", "getExternallyDefinedBodySymbols", "getImportsSection", "()Ljava/lang/String;", "getOpenedTabs", "getUnitTestExamples", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class CompileContext {
   @Nullable
   private final List<Object> projectConfigurations;
   @Nullable
   private final List<Object> externallyDefinedBodySymbols;
   @Nullable
   private final String importsSection;
   @Nullable
   private final List<EditorTab> openedTabs;
   @Nullable
   private final List<EditorTab> unitTestExamples;

   public CompileContext(@Nullable List<? extends Object> projectConfigurations, @Nullable List<? extends Object> externallyDefinedBodySymbols, @Nullable String importsSection, @Nullable List<EditorTab> openedTabs, @Nullable List<EditorTab> unitTestExamples) {
      this.projectConfigurations = projectConfigurations;
      this.externallyDefinedBodySymbols = externallyDefinedBodySymbols;
      this.importsSection = importsSection;
      this.openedTabs = openedTabs;
      this.unitTestExamples = unitTestExamples;
   }

   @JsonProperty("projectConfigurations")
   @Nullable
   public final List<Object> getProjectConfigurations() {
      return this.projectConfigurations;
   }

   @JsonProperty("externallyDefinedBodySymbols")
   @Nullable
   public final List<Object> getExternallyDefinedBodySymbols() {
      return this.externallyDefinedBodySymbols;
   }

   @JsonProperty("importsSection")
   @Nullable
   public final String getImportsSection() {
      return this.importsSection;
   }

   @JsonProperty("openedTabs")
   @Nullable
   public final List<EditorTab> getOpenedTabs() {
      return this.openedTabs;
   }

   @JsonProperty("unitTestExamples")
   @Nullable
   public final List<EditorTab> getUnitTestExamples() {
      return this.unitTestExamples;
   }

   @Nullable
   public final List<Object> component1() {
      return this.projectConfigurations;
   }

   @Nullable
   public final List<Object> component2() {
      return this.externallyDefinedBodySymbols;
   }

   @Nullable
   public final String component3() {
      return this.importsSection;
   }

   @Nullable
   public final List<EditorTab> component4() {
      return this.openedTabs;
   }

   @Nullable
   public final List<EditorTab> component5() {
      return this.unitTestExamples;
   }

   @NotNull
   public final CompileContext copy(@Nullable List<? extends Object> projectConfigurations, @Nullable List<? extends Object> externallyDefinedBodySymbols, @Nullable String importsSection, @Nullable List<EditorTab> openedTabs, @Nullable List<EditorTab> unitTestExamples) {
      return new CompileContext(projectConfigurations, externallyDefinedBodySymbols, importsSection, openedTabs, unitTestExamples);
   }

   @NotNull
   public String toString() {
      return "CompileContext(projectConfigurations=" + this.projectConfigurations + ", externallyDefinedBodySymbols=" + this.externallyDefinedBodySymbols + ", importsSection=" + this.importsSection + ", openedTabs=" + this.openedTabs + ", unitTestExamples=" + this.unitTestExamples + ")";
   }

   public int hashCode() {
      int var1 = this.projectConfigurations == null ? 0 : this.projectConfigurations.hashCode();
      var1 = var1 * 31 + (this.externallyDefinedBodySymbols == null ? 0 : this.externallyDefinedBodySymbols.hashCode());
      var1 = var1 * 31 + (this.importsSection == null ? 0 : this.importsSection.hashCode());
      var1 = var1 * 31 + (this.openedTabs == null ? 0 : this.openedTabs.hashCode());
      var1 = var1 * 31 + (this.unitTestExamples == null ? 0 : this.unitTestExamples.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CompileContext)) {
         return false;
      } else {
         CompileContext var2 = (CompileContext)other;
         if (!Intrinsics.areEqual(this.projectConfigurations, var2.projectConfigurations)) {
            return false;
         } else if (!Intrinsics.areEqual(this.externallyDefinedBodySymbols, var2.externallyDefinedBodySymbols)) {
            return false;
         } else if (!Intrinsics.areEqual(this.importsSection, var2.importsSection)) {
            return false;
         } else if (!Intrinsics.areEqual(this.openedTabs, var2.openedTabs)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.unitTestExamples, var2.unitTestExamples);
         }
      }
   }

   public CompileContext() {
      this((List)null, (List)null, (String)null, (List)null, (List)null, 31, (DefaultConstructorMarker)null);
   }
}
