package ai.zencoder.generated.client.repair.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u0012\u0006\u0010\t\u001a\u00020\n\u00a2\u0006\u0004\b\u000b\u0010\fJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0005H\u00c6\u0003J\u000f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\nH\u00c6\u0003J7\u0010\u0019\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u001a\u001a\u00020\u001b2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001d\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0019\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0013\u0010\t\u001a\u00020\n8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\u001f"},
   d2 = {"Lai/zencoder/generated/client/repair/models/CompileRequest;", "", "requestId", "", "attemptCount", "", "files", "", "Lai/zencoder/generated/client/repair/models/CompileContextFile;", "runtimeReport", "Lai/zencoder/generated/client/repair/models/RuntimeReport;", "<init>", "(Ljava/lang/String;ILjava/util/List;Lai/zencoder/generated/client/repair/models/RuntimeReport;)V", "getRequestId", "()Ljava/lang/String;", "getAttemptCount", "()I", "getFiles", "()Ljava/util/List;", "getRuntimeReport", "()Lai/zencoder/generated/client/repair/models/RuntimeReport;", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class CompileRequest {
   @NotNull
   private final String requestId;
   private final int attemptCount;
   @NotNull
   private final List<CompileContextFile> files;
   @NotNull
   private final RuntimeReport runtimeReport;

   public CompileRequest(@NotNull String requestId, int attemptCount, @NotNull List<CompileContextFile> files, @NotNull RuntimeReport runtimeReport) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(files, "");
      Intrinsics.checkNotNullParameter(runtimeReport, "");
      super();
      this.requestId = requestId;
      this.attemptCount = attemptCount;
      this.files = files;
      this.runtimeReport = runtimeReport;
   }

   @JsonProperty("requestId")
   @NotNull
   public final String getRequestId() {
      return this.requestId;
   }

   @JsonProperty("attemptCount")
   public final int getAttemptCount() {
      return this.attemptCount;
   }

   @JsonProperty("files")
   @NotNull
   public final List<CompileContextFile> getFiles() {
      return this.files;
   }

   @JsonProperty("runtimeReport")
   @NotNull
   public final RuntimeReport getRuntimeReport() {
      return this.runtimeReport;
   }

   @NotNull
   public final String component1() {
      return this.requestId;
   }

   public final int component2() {
      return this.attemptCount;
   }

   @NotNull
   public final List<CompileContextFile> component3() {
      return this.files;
   }

   @NotNull
   public final RuntimeReport component4() {
      return this.runtimeReport;
   }

   @NotNull
   public final CompileRequest copy(@NotNull String requestId, int attemptCount, @NotNull List<CompileContextFile> files, @NotNull RuntimeReport runtimeReport) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(files, "");
      Intrinsics.checkNotNullParameter(runtimeReport, "");
      return new CompileRequest(requestId, attemptCount, files, runtimeReport);
   }

   @NotNull
   public String toString() {
      return "CompileRequest(requestId=" + this.requestId + ", attemptCount=" + this.attemptCount + ", files=" + this.files + ", runtimeReport=" + this.runtimeReport + ")";
   }

   public int hashCode() {
      int var1 = this.requestId.hashCode();
      var1 = var1 * 31 + Integer.hashCode(this.attemptCount);
      var1 = var1 * 31 + this.files.hashCode();
      var1 = var1 * 31 + this.runtimeReport.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CompileRequest)) {
         return false;
      } else {
         CompileRequest var2 = (CompileRequest)other;
         if (!Intrinsics.areEqual(this.requestId, var2.requestId)) {
            return false;
         } else if (this.attemptCount != var2.attemptCount) {
            return false;
         } else if (!Intrinsics.areEqual(this.files, var2.files)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.runtimeReport, var2.runtimeReport);
         }
      }
   }
}
