package ai.zencoder.generated.client.repair.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0004\b\n\u0010\u000bJ\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\tH\u00c6\u0003J9\u0010\u0017\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\tH\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u001c\u001a\u00020\tH\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0019\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0015\u0010\b\u001a\u0004\u0018\u00010\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006\u001d"},
   d2 = {"Lai/zencoder/generated/client/repair/models/CompileResponse;", "", "attemptCount", "", "repairStatus", "patches", "", "Lai/zencoder/generated/client/repair/models/OutputPatch;", "message", "", "<init>", "(IILjava/util/List;Ljava/lang/String;)V", "getAttemptCount", "()I", "getRepairStatus", "getPatches", "()Ljava/util/List;", "getMessage", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class CompileResponse {
   private final int attemptCount;
   private final int repairStatus;
   @NotNull
   private final List<OutputPatch> patches;
   @Nullable
   private final String message;

   public CompileResponse(int attemptCount, int repairStatus, @NotNull List<OutputPatch> patches, @Nullable String message) {
      Intrinsics.checkNotNullParameter(patches, "");
      super();
      this.attemptCount = attemptCount;
      this.repairStatus = repairStatus;
      this.patches = patches;
      this.message = message;
   }

   @JsonProperty("attemptCount")
   public final int getAttemptCount() {
      return this.attemptCount;
   }

   @JsonProperty("repairStatus")
   public final int getRepairStatus() {
      return this.repairStatus;
   }

   @JsonProperty("patches")
   @NotNull
   public final List<OutputPatch> getPatches() {
      return this.patches;
   }

   @JsonProperty("message")
   @Nullable
   public final String getMessage() {
      return this.message;
   }

   public final int component1() {
      return this.attemptCount;
   }

   public final int component2() {
      return this.repairStatus;
   }

   @NotNull
   public final List<OutputPatch> component3() {
      return this.patches;
   }

   @Nullable
   public final String component4() {
      return this.message;
   }

   @NotNull
   public final CompileResponse copy(int attemptCount, int repairStatus, @NotNull List<OutputPatch> patches, @Nullable String message) {
      Intrinsics.checkNotNullParameter(patches, "");
      return new CompileResponse(attemptCount, repairStatus, patches, message);
   }

   @NotNull
   public String toString() {
      return "CompileResponse(attemptCount=" + this.attemptCount + ", repairStatus=" + this.repairStatus + ", patches=" + this.patches + ", message=" + this.message + ")";
   }

   public int hashCode() {
      int var1 = Integer.hashCode(this.attemptCount);
      var1 = var1 * 31 + Integer.hashCode(this.repairStatus);
      var1 = var1 * 31 + this.patches.hashCode();
      var1 = var1 * 31 + (this.message == null ? 0 : this.message.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof CompileResponse)) {
         return false;
      } else {
         CompileResponse var2 = (CompileResponse)other;
         if (this.attemptCount != var2.attemptCount) {
            return false;
         } else if (this.repairStatus != var2.repairStatus) {
            return false;
         } else if (!Intrinsics.areEqual(this.patches, var2.patches)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.message, var2.message);
         }
      }
   }
}
