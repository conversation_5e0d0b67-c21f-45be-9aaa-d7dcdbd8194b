package ai.zencoder.generated.client.repair.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001By\u0012\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0001\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003\u00a2\u0006\u0004\b\f\u0010\rJ\u0011\u0010\u0018\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u0011\u0010\u001c\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u001d\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u001e\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003H\u00c6\u0003J{\u0010\u001f\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00032\u0010\b\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\u00032\u0010\b\u0002\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00032\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010 \u001a\u00020!2\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020$H\u00d6\u0001J\t\u0010%\u001a\u00020\u0007H\u00d6\u0001R\u001b\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u001b\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00018G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u001b\u0010\b\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000fR\u001b\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u000fR\u001b\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u000f\u00a8\u0006&"},
   d2 = {"Lai/zencoder/generated/client/repair/models/DiagnosticsContext;", "", "projectConfigurations", "", "externallyDefinedBodySymbols", "targetSymbol", "importsSection", "", "openedTabs", "Lai/zencoder/generated/client/repair/models/EditorTab;", "origDiagnostics", "currDiagnostics", "<init>", "(Ljava/util/List;Ljava/util/List;Ljava/lang/Object;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getProjectConfigurations", "()Ljava/util/List;", "getExternallyDefinedBodySymbols", "getTargetSymbol", "()Ljava/lang/Object;", "getImportsSection", "()Ljava/lang/String;", "getOpenedTabs", "getOrigDiagnostics", "getCurrDiagnostics", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class DiagnosticsContext {
   @Nullable
   private final List<Object> projectConfigurations;
   @Nullable
   private final List<Object> externallyDefinedBodySymbols;
   @Nullable
   private final Object targetSymbol;
   @Nullable
   private final String importsSection;
   @Nullable
   private final List<EditorTab> openedTabs;
   @Nullable
   private final List<Object> origDiagnostics;
   @Nullable
   private final List<Object> currDiagnostics;

   public DiagnosticsContext(@Nullable List<? extends Object> projectConfigurations, @Nullable List<? extends Object> externallyDefinedBodySymbols, @Nullable Object targetSymbol, @Nullable String importsSection, @Nullable List<EditorTab> openedTabs, @Nullable List<? extends Object> origDiagnostics, @Nullable List<? extends Object> currDiagnostics) {
      this.projectConfigurations = projectConfigurations;
      this.externallyDefinedBodySymbols = externallyDefinedBodySymbols;
      this.targetSymbol = targetSymbol;
      this.importsSection = importsSection;
      this.openedTabs = openedTabs;
      this.origDiagnostics = origDiagnostics;
      this.currDiagnostics = currDiagnostics;
   }

   @JsonProperty("projectConfigurations")
   @Nullable
   public final List<Object> getProjectConfigurations() {
      return this.projectConfigurations;
   }

   @JsonProperty("externallyDefinedBodySymbols")
   @Nullable
   public final List<Object> getExternallyDefinedBodySymbols() {
      return this.externallyDefinedBodySymbols;
   }

   @JsonProperty("targetSymbol")
   @Nullable
   public final Object getTargetSymbol() {
      return this.targetSymbol;
   }

   @JsonProperty("importsSection")
   @Nullable
   public final String getImportsSection() {
      return this.importsSection;
   }

   @JsonProperty("openedTabs")
   @Nullable
   public final List<EditorTab> getOpenedTabs() {
      return this.openedTabs;
   }

   @JsonProperty("origDiagnostics")
   @Nullable
   public final List<Object> getOrigDiagnostics() {
      return this.origDiagnostics;
   }

   @JsonProperty("currDiagnostics")
   @Nullable
   public final List<Object> getCurrDiagnostics() {
      return this.currDiagnostics;
   }

   @Nullable
   public final List<Object> component1() {
      return this.projectConfigurations;
   }

   @Nullable
   public final List<Object> component2() {
      return this.externallyDefinedBodySymbols;
   }

   @Nullable
   public final Object component3() {
      return this.targetSymbol;
   }

   @Nullable
   public final String component4() {
      return this.importsSection;
   }

   @Nullable
   public final List<EditorTab> component5() {
      return this.openedTabs;
   }

   @Nullable
   public final List<Object> component6() {
      return this.origDiagnostics;
   }

   @Nullable
   public final List<Object> component7() {
      return this.currDiagnostics;
   }

   @NotNull
   public final DiagnosticsContext copy(@Nullable List<? extends Object> projectConfigurations, @Nullable List<? extends Object> externallyDefinedBodySymbols, @Nullable Object targetSymbol, @Nullable String importsSection, @Nullable List<EditorTab> openedTabs, @Nullable List<? extends Object> origDiagnostics, @Nullable List<? extends Object> currDiagnostics) {
      return new DiagnosticsContext(projectConfigurations, externallyDefinedBodySymbols, targetSymbol, importsSection, openedTabs, origDiagnostics, currDiagnostics);
   }

   @NotNull
   public String toString() {
      return "DiagnosticsContext(projectConfigurations=" + this.projectConfigurations + ", externallyDefinedBodySymbols=" + this.externallyDefinedBodySymbols + ", targetSymbol=" + this.targetSymbol + ", importsSection=" + this.importsSection + ", openedTabs=" + this.openedTabs + ", origDiagnostics=" + this.origDiagnostics + ", currDiagnostics=" + this.currDiagnostics + ")";
   }

   public int hashCode() {
      int var1 = this.projectConfigurations == null ? 0 : this.projectConfigurations.hashCode();
      var1 = var1 * 31 + (this.externallyDefinedBodySymbols == null ? 0 : this.externallyDefinedBodySymbols.hashCode());
      var1 = var1 * 31 + (this.targetSymbol == null ? 0 : this.targetSymbol.hashCode());
      var1 = var1 * 31 + (this.importsSection == null ? 0 : this.importsSection.hashCode());
      var1 = var1 * 31 + (this.openedTabs == null ? 0 : this.openedTabs.hashCode());
      var1 = var1 * 31 + (this.origDiagnostics == null ? 0 : this.origDiagnostics.hashCode());
      var1 = var1 * 31 + (this.currDiagnostics == null ? 0 : this.currDiagnostics.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof DiagnosticsContext)) {
         return false;
      } else {
         DiagnosticsContext var2 = (DiagnosticsContext)other;
         if (!Intrinsics.areEqual(this.projectConfigurations, var2.projectConfigurations)) {
            return false;
         } else if (!Intrinsics.areEqual(this.externallyDefinedBodySymbols, var2.externallyDefinedBodySymbols)) {
            return false;
         } else if (!Intrinsics.areEqual(this.targetSymbol, var2.targetSymbol)) {
            return false;
         } else if (!Intrinsics.areEqual(this.importsSection, var2.importsSection)) {
            return false;
         } else if (!Intrinsics.areEqual(this.openedTabs, var2.openedTabs)) {
            return false;
         } else if (!Intrinsics.areEqual(this.origDiagnostics, var2.origDiagnostics)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.currDiagnostics, var2.currDiagnostics);
         }
      }
   }

   public DiagnosticsContext() {
      this((List)null, (List)null, (Object)null, (String)null, (List)null, (List)null, (List)null, 127, (DefaultConstructorMarker)null);
   }
}
