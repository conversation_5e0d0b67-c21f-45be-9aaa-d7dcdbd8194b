package ai.zencoder.generated.client.repair.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BU\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\u0004\b\r\u0010\u000eJ\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u001e\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\tH\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\fH\u00c6\u0003J]\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00c6\u0001J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010$\u001a\u00020\nH\u00d6\u0001J\t\u0010%\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u0013\u0010\u0005\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010R\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u001b\u0010\b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0015\u0010\u000b\u001a\u0004\u0018\u00010\f8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018\u00a8\u0006&"},
   d2 = {"Lai/zencoder/generated/client/repair/models/DiagnosticsContextFile;", "", "path", "", "origContent", "language", "project", "patchText", "spanOfInterest", "", "", "additionalContext", "Lai/zencoder/generated/client/repair/models/DiagnosticsContext;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Lai/zencoder/generated/client/repair/models/DiagnosticsContext;)V", "getPath", "()Ljava/lang/String;", "getOrigContent", "getLanguage", "getProject", "getPatchText", "getSpanOfInterest", "()Ljava/util/List;", "getAdditionalContext", "()Lai/zencoder/generated/client/repair/models/DiagnosticsContext;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class DiagnosticsContextFile {
   @NotNull
   private final String path;
   @NotNull
   private final String origContent;
   @NotNull
   private final String language;
   @Nullable
   private final String project;
   @Nullable
   private final String patchText;
   @Nullable
   private final List<Integer> spanOfInterest;
   @Nullable
   private final DiagnosticsContext additionalContext;

   public DiagnosticsContextFile(@NotNull String path, @NotNull String origContent, @NotNull String language, @Nullable String project, @Nullable String patchText, @Nullable List<Integer> spanOfInterest, @Nullable DiagnosticsContext additionalContext) {
      Intrinsics.checkNotNullParameter(path, "");
      Intrinsics.checkNotNullParameter(origContent, "");
      Intrinsics.checkNotNullParameter(language, "");
      super();
      this.path = path;
      this.origContent = origContent;
      this.language = language;
      this.project = project;
      this.patchText = patchText;
      this.spanOfInterest = spanOfInterest;
      this.additionalContext = additionalContext;
   }

   @JsonProperty("path")
   @NotNull
   public final String getPath() {
      return this.path;
   }

   @JsonProperty("origContent")
   @NotNull
   public final String getOrigContent() {
      return this.origContent;
   }

   @JsonProperty("language")
   @NotNull
   public final String getLanguage() {
      return this.language;
   }

   @JsonProperty("project")
   @Nullable
   public final String getProject() {
      return this.project;
   }

   @JsonProperty("patchText")
   @Nullable
   public final String getPatchText() {
      return this.patchText;
   }

   @JsonProperty("spanOfInterest")
   @Nullable
   public final List<Integer> getSpanOfInterest() {
      return this.spanOfInterest;
   }

   @JsonProperty("additionalContext")
   @Nullable
   public final DiagnosticsContext getAdditionalContext() {
      return this.additionalContext;
   }

   @NotNull
   public final String component1() {
      return this.path;
   }

   @NotNull
   public final String component2() {
      return this.origContent;
   }

   @NotNull
   public final String component3() {
      return this.language;
   }

   @Nullable
   public final String component4() {
      return this.project;
   }

   @Nullable
   public final String component5() {
      return this.patchText;
   }

   @Nullable
   public final List<Integer> component6() {
      return this.spanOfInterest;
   }

   @Nullable
   public final DiagnosticsContext component7() {
      return this.additionalContext;
   }

   @NotNull
   public final DiagnosticsContextFile copy(@NotNull String path, @NotNull String origContent, @NotNull String language, @Nullable String project, @Nullable String patchText, @Nullable List<Integer> spanOfInterest, @Nullable DiagnosticsContext additionalContext) {
      Intrinsics.checkNotNullParameter(path, "");
      Intrinsics.checkNotNullParameter(origContent, "");
      Intrinsics.checkNotNullParameter(language, "");
      return new DiagnosticsContextFile(path, origContent, language, project, patchText, spanOfInterest, additionalContext);
   }

   @NotNull
   public String toString() {
      return "DiagnosticsContextFile(path=" + this.path + ", origContent=" + this.origContent + ", language=" + this.language + ", project=" + this.project + ", patchText=" + this.patchText + ", spanOfInterest=" + this.spanOfInterest + ", additionalContext=" + this.additionalContext + ")";
   }

   public int hashCode() {
      int var1 = this.path.hashCode();
      var1 = var1 * 31 + this.origContent.hashCode();
      var1 = var1 * 31 + this.language.hashCode();
      var1 = var1 * 31 + (this.project == null ? 0 : this.project.hashCode());
      var1 = var1 * 31 + (this.patchText == null ? 0 : this.patchText.hashCode());
      var1 = var1 * 31 + (this.spanOfInterest == null ? 0 : this.spanOfInterest.hashCode());
      var1 = var1 * 31 + (this.additionalContext == null ? 0 : this.additionalContext.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof DiagnosticsContextFile)) {
         return false;
      } else {
         DiagnosticsContextFile var2 = (DiagnosticsContextFile)other;
         if (!Intrinsics.areEqual(this.path, var2.path)) {
            return false;
         } else if (!Intrinsics.areEqual(this.origContent, var2.origContent)) {
            return false;
         } else if (!Intrinsics.areEqual(this.language, var2.language)) {
            return false;
         } else if (!Intrinsics.areEqual(this.project, var2.project)) {
            return false;
         } else if (!Intrinsics.areEqual(this.patchText, var2.patchText)) {
            return false;
         } else if (!Intrinsics.areEqual(this.spanOfInterest, var2.spanOfInterest)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.additionalContext, var2.additionalContext);
         }
      }
   }
}
