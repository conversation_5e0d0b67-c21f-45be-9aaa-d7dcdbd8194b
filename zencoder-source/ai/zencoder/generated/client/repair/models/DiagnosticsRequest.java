package ai.zencoder.generated.client.repair.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0007H\u00c6\u0003J'\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0013\u0010\u0006\u001a\u00020\u00078G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/repair/models/DiagnosticsRequest;", "", "requestId", "", "attemptCount", "", "file", "Lai/zencoder/generated/client/repair/models/DiagnosticsContextFile;", "<init>", "(Ljava/lang/String;ILai/zencoder/generated/client/repair/models/DiagnosticsContextFile;)V", "getRequestId", "()Ljava/lang/String;", "getAttemptCount", "()I", "getFile", "()Lai/zencoder/generated/client/repair/models/DiagnosticsContextFile;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class DiagnosticsRequest {
   @NotNull
   private final String requestId;
   private final int attemptCount;
   @NotNull
   private final DiagnosticsContextFile file;

   public DiagnosticsRequest(@NotNull String requestId, int attemptCount, @NotNull DiagnosticsContextFile file) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(file, "");
      super();
      this.requestId = requestId;
      this.attemptCount = attemptCount;
      this.file = file;
   }

   @JsonProperty("requestId")
   @NotNull
   public final String getRequestId() {
      return this.requestId;
   }

   @JsonProperty("attemptCount")
   public final int getAttemptCount() {
      return this.attemptCount;
   }

   @JsonProperty("file")
   @NotNull
   public final DiagnosticsContextFile getFile() {
      return this.file;
   }

   @NotNull
   public final String component1() {
      return this.requestId;
   }

   public final int component2() {
      return this.attemptCount;
   }

   @NotNull
   public final DiagnosticsContextFile component3() {
      return this.file;
   }

   @NotNull
   public final DiagnosticsRequest copy(@NotNull String requestId, int attemptCount, @NotNull DiagnosticsContextFile file) {
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(file, "");
      return new DiagnosticsRequest(requestId, attemptCount, file);
   }

   @NotNull
   public String toString() {
      return "DiagnosticsRequest(requestId=" + this.requestId + ", attemptCount=" + this.attemptCount + ", file=" + this.file + ")";
   }

   public int hashCode() {
      int var1 = this.requestId.hashCode();
      var1 = var1 * 31 + Integer.hashCode(this.attemptCount);
      var1 = var1 * 31 + this.file.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof DiagnosticsRequest)) {
         return false;
      } else {
         DiagnosticsRequest var2 = (DiagnosticsRequest)other;
         if (!Intrinsics.areEqual(this.requestId, var2.requestId)) {
            return false;
         } else if (this.attemptCount != var2.attemptCount) {
            return false;
         } else {
            return Intrinsics.areEqual(this.file, var2.file);
         }
      }
   }
}
