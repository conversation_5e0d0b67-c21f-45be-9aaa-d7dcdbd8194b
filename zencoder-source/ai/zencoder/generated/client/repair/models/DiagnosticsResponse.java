package ai.zencoder.generated.client.repair.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0006H\u00c6\u0003J'\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0013\u0010\u0005\u001a\u00020\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/repair/models/DiagnosticsResponse;", "", "attemptCount", "", "repairStatus", "patch", "Lai/zencoder/generated/client/repair/models/OutputPatch;", "<init>", "(IILai/zencoder/generated/client/repair/models/OutputPatch;)V", "getAttemptCount", "()I", "getRepairStatus", "getPatch", "()Lai/zencoder/generated/client/repair/models/OutputPatch;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "toString", "", "zencoder"}
)
public final class DiagnosticsResponse {
   private final int attemptCount;
   private final int repairStatus;
   @NotNull
   private final OutputPatch patch;

   public DiagnosticsResponse(int attemptCount, int repairStatus, @NotNull OutputPatch patch) {
      Intrinsics.checkNotNullParameter(patch, "");
      super();
      this.attemptCount = attemptCount;
      this.repairStatus = repairStatus;
      this.patch = patch;
   }

   @JsonProperty("attemptCount")
   public final int getAttemptCount() {
      return this.attemptCount;
   }

   @JsonProperty("repairStatus")
   public final int getRepairStatus() {
      return this.repairStatus;
   }

   @JsonProperty("patch")
   @NotNull
   public final OutputPatch getPatch() {
      return this.patch;
   }

   public final int component1() {
      return this.attemptCount;
   }

   public final int component2() {
      return this.repairStatus;
   }

   @NotNull
   public final OutputPatch component3() {
      return this.patch;
   }

   @NotNull
   public final DiagnosticsResponse copy(int attemptCount, int repairStatus, @NotNull OutputPatch patch) {
      Intrinsics.checkNotNullParameter(patch, "");
      return new DiagnosticsResponse(attemptCount, repairStatus, patch);
   }

   @NotNull
   public String toString() {
      return "DiagnosticsResponse(attemptCount=" + this.attemptCount + ", repairStatus=" + this.repairStatus + ", patch=" + this.patch + ")";
   }

   public int hashCode() {
      int var1 = Integer.hashCode(this.attemptCount);
      var1 = var1 * 31 + Integer.hashCode(this.repairStatus);
      var1 = var1 * 31 + this.patch.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof DiagnosticsResponse)) {
         return false;
      } else {
         DiagnosticsResponse var2 = (DiagnosticsResponse)other;
         if (this.attemptCount != var2.attemptCount) {
            return false;
         } else if (this.repairStatus != var2.repairStatus) {
            return false;
         } else {
            return Intrinsics.areEqual(this.patch, var2.patch);
         }
      }
   }
}
