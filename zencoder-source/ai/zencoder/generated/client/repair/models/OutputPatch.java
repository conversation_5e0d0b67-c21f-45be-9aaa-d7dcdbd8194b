package ai.zencoder.generated.client.repair.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\b\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\t\u0010\nJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u00c6\u0003J\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J9\u0010\u0015\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u0007H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR\u0019\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\b\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\f\u00a8\u0006\u001b"},
   d2 = {"Lai/zencoder/generated/client/repair/models/OutputPatch;", "", "path", "", "patchText", "spanOfInterest", "", "", "project", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;)V", "getPath", "()Ljava/lang/String;", "getPatchText", "getSpanOfInterest", "()Ljava/util/List;", "getProject", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "toString", "zencoder"}
)
public final class OutputPatch {
   @NotNull
   private final String path;
   @NotNull
   private final String patchText;
   @NotNull
   private final List<Integer> spanOfInterest;
   @Nullable
   private final String project;

   public OutputPatch(@NotNull String path, @NotNull String patchText, @NotNull List<Integer> spanOfInterest, @Nullable String project) {
      Intrinsics.checkNotNullParameter(path, "");
      Intrinsics.checkNotNullParameter(patchText, "");
      Intrinsics.checkNotNullParameter(spanOfInterest, "");
      super();
      this.path = path;
      this.patchText = patchText;
      this.spanOfInterest = spanOfInterest;
      this.project = project;
   }

   @JsonProperty("path")
   @NotNull
   public final String getPath() {
      return this.path;
   }

   @JsonProperty("patchText")
   @NotNull
   public final String getPatchText() {
      return this.patchText;
   }

   @JsonProperty("spanOfInterest")
   @NotNull
   public final List<Integer> getSpanOfInterest() {
      return this.spanOfInterest;
   }

   @JsonProperty("project")
   @Nullable
   public final String getProject() {
      return this.project;
   }

   @NotNull
   public final String component1() {
      return this.path;
   }

   @NotNull
   public final String component2() {
      return this.patchText;
   }

   @NotNull
   public final List<Integer> component3() {
      return this.spanOfInterest;
   }

   @Nullable
   public final String component4() {
      return this.project;
   }

   @NotNull
   public final OutputPatch copy(@NotNull String path, @NotNull String patchText, @NotNull List<Integer> spanOfInterest, @Nullable String project) {
      Intrinsics.checkNotNullParameter(path, "");
      Intrinsics.checkNotNullParameter(patchText, "");
      Intrinsics.checkNotNullParameter(spanOfInterest, "");
      return new OutputPatch(path, patchText, spanOfInterest, project);
   }

   @NotNull
   public String toString() {
      return "OutputPatch(path=" + this.path + ", patchText=" + this.patchText + ", spanOfInterest=" + this.spanOfInterest + ", project=" + this.project + ")";
   }

   public int hashCode() {
      int var1 = this.path.hashCode();
      var1 = var1 * 31 + this.patchText.hashCode();
      var1 = var1 * 31 + this.spanOfInterest.hashCode();
      var1 = var1 * 31 + (this.project == null ? 0 : this.project.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof OutputPatch)) {
         return false;
      } else {
         OutputPatch var2 = (OutputPatch)other;
         if (!Intrinsics.areEqual(this.path, var2.path)) {
            return false;
         } else if (!Intrinsics.areEqual(this.patchText, var2.patchText)) {
            return false;
         } else if (!Intrinsics.areEqual(this.spanOfInterest, var2.spanOfInterest)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.project, var2.project);
         }
      }
   }
}
