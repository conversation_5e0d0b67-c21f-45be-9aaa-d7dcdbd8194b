package ai.zencoder.generated.client.repair.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B;\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\t\u0010\nJ\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003JA\u0010\u0017\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0004\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\fR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\u0007\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\fR\u0015\u0010\b\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\f\u00a8\u0006\u001e"},
   d2 = {"Lai/zencoder/generated/client/repair/models/RuntimeReport;", "", "stdout", "", "status", "toolchain", "Lai/zencoder/generated/client/repair/models/Toolchain;", "stderr", "report", "<init>", "(Ljava/lang/String;Ljava/lang/String;Lai/zencoder/generated/client/repair/models/Toolchain;Ljava/lang/String;Ljava/lang/String;)V", "getStdout", "()Ljava/lang/String;", "getStatus", "getToolchain", "()Lai/zencoder/generated/client/repair/models/Toolchain;", "getStderr", "getReport", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class RuntimeReport {
   @NotNull
   private final String stdout;
   @NotNull
   private final String status;
   @Nullable
   private final Toolchain toolchain;
   @Nullable
   private final String stderr;
   @Nullable
   private final String report;

   public RuntimeReport(@NotNull String stdout, @NotNull String status, @Nullable Toolchain toolchain, @Nullable String stderr, @Nullable String report) {
      Intrinsics.checkNotNullParameter(stdout, "");
      Intrinsics.checkNotNullParameter(status, "");
      super();
      this.stdout = stdout;
      this.status = status;
      this.toolchain = toolchain;
      this.stderr = stderr;
      this.report = report;
   }

   @JsonProperty("stdout")
   @NotNull
   public final String getStdout() {
      return this.stdout;
   }

   @JsonProperty("status")
   @NotNull
   public final String getStatus() {
      return this.status;
   }

   @JsonProperty("toolchain")
   @Nullable
   public final Toolchain getToolchain() {
      return this.toolchain;
   }

   @JsonProperty("stderr")
   @Nullable
   public final String getStderr() {
      return this.stderr;
   }

   @JsonProperty("report")
   @Nullable
   public final String getReport() {
      return this.report;
   }

   @NotNull
   public final String component1() {
      return this.stdout;
   }

   @NotNull
   public final String component2() {
      return this.status;
   }

   @Nullable
   public final Toolchain component3() {
      return this.toolchain;
   }

   @Nullable
   public final String component4() {
      return this.stderr;
   }

   @Nullable
   public final String component5() {
      return this.report;
   }

   @NotNull
   public final RuntimeReport copy(@NotNull String stdout, @NotNull String status, @Nullable Toolchain toolchain, @Nullable String stderr, @Nullable String report) {
      Intrinsics.checkNotNullParameter(stdout, "");
      Intrinsics.checkNotNullParameter(status, "");
      return new RuntimeReport(stdout, status, toolchain, stderr, report);
   }

   @NotNull
   public String toString() {
      return "RuntimeReport(stdout=" + this.stdout + ", status=" + this.status + ", toolchain=" + this.toolchain + ", stderr=" + this.stderr + ", report=" + this.report + ")";
   }

   public int hashCode() {
      int var1 = this.stdout.hashCode();
      var1 = var1 * 31 + this.status.hashCode();
      var1 = var1 * 31 + (this.toolchain == null ? 0 : this.toolchain.hashCode());
      var1 = var1 * 31 + (this.stderr == null ? 0 : this.stderr.hashCode());
      var1 = var1 * 31 + (this.report == null ? 0 : this.report.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof RuntimeReport)) {
         return false;
      } else {
         RuntimeReport var2 = (RuntimeReport)other;
         if (!Intrinsics.areEqual(this.stdout, var2.stdout)) {
            return false;
         } else if (!Intrinsics.areEqual(this.status, var2.status)) {
            return false;
         } else if (!Intrinsics.areEqual(this.toolchain, var2.toolchain)) {
            return false;
         } else if (!Intrinsics.areEqual(this.stderr, var2.stderr)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.report, var2.report);
         }
      }
   }
}
