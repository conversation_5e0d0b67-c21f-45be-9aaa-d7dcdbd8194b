package ai.zencoder.generated.client.repair.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J9\u0010\u0012\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\nR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\n\u00a8\u0006\u0019"},
   d2 = {"Lai/zencoder/generated/client/repair/models/Toolchain;", "", "buildSystem", "", "testFramework", "assertionFramework", "mockingFramework", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getBuildSystem", "()Ljava/lang/String;", "getTestFramework", "getAssertionFramework", "getMockingFramework", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class Toolchain {
   @Nullable
   private final String buildSystem;
   @Nullable
   private final String testFramework;
   @Nullable
   private final String assertionFramework;
   @Nullable
   private final String mockingFramework;

   public Toolchain(@Nullable String buildSystem, @Nullable String testFramework, @Nullable String assertionFramework, @Nullable String mockingFramework) {
      this.buildSystem = buildSystem;
      this.testFramework = testFramework;
      this.assertionFramework = assertionFramework;
      this.mockingFramework = mockingFramework;
   }

   @JsonProperty("buildSystem")
   @Nullable
   public final String getBuildSystem() {
      return this.buildSystem;
   }

   @JsonProperty("testFramework")
   @Nullable
   public final String getTestFramework() {
      return this.testFramework;
   }

   @JsonProperty("assertionFramework")
   @Nullable
   public final String getAssertionFramework() {
      return this.assertionFramework;
   }

   @JsonProperty("mockingFramework")
   @Nullable
   public final String getMockingFramework() {
      return this.mockingFramework;
   }

   @Nullable
   public final String component1() {
      return this.buildSystem;
   }

   @Nullable
   public final String component2() {
      return this.testFramework;
   }

   @Nullable
   public final String component3() {
      return this.assertionFramework;
   }

   @Nullable
   public final String component4() {
      return this.mockingFramework;
   }

   @NotNull
   public final Toolchain copy(@Nullable String buildSystem, @Nullable String testFramework, @Nullable String assertionFramework, @Nullable String mockingFramework) {
      return new Toolchain(buildSystem, testFramework, assertionFramework, mockingFramework);
   }

   @NotNull
   public String toString() {
      return "Toolchain(buildSystem=" + this.buildSystem + ", testFramework=" + this.testFramework + ", assertionFramework=" + this.assertionFramework + ", mockingFramework=" + this.mockingFramework + ")";
   }

   public int hashCode() {
      int var1 = this.buildSystem == null ? 0 : this.buildSystem.hashCode();
      var1 = var1 * 31 + (this.testFramework == null ? 0 : this.testFramework.hashCode());
      var1 = var1 * 31 + (this.assertionFramework == null ? 0 : this.assertionFramework.hashCode());
      var1 = var1 * 31 + (this.mockingFramework == null ? 0 : this.mockingFramework.hashCode());
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof Toolchain)) {
         return false;
      } else {
         Toolchain var2 = (Toolchain)other;
         if (!Intrinsics.areEqual(this.buildSystem, var2.buildSystem)) {
            return false;
         } else if (!Intrinsics.areEqual(this.testFramework, var2.testFramework)) {
            return false;
         } else if (!Intrinsics.areEqual(this.assertionFramework, var2.assertionFramework)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.mockingFramework, var2.mockingFramework);
         }
      }
   }

   public Toolchain() {
      this((String)null, (String)null, (String)null, (String)null, 15, (DefaultConstructorMarker)null);
   }
}
