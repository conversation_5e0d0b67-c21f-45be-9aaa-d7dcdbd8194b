package ai.zencoder.generated.client.transport.infrastructure;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import kotlin.Metadata;
import kotlin.collections.ArraysKt;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import org.jetbrains.annotations.NotNull;

@Metadata(
   mv = {2, 1, 0},
   k = 2,
   xi = 48,
   d1 = {"\u00000\n\u0000\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0011\n\u0002\b\u0002\n\u0002\u0010\u001c\n\u0000\u001a\u000e\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0002\u001aX\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00020\u0003\"\n\b\u0000\u0010\u000f*\u0004\u0018\u00010\b2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u0002H\u000f0\u00112\u0006\u0010\u0005\u001a\u00020\u00022#\b\u0002\u0010\u0012\u001a\u001d\u0012\u0013\u0012\u0011H\u000f\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000b\u0012\u0004\u0012\u00020\u00020\u0007\u00a2\u0006\u0002\u0010\u0013\u001aS\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00020\u0003\"\n\b\u0000\u0010\u000f*\u0004\u0018\u00010\b2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u0002H\u000f0\u00142\u0006\u0010\u0005\u001a\u00020\u00022#\b\u0002\u0010\u0012\u001a\u001d\u0012\u0013\u0012\u0011H\u000f\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000b\u0012\u0004\u0012\u00020\u00020\u0007\".\u0010\u0006\u001a\u001f\u0012\u0015\u0012\u0013\u0018\u00010\b\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000b\u0012\u0004\u0012\u00020\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r*.\u0010\u0000\"\u0014\u0012\u0004\u0012\u00020\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00020\u00030\u00012\u0014\u0012\u0004\u0012\u00020\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00020\u00030\u0001\u00a8\u0006\u0015"},
   d2 = {"MultiValueMap", "", "", "", "collectionDelimiter", "collectionFormat", "defaultMultiValueConverter", "Lkotlin/Function1;", "", "Lkotlin/ParameterName;", "name", "item", "getDefaultMultiValueConverter", "()Lkotlin/jvm/functions/Function1;", "toMultiValue", "T", "items", "", "map", "([Ljava/lang/Object;Ljava/lang/String;Lkotlin/jvm/functions/Function1;)Ljava/util/List;", "", "zencoder"}
)
@SourceDebugExtension({"SMAP\nApiAbstractions.kt\nKotlin\n*S Kotlin\n*F\n+ 1 ApiAbstractions.kt\nai/zencoder/generated/client/transport/infrastructure/ApiAbstractionsKt\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,24:1\n1549#2:25\n1620#2,3:26\n*S KotlinDebug\n*F\n+ 1 ApiAbstractions.kt\nai/zencoder/generated/client/transport/infrastructure/ApiAbstractionsKt\n*L\n20#1:25\n20#1:26,3\n*E\n"})
public final class ApiAbstractionsKt {
   @NotNull
   private static final Function1<Object, String> defaultMultiValueConverter = ApiAbstractionsKt::a;

   @NotNull
   public static final String a(@NotNull String var0) {
      Intrinsics.checkNotNullParameter(var0, "");
      String var10000;
      switch (var0) {
         case "csv":
            var10000 = ",";
            return var10000;
         case "tsv":
            var10000 = "\t";
            return var10000;
         case "pipe":
            var10000 = "|";
            return var10000;
         case "space":
            var10000 = " ";
            return var10000;
      }

      var10000 = "";
      return var10000;
   }

   @NotNull
   public static final Function1<Object, String> a() {
      return defaultMultiValueConverter;
   }

   @NotNull
   public static final <T> List<String> a(@NotNull T[] var0, @NotNull String var1, @NotNull Function1<? super T, String> var2) {
      Intrinsics.checkNotNullParameter(var0, "");
      Intrinsics.checkNotNullParameter(var1, "");
      Intrinsics.checkNotNullParameter(var2, "");
      return a(ArraysKt.asIterable(var0), var1, var2);
   }

   @NotNull
   public static final <T> List<String> a(@NotNull Iterable<? extends T> var0, @NotNull String var1, @NotNull Function1<? super T, String> var2) {
      Intrinsics.checkNotNullParameter(var0, "");
      Intrinsics.checkNotNullParameter(var1, "");
      Intrinsics.checkNotNullParameter(var2, "");
      List var10000;
      if (Intrinsics.areEqual(var1, "multi")) {
         boolean var4 = false;
         Collection var6 = (Collection)(new ArrayList(CollectionsKt.collectionSizeOrDefault(var0, 10)));
         boolean var7 = false;

         for(Object var9 : var0) {
            var6.add(var2.invoke(var9));
         }

         var10000 = (List)var6;
      } else {
         var10000 = CollectionsKt.listOf(CollectionsKt.joinToString$default(var0, (CharSequence)a(var1), (CharSequence)null, (CharSequence)null, 0, (CharSequence)null, var2, 30, (Object)null));
      }

      return var10000;
   }

   private static final String a(Object var0) {
      return String.valueOf(var0);
   }
}
