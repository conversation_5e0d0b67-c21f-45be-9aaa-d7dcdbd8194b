package ai.zencoder.generated.client.transport.infrastructure;

import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010 \n\u0002\b\n\u0018\u0000*\u0004\b\u0000\u0010\u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002B5\u0012\u0006\u0010\u0003\u001a\u00028\u0000\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\u001a\b\u0002\u0010\u0006\u001a\u0014\u0012\u0004\u0012\u00020\b\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\t0\u0007\u00a2\u0006\u0004\b\n\u0010\u000bR\u0013\u0010\u0003\u001a\u00028\u0000\u00a2\u0006\n\n\u0002\u0010\u000e\u001a\u0004\b\f\u0010\rR\u0014\u0010\u0004\u001a\u00020\u0005X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R&\u0010\u0006\u001a\u0014\u0012\u0004\u0012\u00020\b\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\t0\u0007X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006\u0013"},
   d2 = {"Lai/zencoder/generated/client/transport/infrastructure/Success;", "T", "Lai/zencoder/generated/client/transport/infrastructure/ApiResponse;", "data", "statusCode", "", "headers", "", "", "", "<init>", "(Ljava/lang/Object;ILjava/util/Map;)V", "getData", "()Ljava/lang/Object;", "Ljava/lang/Object;", "getStatusCode", "()I", "getHeaders", "()Ljava/util/Map;", "zencoder"}
)
public final class Success<T> extends ApiResponse<T> {
   private final T data;
   private final int statusCode;
   @NotNull
   private final Map<String, List<String>> headers;

   public Success(T data, int statusCode, @NotNull Map<String, ? extends List<String>> headers) {
      Intrinsics.checkNotNullParameter(headers, "");
      super(ResponseType.Success);
      this.data = (T)data;
      this.statusCode = statusCode;
      this.headers = headers;
   }

   public final T a() {
      return this.data;
   }

   public int a() {
      return this.statusCode;
   }

   @NotNull
   public Map<String, List<String>> a() {
      return this.headers;
   }
}
