package ai.zencoder.generated.client.transport.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J'\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0002\u001a\u00020\u00038G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0013\u0010\u0004\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0006\u001a\u00020\u00058G\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\f\u00a8\u0006\u0018"},
   d2 = {"Lai/zencoder/generated/client/transport/models/TransportBusRepoIndexingEvent;", "", "status", "Lai/zencoder/generated/client/transport/models/TransportBusRepoIndexingStatus;", "requestId", "", "repoId", "<init>", "(Lai/zencoder/generated/client/transport/models/TransportBusRepoIndexingStatus;Ljava/lang/String;Ljava/lang/String;)V", "getStatus", "()Lai/zencoder/generated/client/transport/models/TransportBusRepoIndexingStatus;", "getRequestId", "()Ljava/lang/String;", "getRepoId", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "zencoder"}
)
public final class TransportBusRepoIndexingEvent {
   @NotNull
   private final TransportBusRepoIndexingStatus status;
   @NotNull
   private final String requestId;
   @NotNull
   private final String repoId;

   public TransportBusRepoIndexingEvent(@NotNull TransportBusRepoIndexingStatus status, @NotNull String requestId, @NotNull String repoId) {
      Intrinsics.checkNotNullParameter(status, "");
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(repoId, "");
      super();
      this.status = status;
      this.requestId = requestId;
      this.repoId = repoId;
   }

   @JsonProperty("status")
   @NotNull
   public final TransportBusRepoIndexingStatus getStatus() {
      return this.status;
   }

   @JsonProperty("request_id")
   @NotNull
   public final String getRequestId() {
      return this.requestId;
   }

   @JsonProperty("repo_id")
   @NotNull
   public final String getRepoId() {
      return this.repoId;
   }

   @NotNull
   public final TransportBusRepoIndexingStatus component1() {
      return this.status;
   }

   @NotNull
   public final String component2() {
      return this.requestId;
   }

   @NotNull
   public final String component3() {
      return this.repoId;
   }

   @NotNull
   public final TransportBusRepoIndexingEvent copy(@NotNull TransportBusRepoIndexingStatus status, @NotNull String requestId, @NotNull String repoId) {
      Intrinsics.checkNotNullParameter(status, "");
      Intrinsics.checkNotNullParameter(requestId, "");
      Intrinsics.checkNotNullParameter(repoId, "");
      return new TransportBusRepoIndexingEvent(status, requestId, repoId);
   }

   @NotNull
   public String toString() {
      return "TransportBusRepoIndexingEvent(status=" + this.status + ", requestId=" + this.requestId + ", repoId=" + this.repoId + ")";
   }

   public int hashCode() {
      int var1 = this.status.hashCode();
      var1 = var1 * 31 + this.requestId.hashCode();
      var1 = var1 * 31 + this.repoId.hashCode();
      return var1;
   }

   public boolean equals(@Nullable Object other) {
      if (this == other) {
         return true;
      } else if (!(other instanceof TransportBusRepoIndexingEvent)) {
         return false;
      } else {
         TransportBusRepoIndexingEvent var2 = (TransportBusRepoIndexingEvent)other;
         if (this.status != var2.status) {
            return false;
         } else if (!Intrinsics.areEqual(this.requestId, var2.requestId)) {
            return false;
         } else {
            return Intrinsics.areEqual(this.repoId, var2.repoId);
         }
      }
   }
}
