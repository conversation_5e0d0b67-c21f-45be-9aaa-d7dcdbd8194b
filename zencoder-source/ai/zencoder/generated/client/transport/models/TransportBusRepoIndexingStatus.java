package ai.zencoder.generated.client.transport.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Locale;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Metadata(
   mv = {2, 1, 0},
   k = 1,
   xi = 48,
   d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\b\u0086\u0081\u0002\u0018\u0000 \r2\b\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\rB\u0011\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\b\u0010\f\u001a\u00020\u0003H\u0016R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\u000e"},
   d2 = {"Lai/zencoder/generated/client/transport/models/TransportBusRepoIndexingStatus;", "", "value", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "getValue", "()Ljava/lang/String;", "queued", "started", "completed", "error", "toString", "Companion", "zencoder"}
)
public enum TransportBusRepoIndexingStatus {
   @NotNull
   public static final Companion Companion = new Companion((DefaultConstructorMarker)null);
   @NotNull
   private final String value;
   @JsonProperty("queued")
   queued("queued"),
   @JsonProperty("started")
   started("started"),
   @JsonProperty("completed")
   completed("completed"),
   @JsonProperty("error")
   error("error");

   private TransportBusRepoIndexingStatus(String value) {
      this.value = value;
   }

   @NotNull
   public final String getValue() {
      return this.value;
   }

   @NotNull
   public String toString() {
      return this.value;
   }

   @NotNull
   public static EnumEntries<TransportBusRepoIndexingStatus> getEntries() {
      return $ENTRIES;
   }

   @Metadata(
      mv = {2, 1, 0},
      k = 1,
      xi = 48,
      d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0012\u0010\u0004\u001a\u0004\u0018\u00010\u00052\b\u0010\u0006\u001a\u0004\u0018\u00010\u0001J\u0012\u0010\u0007\u001a\u0004\u0018\u00010\b2\b\u0010\u0006\u001a\u0004\u0018\u00010\u0001\u00a8\u0006\t"},
      d2 = {"Lai/zencoder/generated/client/transport/models/TransportBusRepoIndexingStatus$Companion;", "", "<init>", "()V", "encode", "", "data", "decode", "Lai/zencoder/generated/client/transport/models/TransportBusRepoIndexingStatus;", "zencoder"}
   )
   @SourceDebugExtension({"SMAP\nTransportBusRepoIndexingStatus.kt\nKotlin\n*S Kotlin\n*F\n+ 1 TransportBusRepoIndexingStatus.kt\nai/zencoder/generated/client/transport/models/TransportBusRepoIndexingStatus$Companion\n+ 2 _Arrays.kt\nkotlin/collections/ArraysKt___ArraysKt\n*L\n1#1,68:1\n1282#2,2:69\n*S KotlinDebug\n*F\n+ 1 TransportBusRepoIndexingStatus.kt\nai/zencoder/generated/client/transport/models/TransportBusRepoIndexingStatus$Companion\n*L\n61#1:69,2\n*E\n"})
   public static final class Companion {
      private Companion() {
      }

      @Nullable
      public final String a(@Nullable Object var1) {
         return var1 instanceof TransportBusRepoIndexingStatus ? String.valueOf(var1) : null;
      }

      @Nullable
      public final TransportBusRepoIndexingStatus a(@Nullable Object var1) {
         TransportBusRepoIndexingStatus var13;
         if (var1 != null) {
            Object var2 = var1;
            boolean var3 = false;
            String var10000 = String.valueOf(var1).toLowerCase(Locale.ROOT);
            Intrinsics.checkNotNullExpressionValue(var10000, "");
            String var4 = var10000;
            TransportBusRepoIndexingStatus[] var5 = TransportBusRepoIndexingStatus.values();
            boolean var6 = false;
            int var7 = 0;
            int var8 = var5.length;

            while(true) {
               if (var7 >= var8) {
                  var13 = null;
                  break;
               }

               TransportBusRepoIndexingStatus var9;
               label26: {
                  var9 = var5[var7];
                  boolean var11 = false;
                  if (var2 != var9) {
                     String var10001 = String.valueOf(var9).toLowerCase(Locale.ROOT);
                     Intrinsics.checkNotNullExpressionValue(var10001, "");
                     if (!Intrinsics.areEqual(var4, var10001)) {
                        var12 = false;
                        break label26;
                     }
                  }

                  var12 = true;
               }

               if (var12) {
                  var13 = var9;
                  break;
               }

               ++var7;
            }
         } else {
            var13 = null;
         }

         return var13;
      }
   }
}
