scalar JSONObject

type SourceFile {
  path: String!
  fsPath: String!
  content: String!
  language: String
}

type SourceFileFs {
  fsPath: String!
  content: String!
  encoding: String!
  language: String
  lineEndings: String
  contentType: String!
}

type SourceFileFsV3 {
  fsPath: String!
  content: String!
  encoding: String!
  language: String
  lineEndings: String
  contentType: String!
  offsetLines: Int
  limitLines: Int
  totalLines: Int!
  offsetCharacters: Int
  limitCharacters: Int
  totalCharacters: Int!
}

type AstNode {
  path: String!
  name: String!
  kind: AstNodeKind!
  offset: Int!
  body: String
  children: [AstNode!]!
}

enum AstNodeKind {
  Class
  Function
  Method
  Property
  Unknown
}

type ChatMessage {
  messageId: String
  content: String!
  role: String!
  attachments: [SourceFile!]
  codebaseEnabled: Boolean
}

type Context {
  projectInfo: ProjectInfo
  chatHistory: [ChatMessage!]!
  currentFile: SourceFile
  repoId: String
  chatId: String
  tabs: [Tab!]!
}

type Tab {
  index: Int!
  focused: Boolean!
  content: SourceFile!
}

type FileMetadata {
  path: String!
  isDirectory: Boolean!
}

type FileMetadataFs {
  fsPath: String!
  isDirectory: Boolean!
}

type FileMetadataFsV3 {
  fsPath: String!
  isDirectory: Boolean!
  encoding: String
  contentType: String
  totalCharacters: Int
  totalLines: Int
}

type ProjectInfo {
  rootDir: String
  shellName: String
  ideName: String
  ideVersion: String
  osName: String
  osVersion: String
}

input InputFilePath {
  path: String!
}

input MessageContext {
  usedFilePaths: [InputFilePath!]
}

input AstNodeFilter {
  path: String!
  offset: Int
}

input DiagnosticsFile {
  path: String!
  content: String!
  language: String
  range: DiagnosticsRange
}

input DiagnosticsRequest {
  files: [DiagnosticsFile!]!
}

input DiagnosticsFileFs {
  fsPath: String!
  content: String!
  language: String
  range: DiagnosticsRange
}

input DiagnosticsRequestFs {
  files: [DiagnosticsFileFs!]!
}

input DiagnosticsRange {
  startLine: Int!
  endLine: Int!
}

type DiagnosticsResult {
  path: String!
  severity: String!
  source: String
  message: String!
  language: String
  range: DiagnosticsResultRange!
}

type DiagnosticsResultFs {
  fsPath: String!
  severity: String!
  source: String
  message: String!
  language: String
  range: DiagnosticsResultRange!
}

type DiagnosticsResultRange {
  startLine: Int!
  startCharacter: Int!
  endLine: Int!
  endCharacter: Int!
}

enum ReadFileRequestViewPageType {
    line
    character
}

input ReadFileRequestViewPage {
    type: ReadFileRequestViewPageType!
    offset: Int!
    limit: Int!
}

input ReadFileRequest {
    fsPath: String!
    page: ReadFileRequestViewPage
}

input FileContent {
  path: String!
  content: String!
}

input FileContentFs {
  fsPath: String!
  content: String!
  encoding: String!
  contentType: String!
}

type FileOperationResult {
  path: String!
  success: Boolean!
  errorMessage: String
}

enum WriteFilesErrorCode {
  NotAllowedByUser
  FsPermissionDenied
}

type WriteFilesError {
  code: WriteFilesErrorCode!
  errorMessage: String
}

type WriteOperationResultFs {
  fsPath: String!
  success: Boolean!
  error: WriteFilesError
}

enum ListFilesFsErrorCode {
  NotFound
  NotDirectory
  NotAllowedByUser
  FsPermissionDenied
}

type ListFilesFsError {
  code: ListFilesFsErrorCode!
  errorMessage: String
}

type ListFilesFsResult {
  success: Boolean!
  entries: [FileMetadataFs!]
  error: ListFilesFsError
}

enum ReadFileFsErrorCode {
  NotFound
  IsDirectory
  NotAllowedByUser
  FsPermissionDenied
}

type ReadFileFsError {
  code: ReadFileFsErrorCode!
  errorMessage: String
}

type ReadFileFsResult {
  success: Boolean!
  file: SourceFileFs
  error: ReadFileFsError
}

type ReadFilesFsV3Result {
  success: Boolean!
  file: SourceFileFsV3
  error: ReadFileFsError
}

enum FsObjectMetadataErrorCode {
  NotFound
  FsPermissionDenied
  NotAllowedByUser
}

type FsObjectMetadataError {
  code: FsObjectMetadataErrorCode!
  errorMessage: String
}

type FsObjectMetadataResult {
  success: Boolean!
  metadata: FileMetadataFs
  error: FsObjectMetadataError
}

type FsV3ObjectMetadataResult {
  success: Boolean!
  metadata: FileMetadataFsV3
  error: FsObjectMetadataError
}

type Query {
  context(operationId: String!): Context
  findFiles(pattern: String!): [SourceFile!]!
  files(paths: [String!]!): [SourceFile!]!
  astNodes(astNodeFilters: [AstNodeFilter!]!): [AstNode!]!
  diagnostics(diagnosticsRequest: DiagnosticsRequest!): [DiagnosticsResult!]!
  diagnosticsFs(diagnosticsRequest: DiagnosticsRequestFs!): [DiagnosticsResultFs!]!
  fileMetadata(path: String!): FileMetadata
  listFiles(directoryPath: String!, depth: Int!): [FileMetadata!]
  listTools: [String!]!
  mcp(request: String!): String!
  listFilesFs(fsDirectoryPath: String!, depth: Int!): ListFilesFsResult!
  fsObjectMetadata(fsPath: String!): FsObjectMetadataResult! # skip ignored files
  fsV3ObjectMetadata(fsPath: String!): FsV3ObjectMetadataResult! # skip ignored files
  readFilesFs(fsPaths: [String!]!): [ReadFileFsResult!]!
  readFilesFsV3(files: [ReadFileRequest!]!): [ReadFilesFsV3Result!]!
}

type Mutation {
  createMessage(operationId: String!, message: String!, messageContext: MessageContext): String!
  updateMessage(operationId: String!, messageId: String!, message: String!): String!
  updateMessageContext(
    operationId: String!
    messageId: String!
    messageContext: MessageContext!
  ): String!
  renderComponent(operationId: String!, componentName: String!, props: JSONObject!): String!
  updateComponent(
    operationId: String!
    messageId: String!
    componentName: String!
    props: JSONObject!
  ): String!
  createOrUpdateFiles(files: [FileContent!]!): [FileOperationResult!]!
  createOrUpdateFilesV2(operationId: String!, files: [FileContent!]!): [FileOperationResult!]!
  createOrUpdateFilesV3(operationId: String!, files: [FileContentFs!]!): [WriteOperationResultFs!]! #mkdir -p is default behavior, limits by project root
  endOperation(operationId: String!, errorMessage: String): Boolean!
}
