{"openapi": "3.1.0", "info": {"title": "chat", "version": "0.0.0"}, "servers": [{"url": "/chat"}], "paths": {"/inference": {"post": {"summary": "Inference", "operationId": "chat_inference_inference_post", "tags": ["chat"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/chat_InferenceRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/inference/v1": {"post": {"summary": "Inference V1", "operationId": "chat_inference_v1_inference_v1_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/chat_InferenceRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/inference_simple": {"post": {"summary": "Inference Simple", "operationId": "chat_inference_simple_inference_simple_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/chat_InferenceRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/inference_simple/v1": {"post": {"summary": "Inference Simple V1", "operationId": "chat_inference_simple_v1_inference_simple_v1_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/chat_InferenceRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/x-ndjson": {"schema": {"$ref": "#/components/schemas/chat_GeneratedAnswerChunk", "type": "string"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/inference_easy_apply": {"post": {"summary": "Inference Easy Apply", "operationId": "chat_inference_easy_apply_inference_easy_apply_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/chat_InferenceRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/easy_apply": {"post": {"tags": ["chat"], "summary": "Easy Apply", "operationId": "chat_easy_apply_easy_apply_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/chat_EasyApplyRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/chat_EasyApplyResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/rag_request": {"post": {"summary": "Rag <PERSON>", "tags": ["chat"], "operationId": "chat_rag_request_rag_request_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RagRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RagResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/chat_naming": {"post": {"summary": "<PERSON><PERSON>", "tags": ["chat"], "operationId": "chat_chat_naming_chat_naming_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatNamingRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatNamingResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/memory_extract": {"post": {"summary": "Memory Extract", "operationId": "chat_memory_extract_memory_extract_post", "tags": ["chat"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/chat_MemoryExtractRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/chat_MemoryExtractResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"securitySchemes": {"OAuth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://example.com/oauth/authorize", "tokenUrl": "https://example.com/oauth/token", "scopes": {"read": "Grants read access", "write": "Grants write access"}}}}}, "schemas": {"ChatContext": {"properties": {"currentFile": {"anyOf": [{"$ref": "#/components/schemas/ContextFile"}, {"type": "null"}]}, "openedTabs": {"anyOf": [{"items": {"$ref": "#/components/schemas/ContextFile"}, "type": "array"}, {"type": "null"}], "title": "Openedtabs", "default": []}}, "type": "object", "title": "ChatContext"}, "chat_ChatHistoryItem": {"properties": {"role": {"type": "string", "enum": ["user", "assistant"], "title": "Role"}, "content": {"type": "string", "title": "Content"}, "contentSections": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["text", "codeSnippet", "command"], "title": "Content Type"}, "text": {"type": "string", "title": "Text", "description": "Required for 'text' and 'codeSnippet'."}, "language": {"type": "string", "title": "Language", "description": "Required if type is 'codeSnippet'."}, "commandName": {"type": "string", "title": "Command Name", "description": "Required if type is 'command'."}}, "required": ["type"], "title": "ContentSection"}, "title": "ContentSections"}, "context": {"anyOf": [{"$ref": "#/components/schemas/chat_HistoryItemContext"}, {"type": "null"}]}}, "type": "object", "required": ["role", "content"], "title": "ChatHistoryItem"}, "ChatNamingRequest": {"properties": {"requestId": {"type": "string", "title": "Requestid"}, "language": {"type": "string", "title": "Language", "default": ""}, "context": {"$ref": "#/components/schemas/ChatContext"}, "userInstruction": {"type": "string", "title": "Userinstruction", "default": "", "deprecated": true}, "userInstructions": {"items": {"type": "string"}, "type": "array", "title": "Userinstructions", "default": []}, "memoryItems": {"items": {"type": "string"}, "type": "array", "title": "Memoryitems", "default": []}, "chatHistory": {"items": {"$ref": "#/components/schemas/chat_ChatHistoryItem"}, "type": "array", "minItems": 1, "title": "Chathistory"}}, "type": "object", "required": ["requestId", "context", "chatHistory"], "title": "ChatNamingRequest"}, "ChatNamingResponse": {"properties": {"name": {"type": "string", "title": "Name"}}, "type": "object", "required": ["name"], "title": "ChatNamingResponse"}, "ContextFile": {"properties": {"path": {"type": "string", "format": "path", "title": "Path"}, "sources": {"type": "array", "items": {"type": "string"}, "default": [], "description": "List of sources where this item was found."}, "content": {"type": "string", "title": "Content"}}, "type": "object", "required": ["path", "content"], "title": "ContextFile"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "chat_EasyApplyClassificationChunk": {"properties": {"type": {"type": "string", "enum": ["easy_apply_classification"], "const": "easy_apply_classification", "title": "Type", "default": "easy_apply_classification"}, "content": {"type": "boolean", "title": "Content"}}, "type": "object", "required": ["content"], "title": "EasyApplyClassificationChunk"}, "chat_EasyApplyRequest": {"properties": {"requestId": {"type": "string", "title": "Requestid"}, "language": {"type": "string", "title": "Language"}, "context": {"$ref": "#/components/schemas/ChatContext"}, "userInstruction": {"type": "string", "title": "Userinstruction", "default": "", "deprecated": true}, "userInstructions": {"items": {"type": "string"}, "type": "array", "title": "Userinstructions", "default": []}, "memoryItems": {"items": {"type": "string"}, "type": "array", "title": "Memoryitems", "default": []}, "chatHistory": {"items": {"$ref": "#/components/schemas/chat_ChatHistoryItem"}, "type": "array", "title": "Chathistory"}, "regenerate": {"type": "boolean", "title": "Regenerate", "default": false}, "repoId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Repoid"}}, "type": "object", "required": ["requestId", "language", "context", "chatHistory"], "title": "EasyApplyRequest"}, "chat_EasyApplyResponse": {"properties": {"status": {"$ref": "#/components/schemas/chat_EasyApplyStatus"}, "patches": {"items": {"$ref": "#/components/schemas/chat_OutputPatch"}, "type": "array", "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["status", "patches"], "title": "EasyApplyResponse"}, "chat_EasyApplyStatus": {"type": "string", "enum": ["ok_new_files_present", "ok", "no_change", "errors_during_patch_gen", "errors_and_new_files"], "title": "EasyApplyStatus"}, "chat_GeneratedAnswerChunk": {"properties": {"type": {"type": "string", "enum": ["generated_answer"], "const": "generated_answer", "title": "Type", "default": "generated_answer"}, "content": {"type": "string", "title": "Content"}}, "type": "object", "required": ["content"], "title": "GeneratedAnswerChunk"}, "chat_GeneratedAnswerFinishedChunk": {"properties": {"type": {"type": "string", "enum": ["generated_answer_finished"], "const": "generated_answer_finished", "title": "Type", "default": "generated_answer_finished"}}, "type": "object", "required": ["content"], "title": "GeneratedAnswerChunk"}, "chat_HistoryItemContext": {"properties": {"attachedFiles": {"items": {"$ref": "#/components/schemas/ContextFile"}, "type": "array", "title": "Attachedfiles", "default": []}, "ragFiles": {"items": {"$ref": "#/components/schemas/ContextFile"}, "type": "array", "title": "<PERSON><PERSON><PERSON><PERSON>", "default": []}, "rerankedFiles": {"items": {"$ref": "#/components/schemas/ContextFile"}, "type": "array", "title": "Rerankedfiles", "default": []}}, "type": "object", "title": "HistoryItemContext"}, "chat_InferenceRequest": {"properties": {"requestId": {"type": "string", "title": "Requestid"}, "language": {"type": "string", "title": "Language"}, "context": {"$ref": "#/components/schemas/ChatContext"}, "userInstruction": {"type": "string", "title": "Userinstruction", "default": "", "deprecated": true}, "userInstructions": {"items": {"type": "string"}, "type": "array", "title": "Userinstructions", "default": []}, "memoryItems": {"items": {"type": "string"}, "type": "array", "title": "Memoryitems", "default": []}, "chatHistory": {"items": {"$ref": "#/components/schemas/chat_ChatHistoryItem"}, "type": "array", "title": "Chathistory"}, "regenerate": {"type": "boolean", "title": "Regenerate", "default": false}, "repoId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Repoid"}}, "type": "object", "required": ["requestId", "language", "context", "chatHistory"], "title": "InferenceRequest"}, "chat_MemoryExtractRequest": {"properties": {"requestId": {"type": "string", "title": "Requestid"}, "language": {"type": "string", "title": "Language"}, "context": {"$ref": "#/components/schemas/ChatContext"}, "userInstruction": {"type": "string", "title": "Userinstruction", "default": "", "deprecated": true}, "userInstructions": {"items": {"type": "string"}, "type": "array", "title": "Userinstructions", "default": []}, "memoryItems": {"items": {"type": "string"}, "type": "array", "title": "Memoryitems", "default": []}, "chatHistory": {"items": {"$ref": "#/components/schemas/chat_ChatHistoryItem"}, "type": "array", "title": "Chathistory"}, "regenerate": {"type": "boolean", "title": "Regenerate", "default": false}}, "type": "object", "required": ["requestId", "language", "context", "chatHistory"], "title": "MemoryExtractRequest"}, "chat_MemoryExtractResponse": {"properties": {"memoryItems": {"items": {"type": "string"}, "type": "array", "title": "Memoryitems"}}, "type": "object", "required": ["memoryItems"], "title": "MemoryExtractResponse"}, "chat_OutputPatch": {"properties": {"project": {"type": "string", "title": "Project", "default": ""}, "path": {"type": "string", "title": "Path"}, "patchText": {"type": "string", "title": "Patchtext"}}, "type": "object", "required": ["path", "patchText"], "title": "OutputPatch"}, "RagRequest": {"properties": {"requestId": {"type": "string", "title": "Requestid"}, "language": {"type": "string", "title": "Language"}, "context": {"$ref": "#/components/schemas/ChatContext"}, "userInstruction": {"type": "string", "title": "Userinstruction", "default": "", "deprecated": true}, "userInstructions": {"items": {"type": "string"}, "type": "array", "title": "Userinstructions", "default": []}, "memoryItems": {"items": {"type": "string"}, "type": "array", "title": "Memoryitems", "default": []}, "chatHistory": {"items": {"$ref": "#/components/schemas/chat_ChatHistoryItem"}, "type": "array", "title": "Chathistory"}, "regenerate": {"type": "boolean", "title": "Regenerate", "default": false}, "repoId": {"type": "string", "title": "Repoid"}}, "type": "object", "required": ["requestId", "language", "context", "chatHistory", "repoId"], "title": "RagRequest"}, "RagResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/RagResponseItem"}}, "actions": {"type": "array", "description": "A list of actions that can be performed on repository items.", "items": {"$ref": "#/components/schemas/RagAction"}}}}, "RagAction": {"type": "object", "title": "RagAction", "anyOf": [{"$ref": "#/components/schemas/RagKeywordsActionDef"}, {"$ref": "#/components/schemas/RagDummyActionDef"}], "description": "An action that can be performed on a repository."}, "RagKeywordsActionDef": {"type": "object", "title": "RagKeywordsActionDef", "properties": {"keywordSearch": {"$ref": "#/components/schemas/RagKeywordsAction"}}, "description": "Filter criteria for searching files."}, "RagDummyActionDef": {"type": "object", "title": "RagDummyActionDef", "properties": {"dummy": {"$ref": "#/components/schemas/RagDummyAction"}}, "description": "Filter criteria for searching files."}, "RagKeywordsAction": {"title": "RagKeywordsAction", "type": "object", "properties": {"keywords": {"type": "array", "description": "KKeywords to search for in the content of files.", "items": {"type": "string"}}, "limit": {"type": "integer", "description": "Maximum number of results to return.", "default": 20}}, "required": ["keywords", "limit"], "description": "Filter criteria for searching files."}, "RagDummyAction": {"title": "RagDummyAction", "type": "object", "properties": {"dummyParam": {"type": "array", "description": "Keywords to search for in the content of files.", "items": {"type": "string"}}}, "required": ["dummy<PERSON><PERSON><PERSON>"], "description": "Filter criteria for searching files."}, "RagResponseItem": {"type": "object", "properties": {"filepath": {"type": "string", "description": "The file path"}, "sources": {"type": "array", "items": {"type": "string"}, "default": [], "description": "List of sources where this item was found."}}, "required": ["filepath"]}, "ValidationError": {"properties": {"loc": {"items": {"type": "string"}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "chat_RerankedFilesPathsChunk": {"properties": {"type": {"type": "string", "enum": ["reranked_files_paths"], "const": "reranked_files_paths", "title": "Type", "default": "reranked_files_paths"}, "content": {"items": {"type": "string", "format": "path"}, "type": "array", "title": "Content"}}, "type": "object", "required": ["content"], "title": "RerankedFilesPathsChunk"}}}}