{"openapi": "3.1.0", "info": {"title": "codegen", "version": "0.1.0"}, "servers": [{"url": "/codegen"}], "paths": {"/edit": {"post": {"summary": "Inference", "operationId": "inference_refactor_post", "tags": ["codegen"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeEditRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeEditResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"securitySchemes": {"OAuth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://example.com/oauth/authorize", "tokenUrl": "https://example.com/oauth/token", "scopes": {"read": "Grants read access", "write": "Grants write access"}}}}}, "schemas": {"CodeEditRequest": {"properties": {"requestId": {"type": "string", "title": "Requestid"}, "userQuery": {"type": "string", "title": "Userquery"}, "openedTabs": {"items": {"$ref": "#/components/schemas/TabItem"}, "type": "array", "title": "Openedtabs"}, "activeFilePath": {"$ref": "#/components/schemas/UserPath"}, "multiShot": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "Multishot"}, "selectedCodeSpan": {"type": "array", "maxItems": 2, "minItems": 2, "title": "Selectedcodespan", "items": {"type": "integer"}}}, "type": "object", "required": ["requestId", "userQuery", "openedTabs", "activeFilePath", "selectedCodeSpan"], "title": "CodeEditRequest"}, "CodeEditResponse": {"properties": {"spanOfInterest": {"type": "array", "maxItems": 2, "minItems": 2, "title": "Spanofinterest", "deprecated": true, "items": {"type": "integer"}}, "patch": {"type": "string", "title": "Patch"}}, "type": "object", "required": ["spanOfInterest", "patch"], "title": "CodeEditResponse"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "TabItem": {"properties": {"path": {"$ref": "#/components/schemas/UserPath"}, "language": {"type": "string", "title": "Language", "default": ""}, "content": {"type": "string", "title": "Content"}}, "type": "object", "required": ["path", "content"], "title": "TabItem"}, "UserPath": {"type": "string", "format": "path"}, "ValidationError": {"properties": {"loc": {"items": {"type": "string"}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}