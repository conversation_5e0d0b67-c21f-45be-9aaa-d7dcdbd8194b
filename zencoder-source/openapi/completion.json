{"openapi": "3.1.0", "info": {"title": "code-completion-service", "version": "0.0.7"}, "servers": [{"url": "/code-completion-service"}], "tags": [{"name": "CodeCompletion"}], "paths": {"/inference": {"post": {"tags": ["CodeCompletion"], "summary": "Inference", "operationId": "inference_inference_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeCompletionRequest"}}}, "required": true}, "parameters": [{"name": "sentry-trace", "in": "header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeCompletionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/fireworks": {"post": {"tags": ["CodeCompletion"], "summary": "Fireworks", "operationId": "fireworks_fireworks_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeCompletionRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeCompletionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "deprecated": true}}, "/predict-next-edit": {"post": {"tags": ["CodeCompletion"], "summary": "Next Edit Prediction", "operationId": "next_edit_prediction_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditPredictionRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditPredictionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"securitySchemes": {"OAuth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://example.com/oauth/authorize", "tokenUrl": "https://example.com/oauth/token", "scopes": {"read": "Grants read access", "write": "Grants write access"}}}}}, "headers": {"X-Custom-Global-Header": {"description": "A custom global header for all requests", "required": true, "schema": {"type": "string"}}}, "schemas": {"CodeCompletionContext": {"properties": {"active_file_path": {"type": "string", "title": "Active File Path"}, "project_name": {"type": "string", "title": "Project Name"}, "prefix": {"type": "string", "title": "Prefix"}, "suffix": {"type": "string", "title": "Suffix"}, "language": {"type": "string", "title": "Language"}, "imports_section": {"type": "string", "title": "Imports Section"}, "imports_signatures": {"items": {"$ref": "#/components/schemas/ExternalSignature"}, "type": "array", "title": "Imports Signatures"}, "used_signatures": {"items": {"$ref": "#/components/schemas/ExternalSignature"}, "type": "array", "title": "Used Signatures", "default": []}, "opened_tabs_signatures": {"items": {"$ref": "#/components/schemas/ExternalSignature"}, "type": "array", "title": "Opened Tabs Signatures", "default": []}}, "type": "object", "required": ["active_file_path", "project_name", "prefix", "suffix", "language", "imports_section", "imports_signatures"], "title": "CodeCompletionContext"}, "CodeCompletionRequest": {"properties": {"requestId": {"type": "string", "title": "Requestid"}, "language": {"type": "string", "title": "Language"}, "segments": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Segments"}, "multiLine": {"type": "boolean", "title": "Multiline", "default": false}, "context": {"anyOf": [{"$ref": "#/components/schemas/CodeCompletionContext"}, {"type": "null"}]}}, "type": "object", "required": ["requestId", "language", "segments"], "title": "CodeCompletionRequest"}, "CodeCompletionResponse": {"properties": {"completion": {"type": "string", "title": "Completion"}}, "type": "object", "required": ["completion"], "title": "CodeCompletionResponse"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ExternalSignature": {"properties": {"file_name": {"type": "string", "title": "File Name"}, "signature": {"type": "string", "title": "Signature"}}, "type": "object", "required": ["file_name", "signature"], "title": "ExternalSignature"}, "FileState": {"properties": {"timestamp": {"type": "integer", "format": "int64", "title": "Timestamp"}, "content": {"type": "string", "title": "Content"}}, "type": "object", "required": ["timestamp", "content"], "title": "FileState", "description": "Represents a file state with its content and cursor position."}, "CursorPosition": {"properties": {"row": {"type": "integer", "title": "Row"}, "column": {"type": "integer", "title": "Column"}}, "type": "object", "required": ["row", "column"], "title": "CursorPosition"}, "EditPredictionRequest": {"properties": {"requestId": {"type": "string", "title": "Requestid"}, "fileStates": {"items": {"$ref": "#/components/schemas/FileState"}, "type": "array", "title": "Filestates"}, "fileContent": {"type": "string", "title": "Filecontent"}, "cursorPosition": {"$ref": "#/components/schemas/CursorPosition"}, "language": {"type": "string", "title": "Language"}, "filePath": {"type": "string", "title": "Filepath"}}, "type": "object", "required": ["requestId", "fileStates", "fileContent", "formattedFileContent", "language", "filePath"], "title": "EditPredictionRequest"}, "EditPredictionResponse": {"properties": {"original_str": {"type": "string", "title": "Original Str"}, "modified_str": {"type": "string", "title": "Modified Str"}, "cursor_position": {"$ref": "#/components/schemas/CursorPosition"}}, "type": "object", "required": ["original_str", "modified_str", "cursor_position"], "title": "EditPredictionResponse", "description": "Response model for the next edit prediction feature."}, "ValidationError": {"properties": {"items": {"type": "string"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}