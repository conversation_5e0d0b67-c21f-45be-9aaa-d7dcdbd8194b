{"openapi": "3.1.0", "info": {"title": "feedback-service", "version": "0.0.25.post3.dev0+85c7c74"}, "servers": [{"url": "/feedback-service"}], "paths": {"/user_feedback": {"post": {"summary": "User <PERSON>", "operationId": "user_feedback_user_feedback_post", "tags": ["feedback"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFeedbackRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/user_feedback_deliberate": {"post": {"summary": "User <PERSON><PERSON><PERSON> Deliberate", "operationId": "user_feedback_deliberate_user_feedback_deliberate_post", "tags": ["feedback"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFeedbackRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"securitySchemes": {"OAuth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://example.com/oauth/authorize", "tokenUrl": "https://example.com/oauth/token", "scopes": {"read": "Grants read access", "write": "Grants write access"}}}}}, "schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Response": {"properties": {"status": {"type": "string", "title": "Status"}}, "type": "object", "required": ["status"], "title": "Response"}, "UserFeedback": {"properties": {"text": {"type": "string", "title": "Text"}, "accepted": {"type": "boolean", "title": "Accepted"}, "modification": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Modification", "default": ""}}, "type": "object", "required": ["text", "accepted"], "title": "UserFeedback"}, "UserFeedbackRequest": {"properties": {"request_id": {"type": "string", "title": "Request Id"}, "ide_name": {"type": "string", "enum": ["vscode", "jetbrains"], "title": "Ide Name"}, "feedback": {"additionalProperties": {"$ref": "#/components/schemas/UserFeedback"}, "type": "object", "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["request_id", "ide_name", "feedback"], "title": "UserFeedbackRequest"}, "ValidationError": {"properties": {"loc": {"items": {"type": "string"}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}