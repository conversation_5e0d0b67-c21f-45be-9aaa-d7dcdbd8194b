package {{packageName}}.infrastructure

{{#supportAndroidApiLevel25AndBelow}}
    import android.os.Build
{{/supportAndroidApiLevel25AndBelow}}
import io.sentry.okhttp.SentryOkHttpEventListener
import io.sentry.okhttp.SentryOkHttpInterceptor
import okhttp3.OkHttpClient
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.FormBody
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.ResponseBody
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.Request
import okhttp3.Headers
import okhttp3.Headers.Companion.toHeaders
import okhttp3.MultipartBody
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Response
import java.io.BufferedWriter
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.net.URLConnection
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Future
import ai.zencoder.plugin.utils.auth.ZenRefreshAuthenticator
import java.util.concurrent.TimeUnit
import ai.zencoder.plugin.api.exception.trackRateLimit
import ai.zencoder.plugin.api.exception.RateLimitException
import ai.zencoder.plugin.utils.systemSsl

{{^threetenbp}}
    import java.time.LocalDate
    import java.time.LocalDateTime
    import java.time.LocalTime
    import java.time.OffsetDateTime
    import java.time.OffsetTime
{{/threetenbp}}
import java.util.Locale
import java.util.regex.Pattern
{{#useCoroutines}}
    import kotlin.coroutines.resume
    import kotlin.coroutines.resumeWithException
    import kotlinx.coroutines.suspendCancellableCoroutine
{{/useCoroutines}}
{{#kotlinx_serialization}}
    import kotlinx.serialization.decodeFromString
    import kotlinx.serialization.encodeToString
{{/kotlinx_serialization}}
{{#threetenbp}}
    import org.threeten.bp.LocalDate
    import org.threeten.bp.LocalDateTime
    import org.threeten.bp.LocalTime
    import org.threeten.bp.OffsetDateTime
    import org.threeten.bp.OffsetTime
{{/threetenbp}}
{{#gson}}
    import com.google.gson.reflect.TypeToken
{{/gson}}
{{#jackson}}
    import com.fasterxml.jackson.core.type.TypeReference
{{/jackson}}
{{#moshi}}
    import com.squareup.moshi.adapter
{{/moshi}}

{{#nonPublicApi}}internal {{/nonPublicApi}}val EMPTY_REQUEST: RequestBody = ByteArray(0).toRequestBody()

{{#nonPublicApi}}internal {{/nonPublicApi}}open class ApiClient(val baseUrl: String, val client: Call.Factory = defaultClient) {
{{#nonPublicApi}}internal {{/nonPublicApi}}companion object {
/* Request headers */
const val CONTENT_TYPE = "Content-Type"
const val ACCEPT = "Accept"
const val AUTHORIZATION = "Authorization"
const val CONTENT_DISPOSITION = "Content-Disposition"

/* Media Types */
protected const val JSON_MEDIA_TYPE = "application/json"
protected const val FORM_DATA_MEDIA_TYPE = "multipart/form-data"
protected const val FORM_URL_ENC_MEDIA_TYPE = "application/x-www-form-urlencoded"
protected const val XML_MEDIA_TYPE = "application/xml"
protected const val OCTET_MEDIA_TYPE = "application/octet-stream"

/* Response headers */
const val RETRY_AFTER = "retry-after"
const val QUOTA_KEY = "quota-key"
const val REQUEST_DELAYED = "x-request-delayed"
const val LIMIT_REASON = "x-limit-reason"

val apiKey: MutableMap<String, String> = mutableMapOf()
val apiKeyPrefix: MutableMap<String, String> = mutableMapOf()
var username: String? = null
var password: String? = null
var accessToken: String? = null
const val BASE_URL_KEY = "{{packageName}}.baseUrl"

@JvmStatic
val defaultClient: OkHttpClient = OkHttpClient().newBuilder()
    .systemSsl()
    .eventListener(SentryOkHttpEventListener())
    .addInterceptor(SentryOkHttpInterceptor())
    .authenticator(ZenRefreshAuthenticator)
    .readTimeout(150L, TimeUnit.SECONDS)
    .build()

@JvmStatic
val builder: OkHttpClient.Builder = OkHttpClient.Builder()
    .systemSsl()
    .eventListener(SentryOkHttpEventListener())
    .addInterceptor(SentryOkHttpInterceptor())
}

/**
* Guess Content-Type header from the given file (defaults to "application/octet-stream").
*
* @param file The given file
* @return The guessed Content-Type
*/
protected fun guessContentTypeFromFile(file: File): String {
val contentType = URLConnection.guessContentTypeFromName(file.name)
return contentType ?: "application/octet-stream"
}

protected inline fun <reified T> requestBody(content: T, mediaType: String?): RequestBody =
    when {
    content is File -> content.asRequestBody((mediaType ?: guessContentTypeFromFile(content)).toMediaTypeOrNull())
    mediaType == FORM_DATA_MEDIA_TYPE ->
    MultipartBody.Builder()
    .setType(MultipartBody.FORM)
    .apply {
    // content's type *must* be Map<String, PartConfig<*>>
    @Suppress("UNCHECKED_CAST")
    (content as Map<String, PartConfig<*>>).forEach { (name, part) ->
    if (part.body is File) {
    val partHeaders = part.headers.toMutableMap() +
    (CONTENT_DISPOSITION to "form-data; name=\"$name\"; filename=\"${part.body.name}\"")
    val fileMediaType = guessContentTypeFromFile(part.body).toMediaTypeOrNull()
    addPart(
    partHeaders.toHeaders(),
    part.body.asRequestBody(fileMediaType)
    )
    } else {
    val partHeaders = part.headers.toMutableMap() +
    (CONTENT_DISPOSITION to "form-data; name=\"$name\"")
    addPart(
    partHeaders.toHeaders(),
    parameterToString(part.body).toRequestBody(null)
    )
    }
    }
    }.build()
    mediaType == FORM_URL_ENC_MEDIA_TYPE -> {
    FormBody.Builder().apply {
    // content's type *must* be Map<String, PartConfig<*>>
    @Suppress("UNCHECKED_CAST")
    (content as Map<String, PartConfig<*>>).forEach { (name, part) ->
    add(name, parameterToString(part.body))
    }
    }.build()
    }
    mediaType == null || mediaType.startsWith("application/") && mediaType.endsWith("json") ->
    if (content == null) {
    EMPTY_REQUEST
    } else {
    {{#moshi}}
        Serializer.moshi.adapter(T::class.java).toJson(content)
    {{/moshi}}
    {{#gson}}
        Serializer.gson.toJson(content, T::class.java)
    {{/gson}}
    {{#jackson}}
        Serializer.jacksonObjectMapper.writeValueAsString(content)
    {{/jackson}}
    {{#kotlinx_serialization}}
        Serializer.kotlinxSerializationJson.encodeToString(content)
    {{/kotlinx_serialization}}
    .toRequestBody((mediaType ?: JSON_MEDIA_TYPE).toMediaTypeOrNull())
    }
    mediaType == XML_MEDIA_TYPE -> throw UnsupportedOperationException("xml not currently supported.")
    mediaType == OCTET_MEDIA_TYPE && content is ByteArray ->
    content.toRequestBody(OCTET_MEDIA_TYPE.toMediaTypeOrNull())
    // TODO: this should be extended with other serializers
    else -> throw UnsupportedOperationException("requestBody currently only supports JSON body, byte body and File body.")
    }

    {{#moshi}}
        @OptIn(ExperimentalStdlibApi::class)
    {{/moshi}}
    protected inline fun <reified T: Any?> responseBody(response: Response, mediaType: String? = JSON_MEDIA_TYPE): T? {
        val body = response.body
        if(body == null) {
        return null
        }
        if (T::class.java == File::class.java) {
        // return tempFile
        val contentDisposition = response.header(CONTENT_DISPOSITION)

        val fileName = if (contentDisposition != null) {
        // Get filename from the Content-Disposition header.
        val pattern = Pattern.compile("filename=['\"]?([^'\"\\s]+)['\"]?")
        val matcher = pattern.matcher(contentDisposition)
        if (matcher.find()) {
        matcher.group(1)
        ?.replace(".*[/\\\\]", "")
        ?.replace(";", "")
        } else {
        null
        }
        } else {
        null
        }

        var prefix: String?
        val suffix: String?
        if (fileName == null) {
        prefix = "download"
        suffix = ""
        } else {
        val pos = fileName.lastIndexOf(".")
        if (pos == -1) {
        prefix = fileName
        suffix = null
        } else {
        prefix = fileName.substring(0, pos)
        suffix = fileName.substring(pos)
        }
        // Files.createTempFile requires the prefix to be at least three characters long
        if (prefix.length < 3) {
        prefix = "download"
        }
        }

        {{^supportAndroidApiLevel25AndBelow}}
            // Attention: if you are developing an android app that supports API Level 25 and bellow, please check flag supportAndroidApiLevel25AndBelow in https://openapi-generator.tech/docs/generators/kotlin#config-options
            val tempFile = java.nio.file.Files.createTempFile(prefix, suffix).toFile()
        {{/supportAndroidApiLevel25AndBelow}}
        {{#supportAndroidApiLevel25AndBelow}}
            val tempFile = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            java.nio.file.Files.createTempFile(prefix, suffix).toFile()
            } else {
            @Suppress("DEPRECATION")
            createTempFile(prefix, suffix)
            }
        {{/supportAndroidApiLevel25AndBelow}}
        tempFile.deleteOnExit()
        body.byteStream().use { inputStream ->
        tempFile.outputStream().use { tempFileOutputStream ->
        inputStream.copyTo(tempFileOutputStream)
        }
        }
        return tempFile as T
        }

        return when {
        mediaType == null || (mediaType.startsWith("application/") && mediaType.endsWith("json")) -> {
        val bodyContent = body.string()
        if (bodyContent.isEmpty()) {
        return null
        }
        {{#moshi}}Serializer.moshi.adapter<T>().fromJson(bodyContent){{/moshi}}{{!
                }}{{#gson}}Serializer.gson.fromJson(bodyContent, (object: TypeToken<T>(){}).getType()){{/gson}}{{!
                }}{{#jackson}}Serializer.jacksonObjectMapper.readValue(bodyContent, object: TypeReference<T>() {}){{/jackson}}{{!
                }}{{#kotlinx_serialization}}Serializer.kotlinxSerializationJson.decodeFromString<T>(bodyContent){{/kotlinx_serialization}}
        }
        mediaType == OCTET_MEDIA_TYPE -> body.bytes() as? T
        else ->  throw UnsupportedOperationException("responseBody currently only supports JSON body.")
        }
        }

        {{#hasAuthMethods}}
            protected fun <T> updateAuthParams(requestConfig: RequestConfig<T>) {
            {{#authMethods}}
                {{#isApiKey}}
                    {{#isKeyInHeader}}
                        if (requestConfig.headers["{{keyParamName}}"].isNullOrEmpty()) {
                    {{/isKeyInHeader}}
                    {{#isKeyInQuery}}
                        if (requestConfig.query["{{keyParamName}}"].isNullOrEmpty()) {
                    {{/isKeyInQuery}}
                    if (apiKey["{{keyParamName}}"] != null) {
                    if (apiKeyPrefix["{{keyParamName}}"] != null) {
                    {{#isKeyInHeader}}
                        requestConfig.headers["{{keyParamName}}"] = apiKeyPrefix["{{keyParamName}}"]!! + " " + apiKey["{{keyParamName}}"]!!
                    {{/isKeyInHeader}}
                    {{#isKeyInQuery}}
                        requestConfig.query["{{keyParamName}}"] = listOf(apiKeyPrefix["{{keyParamName}}"]!! + " " + apiKey["{{keyParamName}}"]!!)
                    {{/isKeyInQuery}}
                    } else {
                    {{#isKeyInHeader}}
                        requestConfig.headers["{{keyParamName}}"] = apiKey["{{keyParamName}}"]!!
                    {{/isKeyInHeader}}
                    {{#isKeyInQuery}}
                        requestConfig.query["{{keyParamName}}"] = listOf(apiKey["{{keyParamName}}"]!!)
                    {{/isKeyInQuery}}
                    }
                    }
                    {{#isKeyInQuery}}
                        }
                    {{/isKeyInQuery}}
                    {{#isKeyInHeader}}
                        }
                    {{/isKeyInHeader}}
                {{/isApiKey}}
                {{#isBasic}}
                    {{#isBasicBasic}}
                        if (requestConfig.headers[AUTHORIZATION].isNullOrEmpty()) {
                        username?.let { username ->
                        password?.let { password ->
                        requestConfig.headers[AUTHORIZATION] = okhttp3.Credentials.basic(username, password)
                        }
                        }
                        }
                    {{/isBasicBasic}}
                    {{#isBasicBearer}}
                        if (requestConfig.headers[AUTHORIZATION].isNullOrEmpty()) {
                        accessToken?.let { accessToken ->
                        requestConfig.headers[AUTHORIZATION] = "Bearer $accessToken"
                        }
                        }
                    {{/isBasicBearer}}
                {{/isBasic}}
                {{#isOAuth}}
                    if (requestConfig.headers[AUTHORIZATION].isNullOrEmpty()) {
                    accessToken?.let { accessToken ->
                    requestConfig.headers[AUTHORIZATION] = "Bearer $accessToken "
                    }
                    }
                {{/isOAuth}}
            {{/authMethods}}
            }
        {{/hasAuthMethods}}

        protected {{#useCoroutines}}suspend {{/useCoroutines}}inline fun <reified I, reified T: Any?> request(requestConfig: RequestConfig<I>): ApiResponse<T?> {
            val httpUrl = baseUrl.toHttpUrlOrNull() ?: throw IllegalStateException("baseUrl is invalid.")
            {{#hasAuthMethods}}

                // take authMethod from operation
                updateAuthParams(requestConfig)
            {{/hasAuthMethods}}

            val url = httpUrl.newBuilder()
            .addEncodedPathSegments(requestConfig.path.trimStart('/'))
            .apply {
            requestConfig.query.forEach { query ->
            query.value.forEach { queryValue ->
            addQueryParameter(query.key, queryValue)
            }
            }
            }.build()

            // take content-type/accept from spec or set to default (application/json) if not defined
            if (requestConfig.body != null && requestConfig.headers[CONTENT_TYPE].isNullOrEmpty()) {
            requestConfig.headers[CONTENT_TYPE] = JSON_MEDIA_TYPE
            }
            if (requestConfig.headers[ACCEPT].isNullOrEmpty()) {
            requestConfig.headers[ACCEPT] = JSON_MEDIA_TYPE
            }
            val headers = requestConfig.headers

            if (headers[ACCEPT].isNullOrEmpty()) {
            throw kotlin.IllegalStateException("Missing Accept header. This is required.")
            }

            val contentType = if (headers[CONTENT_TYPE] != null) {
            // TODO: support multiple contentType options here.
            (headers[CONTENT_TYPE] as String).substringBefore(";").lowercase(Locale.US)
            } else {
            null
            }

            val request = when (requestConfig.method) {
            RequestMethod.DELETE -> Request.Builder().url(url).delete(requestBody(requestConfig.body, contentType))
            RequestMethod.GET -> Request.Builder().url(url)
            RequestMethod.HEAD -> Request.Builder().url(url).head()
            RequestMethod.PATCH -> Request.Builder().url(url).patch(requestBody(requestConfig.body, contentType))
            RequestMethod.PUT -> Request.Builder().url(url).put(requestBody(requestConfig.body, contentType))
            RequestMethod.POST -> Request.Builder().url(url).post(requestBody(requestConfig.body, contentType))
            RequestMethod.OPTIONS -> Request.Builder().url(url).method("OPTIONS", null)
            }.apply {
            headers.forEach { header -> addHeader(header.key, header.value) }
            }.build()

            {{#useCoroutines}}
                val response: Response = suspendCancellableCoroutine { continuation ->
                val call = client.newCall(request)
                continuation.invokeOnCancellation { call.cancel() }
                call.enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                continuation.resumeWithException(e)
                }
                override fun onResponse(call: Call, response: Response) {
                continuation.resume(response)
                }
                })
                }
            {{/useCoroutines}}
            {{^useCoroutines}}
                val response = client.newCall(request).execute()
            {{/useCoroutines}}

            val accept = response.header(CONTENT_TYPE)?.substringBefore(";")?.lowercase(Locale.US)

            // TODO: handle specific mapping types. e.g. Map<int, Class<?>>
            @Suppress("UNNECESSARY_SAFE_CALL")
            return response.use {
            when {
            it.isRedirect -> Redirection(
            it.code,
            it.headers.toMultimap()
            )
            it.isInformational -> Informational(
            it.message,
            it.code,
            it.headers.toMultimap()
            )
            it.isSuccessful -> Success(
            responseBody(it, accept),
            it.code,
            it.headers.toMultimap()
            )
            it.isClientError -> ClientError(
            it.message,
            it.body?.string(),
            it.code,
            it.headers.toMultimap()
            )
            else -> ServerError(
            it.message,
            it.body?.string(),
            it.code,
            it.headers.toMultimap()
            )
            }
            }
            }

            protected fun parameterToString(value: Any?): String = when (value) {
            null -> ""
            is Array<*> -> toMultiValue(value, "csv").toString()
            is Iterable<*> -> toMultiValue(value, "csv").toString()
            is OffsetDateTime, is OffsetTime, is LocalDateTime, is LocalDate, is LocalTime ->
            parseDateToQueryString(value)
            else -> value.toString()
            }

            protected inline fun <reified I, reified T: Any?> requestAsync(requestConfig: RequestConfig<I>): Future<T> {
                val httpUrl = baseUrl.toHttpUrlOrNull() ?: throw IllegalStateException("baseUrl is invalid.")
                {{#hasAuthMethods}}

                // take authMethod from operation
                updateAuthParams(requestConfig)
                {{/hasAuthMethods}}

                val url = httpUrl.newBuilder()
                .addEncodedPathSegments(requestConfig.path.trimStart('/'))
                .apply {
                requestConfig.query.forEach { query ->
                query.value.forEach { queryValue ->
                addQueryParameter(query.key, queryValue)
                }
                }
                }.build()

                // take content-type/accept from spec or set to default (application/json) if not defined
                if (requestConfig.body != null && requestConfig.headers[CONTENT_TYPE].isNullOrEmpty()) {
                requestConfig.headers[CONTENT_TYPE] = JSON_MEDIA_TYPE
                }
                if (requestConfig.headers[ACCEPT].isNullOrEmpty()) {
                requestConfig.headers[ACCEPT] = JSON_MEDIA_TYPE
                }
                val headers = requestConfig.headers

                if (headers[ACCEPT].isNullOrEmpty()) {
                throw kotlin.IllegalStateException("Missing Accept header. This is required.")
                }

                val contentType = if (headers[CONTENT_TYPE] != null) {
                // TODO: support multiple contentType options here.
                (headers[CONTENT_TYPE] as String).substringBefore(";").lowercase(Locale.US)
                } else {
                null
                }

                val request = when (requestConfig.method) {
                RequestMethod.DELETE -> Request.Builder().url(url).delete(requestBody(requestConfig.body, contentType))
                RequestMethod.GET -> Request.Builder().url(url)
                RequestMethod.HEAD -> Request.Builder().url(url).head()
                RequestMethod.PATCH -> Request.Builder().url(url).patch(requestBody(requestConfig.body, contentType))
                RequestMethod.PUT -> Request.Builder().url(url).put(requestBody(requestConfig.body, contentType))
                RequestMethod.POST -> Request.Builder().url(url).post(requestBody(requestConfig.body, contentType))
                RequestMethod.OPTIONS -> Request.Builder().url(url).method("OPTIONS", null)
                }.apply {
                headers.forEach { header -> addHeader(header.key, header.value) }
                }.build()

                val future = CompletableFuture<T>()
                    client.newCall(request).enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                    future.completeExceptionally(e)
                    }

                    override fun onResponse(call: Call, response: Response) {
                        val accept = response.header(CONTENT_TYPE)?.substringBefore(";")?.lowercase(Locale.US)
                        if (!response.isSuccessful) {
                            if (response.code == 429) {
                                val retryAfter = response.headers[RETRY_AFTER]?.toLongOrNull()
                                val quotaKey = response.headers[QUOTA_KEY]
                                val limitReason = response.headers[LIMIT_REASON]
                                trackRateLimit(baseUrl, retryAfter, quotaKey)

                                future.completeExceptionally(RateLimitException(retryAfter, limitReason))
                            } else {
                                future.completeExceptionally(IOException("Unexpected response code: ${response.code}"))
                            }
                        } else {
                            future.complete(responseBody(response, accept))
                        }
                    }
                    })
                    return future
                    }


                    protected inline fun <reified T: Any> parseDateToQueryString(value : T): String {
                {{#toJson}}
                    /*
                    .replace("\"", "") converts the json object string to an actual string for the query parameter.
                    The moshi or gson adapter allows a more generic solution instead of trying to use a native
                    formatter. It also easily allows to provide a simple way to define a custom date format pattern
                    inside a gson/moshi adapter.
                    */
                    {{#moshi}}
                        return Serializer.moshi.adapter(T::class.java).toJson(value).replace("\"", "")
                    {{/moshi}}
                    {{#gson}}
                        return Serializer.gson.toJson(value, T::class.java).replace("\"", "")
                    {{/gson}}
                    {{#jackson}}
                        return Serializer.jacksonObjectMapper.writeValueAsString(value).replace("\"", "")
                    {{/jackson}}
                    {{#kotlinx_serialization}}
                        return Serializer.kotlinxSerializationJson.encodeToString(value).replace("\"", "")
                    {{/kotlinx_serialization}}
                {{/toJson}}
                {{^toJson}}
                    return value.toString()
                {{/toJson}}
                }
                }
