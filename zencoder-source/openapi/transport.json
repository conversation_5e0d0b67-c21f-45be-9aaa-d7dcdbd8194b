{"openapi": "3.1.0", "info": {"title": "transport-bus", "version": "0.1.0"}, "servers": [{"url": ""}], "paths": {}, "components": {"securitySchemes": {"OAuth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://example.com/oauth/authorize", "tokenUrl": "https://example.com/oauth/token", "scopes": {"read": "Grants read access", "write": "Grants write access"}}}}}, "schemas": {"transport-bus_RepoIndexingEvent": {"properties": {"status": {"$ref": "#/components/schemas/transport-bus_RepoIndexingStatus"}, "request_id": {"minLength": 1, "title": "Request Id", "type": "string"}, "repo_id": {"minLength": 1, "title": "Repo Id", "type": "string"}}, "required": ["status", "request_id", "repo_id"], "title": "RepoIndexingEvent", "type": "object"}, "transport-bus_RepoIndexingStatus": {"enum": ["queued", "started", "completed", "error"], "title": "RepoIndexingStatus", "type": "string"}}}}