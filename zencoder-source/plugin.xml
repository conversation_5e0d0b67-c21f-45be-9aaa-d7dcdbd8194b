<?xml version="1.0" encoding="UTF-8"?>
<idea-plugin>
    <id>ai.zencoder.plugin.noauth</id>
    <name>Zencoder No-Auth Extension</name>
    <version>1.0.0</version>
    <vendor email="<EMAIL>" url="https://your-website.com">Your Company</vendor>
    
    <description><![CDATA[
    <h1>Zencoder No-Auth Extension</h1>
    <p>This plugin extends Zencoder to work without authentication, suitable for development and testing environments.</p>
    
    <h2>Features</h2>
    <ul>
        <li>Bypasses OAuth authentication flow</li>
        <li>Provides mock user credentials</li>
        <li>Enables all Zencoder features without login</li>
        <li>Suitable for offline development</li>
    </ul>
    
    <h2>Usage</h2>
    <p>Install this plugin alongside Zencoder. The authentication will be automatically bypassed.</p>
    
    <h2>Warning</h2>
    <p>This plugin is intended for development and testing purposes only. Do not use in production environments.</p>
    ]]></description>
    
    <!-- 依赖Zencoder插件 -->
    <depends>ai.zencoder.plugin</depends>
    
    <!-- 兼容的IDE版本 -->
    <idea-version since-build="242.20224.91"/>
    
    <!-- 扩展点配置 -->
    <extensions defaultExtensionNs="com.intellij">
        <!-- 替换AuthService实现 -->
        <applicationService 
            serviceInterface="ai.zencoder.plugin.auth.AuthService" 
            serviceImplementation="ai.zencoder.plugin.auth.NoAuthService" 
            overrides="true"/>
            
        <!-- 可选：添加配置页面 -->
        <projectConfigurable 
            id="ai.zencoder.plugin.noauth.settings" 
            groupId="tools" 
            instance="ai.zencoder.plugin.noauth.NoAuthSettingsConfigurable" 
            displayName="Zencoder No-Auth" 
            nonDefaultProject="false"/>
    </extensions>
    
    <!-- 启动活动 -->
    <extensions defaultExtensionNs="com.intellij">
        <postStartupActivity implementation="ai.zencoder.plugin.noauth.NoAuthStartupActivity"/>
    </extensions>
    
    <!-- 操作定义 -->
    <actions>
        <action id="zencoder.noauth.toggle" 
                class="ai.zencoder.plugin.noauth.ToggleNoAuthAction" 
                text="Toggle No-Auth Mode" 
                description="Toggle Zencoder no-auth mode">
            <add-to-group group-id="ToolsMenu" anchor="last"/>
        </action>
    </actions>
</idea-plugin>
