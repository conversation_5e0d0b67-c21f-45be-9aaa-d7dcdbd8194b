# Bring zen to your code

Welcome to <PERSON><PERSON><PERSON>, companion that helps out with routine in your coding.
This is how Zencoder can help you:

- [Multi-line code completion](#multi-line-code-completion)
- [Code generation and repair](#code-generation-and-repair)
- [Code-aware Cha<PERSON> Assistant](#code-aware-chat-assistant)
- [Unit tests Generation](#unit-tests-generation)
- [Doc Comments](#doc-comments)

## Multi-line code completion

Finish your thoughts with real-time suggestions as you write the code

![](code-completion.svg)

- Start typing and watch for intelligent suggestions to appear. Press `Tab` to accept a completion

## Code generation and repair

Generate production-ready code with repair pipeline. Code Repair attempts to fix generated code by running various checks. It checks the code for errors and ensures it functions correctly.

![](code-gen.svg)

- Click on the floating hint or use `Cmd`/`Ctrl` + `I` to describe your desired code, then click `Generate`. You can also switch the repair pipeline on/off in addition to the code generation.

## Code-aware Chat Assistant

Ask questions, request explanations, or seek advice about your code

![](chat.svg)

- Chat is available on the left panel and lets you talk with an AI that sees your codebase and current file.

## Unit tests Generation

Write reliable code with meaningful tests and suggested scenarios that cover edge cases.

![](unit-tests.svg)

- Click the `Unit Tests` code lens above your function. This code lens in only visible in the supported languages

## Doc Comments

Bring clarity to your codebase through automated docstring generation

![](docstrings.svg)

- Click the `Doc Comment` code lens above your function. This code lens in only visible in the supported languages

Let's start coding hussle-free
